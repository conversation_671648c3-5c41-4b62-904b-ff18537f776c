package com.pulse.pulse.common.exception;

import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

import java.util.HashMap;
import java.util.Map;

import javax.validation.ConstraintViolationException;

/**
 * 全局异常处理器
 *
 * <p>统一处理系统中各种异常情况，将异常转换为规范的HTTP响应。 通过使用Spring的@ControllerAdvice注解，可以在全局范围内捕获所有控制器抛出的异常。
 * 针对不同类型的异常提供统一的处理逻辑，确保API返回格式一致，便于前端处理。
 *
 * <p>主要处理以下几类异常： 1. 文件上传相关异常 2. 请求参数缺失或验证失败异常 3. 业务逻辑异常 4. 其他未预期的系统异常
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理文件上传相关的异常
     *
     * <p>处理在文件上传过程中可能出现的异常情况，如文件过大、格式错误等。
     * 支持MultipartException和MissingServletRequestPartException两种文件上传异常类型。
     *
     * @param ex 文件上传过程中抛出的异常对象
     * @return 包含错误代码和错误信息的HTTP响应，状态码为400(BAD_REQUEST)
     */
    @ExceptionHandler({MultipartException.class, MissingServletRequestPartException.class})
    public ResponseEntity<Map<String, Object>> handleMultipartException(Exception ex) {
        log.error("文件上传异常: ", ex);
        Map<String, Object> response = new HashMap<>();
        response.put("code", ErrorCode.WRONG_PARAMETER);
        response.put("message", "文件上传失败: " + ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理请求参数缺失异常
     *
     * <p>当请求中缺少必要的请求参数时，Spring会抛出MissingServletRequestParameterException异常。
     * 本方法捕获该异常并返回友好的错误信息，包含缺失的参数名称。
     *
     * @param ex 参数缺失异常对象，包含缺失参数的详细信息
     * @return 包含错误代码和缺失参数信息的HTTP响应，状态码为400(BAD_REQUEST)
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<Map<String, Object>> handleMissingParameterException(
            MissingServletRequestParameterException ex) {
        log.error("请求参数缺失: ", ex);
        Map<String, Object> response = new HashMap<>();
        response.put("code", ErrorCode.WRONG_PARAMETER);
        response.put("message", "缺少必要参数: " + ex.getParameterName());
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理参数验证异常
     *
     * <p>处理Bean验证和方法参数验证失败的情况。当使用@Valid或@Validated进行参数验证， 且验证不通过时，会抛出相应的验证异常。本方法统一处理这些验证异常。
     *
     * <p>支持的异常类型： - MethodArgumentNotValidException: Bean验证失败异常 - ConstraintViolationException:
     * 方法参数约束违反异常
     *
     * @param ex 参数验证过程中抛出的异常对象
     * @return 包含错误代码和验证失败信息的HTTP响应，状态码为400(BAD_REQUEST)
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, ConstraintViolationException.class})
    public ResponseEntity<Map<String, Object>> handleValidationException(Exception ex) {
        log.error("参数验证异常: ", ex);
        Map<String, Object> response = new HashMap<>();
        response.put("code", ErrorCode.WRONG_PARAMETER);
        response.put("message", "参数验证失败: " + ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理业务异常
     *
     * <p>处理应用程序中自定义的业务逻辑异常。业务异常通常表示一个可预期的错误情况， 如权限不足、资源不存在等。这类异常包含特定的错误代码和可能的附加数据。
     *
     * <p>IgnoredException为系统中定义的业务异常基类，可以携带错误代码、错误消息和额外数据。
     *
     * @param ex 业务异常对象，包含错误代码、错误消息和可能的额外数据
     * @return 包含错误代码、错误信息和额外数据的HTTP响应，状态码为200(OK)
     */
    @ExceptionHandler(IgnoredException.class)
    public ResponseEntity<Map<String, Object>> handleBusinessException(IgnoredException ex) {
        log.error("业务异常: ", ex);
        Map<String, Object> response = new HashMap<>();
        response.put("code", ex.getErrorCode());
        response.put("message", ex.getMessage());
        response.put("data", ex.getData());
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * 处理其他未捕获的异常
     *
     * <p>作为最后的防线，捕获所有其他未明确处理的异常。这类异常通常是未预期的系统错误， 如数据库连接失败、外部服务调用异常等。对于这类异常，返回通用的错误信息。
     *
     * <p>本方法具有最低的优先级，只有在其他异常处理方法不匹配时才会执行。
     *
     * @param ex 未捕获的异常对象
     * @return 包含通用错误代码和错误信息的HTTP响应，状态码为500(INTERNAL_SERVER_ERROR)
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception ex) {
        log.error("系统异常: ", ex);
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("message", "系统异常: " + ex.getMessage());
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
