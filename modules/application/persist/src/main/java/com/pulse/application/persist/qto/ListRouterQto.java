package com.pulse.application.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "c74612d2-ba06-433b-917d-962c847ac0ca|QTO|DEFINITION")
public class ListRouterQto {
    /** 启用标识 router.enable_flag */
    @AutoGenerated(locked = true, uuid = "cdbae862-d2f0-49fa-be3a-3711a0183ce7")
    private Boolean enableFlagIs;

    @AutoGenerated(locked = true, uuid = "8492d45f-2a7b-400e-b523-************")
    private Integer from;

    /** 主键 router.id */
    @AutoGenerated(locked = true, uuid = "abda2c9f-4ada-46b9-b79f-9a10fa4de05d")
    private String searchLike;

    @AutoGenerated(locked = true, uuid = "beab5c3e-1fd7-4105-a7c0-f3aaab61f656")
    private Integer size;
}
