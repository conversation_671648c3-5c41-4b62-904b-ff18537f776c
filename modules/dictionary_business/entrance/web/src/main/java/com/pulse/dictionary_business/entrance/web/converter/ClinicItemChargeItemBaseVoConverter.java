package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemBaseVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemBaseVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ClinicItemChargeItemBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "96130879-4c5b-42a4-baa8-b59912351084|VO|CONVERTER")
public class ClinicItemChargeItemBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseVoDataAssembler clinicItemChargeItemBaseVoDataAssembler;

    /** 使用默认方式组装ClinicItemChargeItemBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "5685e88b-2970-37fd-ab45-adad69cc306c")
    public ClinicItemChargeItemBaseVo convertAndAssembleData(ClinicItemChargeItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ClinicItemChargeItemBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "9538b97b-c260-327a-81bd-45f8080e11b1")
    public List<ClinicItemChargeItemBaseVo> convertAndAssembleDataList(
            List<ClinicItemChargeItemBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, ClinicItemChargeItemBaseVo> voMap =
                convertToClinicItemChargeItemBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        clinicItemChargeItemBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ClinicItemChargeItemBaseDto转换成ClinicItemChargeItemBaseVo */
    @AutoGenerated(locked = false, uuid = "96130879-4c5b-42a4-baa8-b59912351084-converter-Map")
    public Map<ClinicItemChargeItemBaseDto, ClinicItemChargeItemBaseVo>
            convertToClinicItemChargeItemBaseVoMap(List<ClinicItemChargeItemBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<ClinicItemChargeItemBaseDto, ClinicItemChargeItemBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ClinicItemChargeItemBaseVo vo =
                                                    new ClinicItemChargeItemBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setClinicItemId(dto.getClinicItemId());
                                            vo.setChargeItemId(dto.getChargeItemId());
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setChargeItemCount(dto.getChargeItemCount());
                                            vo.setFilmFeeType(dto.getFilmFeeType());
                                            vo.setGraphicFeeFlag(dto.getGraphicFeeFlag());
                                            vo.setDigitalImagingFeeFlag(
                                                    dto.getDigitalImagingFeeFlag());
                                            vo.setUseScopeList(dto.getUseScopeList());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setFirstTimeBillingFlag(
                                                    dto.getFirstTimeBillingFlag());
                                            vo.setPerformDepartmentId(dto.getPerformDepartmentId());
                                            vo.setAllowModifyCountFlag(
                                                    dto.getAllowModifyCountFlag());
                                            vo.setClinicItemBillingType(
                                                    dto.getClinicItemBillingType());
                                            vo.setClinicInsuranceCode(dto.getClinicInsuranceCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ClinicItemChargeItemBaseDto转换成ClinicItemChargeItemBaseVo */
    @AutoGenerated(locked = true, uuid = "96130879-4c5b-42a4-baa8-b59912351084-converter-list")
    public List<ClinicItemChargeItemBaseVo> convertToClinicItemChargeItemBaseVoList(
            List<ClinicItemChargeItemBaseDto> dtoList) {
        return new ArrayList<>(convertToClinicItemChargeItemBaseVoMap(dtoList).values());
    }

    /** 把ClinicItemChargeItemBaseDto转换成ClinicItemChargeItemBaseVo */
    @AutoGenerated(locked = true, uuid = "eff3aafa-31c4-33cc-9c89-241df2ad5b3b")
    public ClinicItemChargeItemBaseVo convertToClinicItemChargeItemBaseVo(
            ClinicItemChargeItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToClinicItemChargeItemBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
