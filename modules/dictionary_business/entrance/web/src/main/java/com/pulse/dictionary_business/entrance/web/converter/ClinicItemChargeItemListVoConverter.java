package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemClinicItemDto;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemListVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemListVoDataAssembler.ClinicItemChargeItemListVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ClinicItemChargeItemListVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemListVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemListDto;
import com.pulse.dictionary_business.service.ClinicItemChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ClinicItemChargeItemListVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "8c374214-a333-4d7f-a0b4-7b34c128890c|VO|CONVERTER")
public class ClinicItemChargeItemListVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemClinicItemVoConverter chargeItemClinicItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoService clinicItemChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemListVoDataAssembler clinicItemChargeItemListVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemListVoDataCollector clinicItemChargeItemListVoDataCollector;

    /** 使用默认方式组装ClinicItemChargeItemListVo列表数据 */
    @AutoGenerated(locked = true, uuid = "07bb2aa1-9961-36e3-9474-6849b2c933c6")
    public List<ClinicItemChargeItemListVo> convertAndAssembleDataList(
            List<ClinicItemChargeItemListDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ClinicItemChargeItemListVoDataHolder dataHolder =
                new ClinicItemChargeItemListVoDataHolder();
        dataHolder.setRootBaseDtoList(
                clinicItemChargeItemBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ClinicItemChargeItemListDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ClinicItemChargeItemListVo> voMap =
                convertToClinicItemChargeItemListVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        clinicItemChargeItemListVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        clinicItemChargeItemListVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装ClinicItemChargeItemListVo数据 */
    @AutoGenerated(locked = true, uuid = "560a3304-7c43-34b2-8793-d0721bd6273b")
    public ClinicItemChargeItemListVo convertAndAssembleData(ClinicItemChargeItemListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ClinicItemChargeItemListDto转换成ClinicItemChargeItemListVo */
    @AutoGenerated(locked = true, uuid = "80448546-a3ca-38c0-b3fc-e20fb79b6966")
    public ClinicItemChargeItemListVo convertToClinicItemChargeItemListVo(
            ClinicItemChargeItemListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToClinicItemChargeItemListVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把ClinicItemChargeItemListDto转换成ClinicItemChargeItemListVo */
    @AutoGenerated(locked = false, uuid = "8c374214-a333-4d7f-a0b4-7b34c128890c-converter-Map")
    public Map<ClinicItemChargeItemListDto, ClinicItemChargeItemListVo>
            convertToClinicItemChargeItemListVoMap(List<ClinicItemChargeItemListDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ChargeItemClinicItemDto, ChargeItemClinicItemVo> chargeItemMap =
                chargeItemClinicItemVoConverter.convertToChargeItemClinicItemVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ClinicItemChargeItemListDto::getChargeItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ClinicItemChargeItemListDto, ClinicItemChargeItemListVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ClinicItemChargeItemListVo vo =
                                                    new ClinicItemChargeItemListVo();
                                            vo.setId(dto.getId());
                                            vo.setChargeItem(
                                                    dto.getChargeItem() == null
                                                            ? null
                                                            : chargeItemMap.get(
                                                                    dto.getChargeItem()));
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setChargeItemCount(dto.getChargeItemCount());
                                            vo.setFilmFeeType(dto.getFilmFeeType());
                                            vo.setGraphicFeeFlag(dto.getGraphicFeeFlag());
                                            vo.setDigitalImagingFeeFlag(
                                                    dto.getDigitalImagingFeeFlag());
                                            vo.setUseScopeList(dto.getUseScopeList());
                                            vo.setFirstTimeBillingFlag(
                                                    dto.getFirstTimeBillingFlag());
                                            vo.setPerformDepartmentId(dto.getPerformDepartmentId());
                                            vo.setAllowModifyCountFlag(
                                                    dto.getAllowModifyCountFlag());
                                            vo.setClinicItemBillingType(
                                                    dto.getClinicItemBillingType());
                                            vo.setClinicInsuranceCode(dto.getClinicInsuranceCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ClinicItemChargeItemListDto转换成ClinicItemChargeItemListVo */
    @AutoGenerated(locked = true, uuid = "8c374214-a333-4d7f-a0b4-7b34c128890c-converter-list")
    public List<ClinicItemChargeItemListVo> convertToClinicItemChargeItemListVoList(
            List<ClinicItemChargeItemListDto> dtoList) {
        return new ArrayList<>(convertToClinicItemChargeItemListVoMap(dtoList).values());
    }
}
