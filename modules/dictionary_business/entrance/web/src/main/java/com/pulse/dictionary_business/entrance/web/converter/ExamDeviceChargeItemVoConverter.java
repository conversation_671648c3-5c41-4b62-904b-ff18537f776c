package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamDeviceChargeItemVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamDeviceChargeItemVoDataAssembler.ExamDeviceChargeItemVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ExamDeviceChargeItemVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamDeviceChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDeviceVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemDto;
import com.pulse.dictionary_business.service.ExamDeviceChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamDeviceChargeItemVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "d3493aba-e6e9-4f40-af1a-e1436f0e0e24|VO|CONVERTER")
public class ExamDeviceChargeItemVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemVoConverter chargeItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemBaseDtoService examDeviceChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemVoDataAssembler examDeviceChargeItemVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemVoDataCollector examDeviceChargeItemVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDeviceVoConverter examTypeDeviceVoConverter;

    /** 使用默认方式组装ExamDeviceChargeItemVo数据 */
    @AutoGenerated(locked = true, uuid = "83f59249-4ea4-314b-842b-699443cab9ae")
    public ExamDeviceChargeItemVo convertAndAssembleData(ExamDeviceChargeItemDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamDeviceChargeItemVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b5147b62-0714-38f5-94bb-c50eec309e2e")
    public List<ExamDeviceChargeItemVo> convertAndAssembleDataList(
            List<ExamDeviceChargeItemDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ExamDeviceChargeItemVoDataHolder dataHolder = new ExamDeviceChargeItemVoDataHolder();
        dataHolder.setRootBaseDtoList(
                examDeviceChargeItemBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ExamDeviceChargeItemDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ExamDeviceChargeItemVo> voMap =
                convertToExamDeviceChargeItemVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examDeviceChargeItemVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        examDeviceChargeItemVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ExamDeviceChargeItemDto转换成ExamDeviceChargeItemVo */
    @AutoGenerated(locked = false, uuid = "d3493aba-e6e9-4f40-af1a-e1436f0e0e24-converter-Map")
    public Map<ExamDeviceChargeItemDto, ExamDeviceChargeItemVo> convertToExamDeviceChargeItemVoMap(
            List<ExamDeviceChargeItemDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ExamDeviceBaseDto, ExamTypeDeviceVo> examDeviceMap =
                examTypeDeviceVoConverter.convertToExamTypeDeviceVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamDeviceChargeItemDto::getExamDevice)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ChargeItemDto, ChargeItemVo> chargeItemMap =
                chargeItemVoConverter.convertToChargeItemVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamDeviceChargeItemDto::getChargeItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamDeviceChargeItemDto, ExamDeviceChargeItemVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamDeviceChargeItemVo vo =
                                                    new ExamDeviceChargeItemVo();
                                            vo.setId(dto.getId());
                                            vo.setExamDevice(
                                                    dto.getExamDevice() == null
                                                            ? null
                                                            : examDeviceMap.get(
                                                                    dto.getExamDevice()));
                                            vo.setChargeItem(
                                                    dto.getChargeItem() == null
                                                            ? null
                                                            : chargeItemMap.get(
                                                                    dto.getChargeItem()));
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setChargeItemCount(dto.getChargeItemCount());
                                            vo.setFilmFeeType(dto.getFilmFeeType());
                                            vo.setGraphicFeeFlag(dto.getGraphicFeeFlag());
                                            vo.setDigitalImagingFeeFlag(
                                                    dto.getDigitalImagingFeeFlag());
                                            vo.setUseScope(dto.getUseScopeList());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setEnhancedFlag(dto.getEnhancedFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamDeviceChargeItemDto转换成ExamDeviceChargeItemVo */
    @AutoGenerated(locked = true, uuid = "d3493aba-e6e9-4f40-af1a-e1436f0e0e24-converter-list")
    public List<ExamDeviceChargeItemVo> convertToExamDeviceChargeItemVoList(
            List<ExamDeviceChargeItemDto> dtoList) {
        return new ArrayList<>(convertToExamDeviceChargeItemVoMap(dtoList).values());
    }

    /** 把ExamDeviceChargeItemDto转换成ExamDeviceChargeItemVo */
    @AutoGenerated(locked = true, uuid = "e4074490-91d7-31de-81da-d91a4bc2c51b")
    public ExamDeviceChargeItemVo convertToExamDeviceChargeItemVo(ExamDeviceChargeItemDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamDeviceChargeItemVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
