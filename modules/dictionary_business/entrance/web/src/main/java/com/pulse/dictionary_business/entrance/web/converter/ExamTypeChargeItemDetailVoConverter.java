package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamTypeChargeItemDetailVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamTypeChargeItemDetailVoDataAssembler.ExamTypeChargeItemDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ExamTypeChargeItemDetailVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeChargeItemDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryVo;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemDetailDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ExamTypeChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamTypeChargeItemDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "08978a32-1ced-4c11-992d-161347f72f2a|VO|CONVERTER")
public class ExamTypeChargeItemDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemVoConverter chargeItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemBaseDtoService examTypeChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemDetailVoDataAssembler examTypeChargeItemDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemDetailVoDataCollector examTypeChargeItemDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryVoConverter examTypeDictionaryVoConverter;

    /** 把ExamTypeChargeItemDetailDto转换成ExamTypeChargeItemDetailVo */
    @AutoGenerated(locked = false, uuid = "08978a32-1ced-4c11-992d-161347f72f2a-converter-Map")
    public Map<ExamTypeChargeItemDetailDto, ExamTypeChargeItemDetailVo>
            convertToExamTypeChargeItemDetailVoMap(List<ExamTypeChargeItemDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ChargeItemDto, ChargeItemVo> chargeItemMap =
                chargeItemVoConverter.convertToChargeItemVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamTypeChargeItemDetailDto::getChargeItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo> examTypeMap =
                examTypeDictionaryVoConverter.convertToExamTypeDictionaryVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamTypeChargeItemDetailDto::getExamType)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamTypeChargeItemDetailDto, ExamTypeChargeItemDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamTypeChargeItemDetailVo vo =
                                                    new ExamTypeChargeItemDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setChargeItem(
                                                    dto.getChargeItem() == null
                                                            ? null
                                                            : chargeItemMap.get(
                                                                    dto.getChargeItem()));
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setChargeItemCount(dto.getChargeItemCount());
                                            vo.setFilmFeeType(dto.getFilmFeeType());
                                            vo.setGraphicFeeFlag(dto.getGraphicFeeFlag());
                                            vo.setDigitalImagingFeeFlag(
                                                    dto.getDigitalImagingFeeFlag());
                                            vo.setUseScope(dto.getUseScopeList());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setExamType(
                                                    dto.getExamType() == null
                                                            ? null
                                                            : examTypeMap.get(dto.getExamType()));
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setEnhancedFlag(dto.getEnhancedFlag());
                                            vo.setChargePartNumber(dto.getChargePartNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamTypeChargeItemDetailDto转换成ExamTypeChargeItemDetailVo */
    @AutoGenerated(locked = true, uuid = "08978a32-1ced-4c11-992d-161347f72f2a-converter-list")
    public List<ExamTypeChargeItemDetailVo> convertToExamTypeChargeItemDetailVoList(
            List<ExamTypeChargeItemDetailDto> dtoList) {
        return new ArrayList<>(convertToExamTypeChargeItemDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装ExamTypeChargeItemDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "32dcd2f1-23c2-34e1-8b6f-a71b7a769342")
    public ExamTypeChargeItemDetailVo convertAndAssembleData(ExamTypeChargeItemDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamTypeChargeItemDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "5841e824-7000-368a-a1f5-850f0cc64e16")
    public List<ExamTypeChargeItemDetailVo> convertAndAssembleDataList(
            List<ExamTypeChargeItemDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ExamTypeChargeItemDetailVoDataHolder dataHolder =
                new ExamTypeChargeItemDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                examTypeChargeItemBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ExamTypeChargeItemDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ExamTypeChargeItemDetailVo> voMap =
                convertToExamTypeChargeItemDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examTypeChargeItemDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        examTypeChargeItemDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ExamTypeChargeItemDetailDto转换成ExamTypeChargeItemDetailVo */
    @AutoGenerated(locked = true, uuid = "d659a478-6c7a-3a96-a541-66effae30acc")
    public ExamTypeChargeItemDetailVo convertToExamTypeChargeItemDetailVo(
            ExamTypeChargeItemDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamTypeChargeItemDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
