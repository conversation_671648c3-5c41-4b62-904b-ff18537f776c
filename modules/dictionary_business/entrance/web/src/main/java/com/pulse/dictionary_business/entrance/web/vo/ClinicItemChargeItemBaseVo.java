package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "96130879-4c5b-42a4-baa8-b59912351084|VO|DEFINITION")
public class ClinicItemChargeItemBaseVo {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "d88d2de0-ddfc-4899-bba5-081c2234ca07")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "b0888514-89f9-4ef2-b0e6-d725361921d0")
    private List<String> campusIdList;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "adb2fc9f-ffc4-4473-b86a-dbf1433a623d")
    private Long chargeItemCount;

    /** 收费项目ID */
    @AutoGenerated(locked = true, uuid = "9c387115-ab0d-4337-9218-179a565b672a")
    private String chargeItemId;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "e03dbeec-9e06-43df-b2d8-857eb5dbbf6a")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "d2326a8c-b8ed-460d-b923-78a5447a4143")
    private String clinicItemBillingType;

    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "cb5cbf88-4fa9-476f-a0dc-0328f1f08af3")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "81f24c7f-7d2a-46d7-93e2-ba0db9581ac9")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "2a8e8cc2-**************-d0b3a79c1748")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "374d2243-455c-4758-8090-91bed0c221b0")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "3fb444d6-d755-4b35-b713-b9da333b9812")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "eb77eee8-293c-4236-900a-50cdf16ef94d")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "a8a56f00-0f14-40d3-a7c3-78be77a564da")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "680e40ca-f401-473a-b916-67e668908118")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "0231279e-c7c0-4e9c-bc1c-6a35245d3053")
    private String performDepartmentId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ad16e279-59b1-4c4c-be1a-320327ee3dc4")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "ed5d7ae1-2af8-4ea7-94d8-e9e976b237c6")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "358b785b-a00a-4614-b907-30c62f0473c7")
    private List<String> useScopeList;
}
