package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9e122299-d6a1-4b24-b60f-5368dae3b742|VO|DEFINITION")
public class ClinicItemChargeItemChargeItemExtVo {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "300ed605-2742-4601-bdfe-b4fed863a3fa")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "2e07e839-109f-4a31-a74f-ac1d6d6c40bf")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "e8c20b16-c584-4f22-8333-ea53f5ea9655")
    private ChargeItemVo chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "ee452435-4124-4f64-b0f4-a6db94faeedd")
    private Long chargeItemCount;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "dec0ee63-7151-4a64-953c-59a6c4de9dc4")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "4c6d1b8b-2e6f-4c06-b9d0-cda29f3e6728")
    private String clinicItemBillingType;

    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "a3c7461f-9c60-4d8c-a486-8887e0b1867b")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "0a2a3afd-2a75-457a-a6cd-946573a9a967")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "cd6a92f8-1973-4c66-b5de-6eba6676ce88")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "a6560330-d095-4707-8eb9-d1440b8b1917")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "b4d1e2a8-7cdf-4d0f-bb52-0fd13e36ff3a")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "89cddd7d-273e-43a4-aeb9-f7d2b7617570")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "d78ed92b-65b6-4d05-b1da-f22c0d40db49")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "89faa9e9-73f5-4d40-96e3-77f58873cb52")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "6a2bf65a-2019-4bb7-9d70-93485aa0a83e")
    private String performDepartmentId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "163efe9c-006a-4c9d-b312-a1828c65db7c")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "43bea54e-9c3e-4bcd-8d35-a814067a3739")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2b410bf3-fec3-4c4b-997b-2649936d0052")
    private List<String> useScopeList;
}
