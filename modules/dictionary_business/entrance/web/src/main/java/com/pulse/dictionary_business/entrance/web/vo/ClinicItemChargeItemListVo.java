package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "8c374214-a333-4d7f-a0b4-7b34c128890c|VO|DEFINITION")
public class ClinicItemChargeItemListVo {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "d4f20ae4-af0d-4e35-8289-eae97c583614")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "f2313c53-e53c-4468-b727-71f2971fbe18")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "8b91b7df-0c5b-4972-8ade-9487efe473b0")
    private ChargeItemClinicItemVo chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "4e13a088-2240-43dd-9fed-af808166356c")
    private Long chargeItemCount;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "891c9c14-19b1-4cd8-a982-9709c8f72cbd")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "bb56227b-17a4-4f9d-ba56-7da7a64b8d10")
    private String clinicItemBillingType;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "1df24e61-38f7-4651-a608-f234eb1e6ad0")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "f3570bf1-6997-43c3-bafa-9d7d5bb74125")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "d7f436d7-9b98-4de9-95f4-05e1016a5b83")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "5e35d951-**************-16d3f2f2d51f")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "dd25722d-e6dd-4b8a-81f3-825e81ff6b00")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "1fdb9148-601e-4097-8b0a-c07761cb06d8")
    private String performDepartmentId;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7fd1e6d1-d766-417a-af62-4d54f6362551")
    private List<String> useScopeList;
}
