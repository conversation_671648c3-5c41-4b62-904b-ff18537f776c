package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "d3493aba-e6e9-4f40-af1a-e1436f0e0e24|VO|DEFINITION")
public class ExamDeviceChargeItemVo {
    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "d1edce1c-9004-482d-a502-3aa5e4befab2")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "07ca2b13-f3d0-4c8e-814b-3275e64d11e7")
    private ChargeItemVo chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "c6f68158-a187-4732-9efb-c67d16ec1362")
    private Long chargeItemCount;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "26f34627-4825-4510-afc8-cf1bf052a906")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "1df8ee63-f389-429c-b7b6-645f593614b9")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "d19f94a3-1fa5-49c2-adba-e6b80bce0428")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "ea265c7f-f5cc-4b53-9005-24f051d5deb6")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "14d25fb7-8dae-46f1-bdd3-010e5e01ae5d")
    private Boolean enhancedFlag;

    /** 检查设备id */
    @Valid
    @AutoGenerated(locked = true, uuid = "aa21e6b7-5392-474e-ac75-3082d3d5917b")
    private ExamTypeDeviceVo examDevice;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "3b40b9a7-5d0c-4233-9ad3-31e30703c909")
    private FilmFeeTypeEnum filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "8fab44c6-952c-440e-bb9a-f62770e95ed2")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ab7a1317-b464-4f70-97e8-0a9518fd4ca2")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b9f4c6db-d841-418d-afb5-ce0491186a78")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "edef3250-2092-4822-bdda-edcb872d870f")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "95caba53-c538-481f-99f6-26dea83fbd1d")
    private List<String> useScope;
}
