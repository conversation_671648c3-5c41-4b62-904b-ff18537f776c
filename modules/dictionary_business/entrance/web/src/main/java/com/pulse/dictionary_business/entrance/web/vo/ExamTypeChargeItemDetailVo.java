package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "08978a32-1ced-4c11-992d-161347f72f2a|VO|DEFINITION")
public class ExamTypeChargeItemDetailVo {
    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "e2deb1a8-3299-4c5a-b2a9-19f813719425")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "09cfbdd5-4eb1-4971-a0db-a159579801c9")
    private ChargeItemVo chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "b0438d40-8979-44a0-9889-32faf7e30f94")
    private Long chargeItemCount;

    /** 收费部位数量 */
    @AutoGenerated(locked = true, uuid = "3189cfbb-5e02-497d-9852-c77713ee16c7")
    private Long chargePartNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5a4f4f6f-3a5d-4a3b-ab38-59b0f4e0444c")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "ef7a0f24-e9ad-4d97-ad42-d714a34e59e7")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "4a3a7277-66d8-47ae-9d19-6ee41186a65f")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "dfa9b88d-1d60-4f0e-943b-d0e55a3a6b88")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "f88bf4e7-b632-4945-93e9-bf75da63b495")
    private Boolean enhancedFlag;

    /** 检查类型id */
    @Valid
    @AutoGenerated(locked = true, uuid = "af8591ba-a0c4-4d33-a31c-fc3ec0c8d52c")
    private ExamTypeDictionaryVo examType;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "f179fa13-4028-4df4-b487-13bfc923d1b0")
    private String filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "31d810ce-7e57-46e1-a780-d48429446603")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5cd49c37-a8ec-4c7a-90d8-510b7b5d767e")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "fea6a5ef-a1a6-4cec-843b-d0b4560712a4")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "88573ea9-6f22-418d-940f-bb95e5af4de7")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e0472cb3-43bc-426b-89bc-2c4923ec4421")
    private List<String> useScope;
}
