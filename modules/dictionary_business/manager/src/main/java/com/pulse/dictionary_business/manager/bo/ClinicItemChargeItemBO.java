package com.pulse.dictionary_business.manager.bo;

import com.pulse.dictionary_business.persist.dos.ClinicItemChargeItem;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.eo.StringListConverter;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@DynamicInsert
@Getter
@Setter
@Table(name = "clinic_item_charge_item")
@Entity
@AutoGenerated(locked = true, uuid = "f85d0833-6b18-49fe-8f64-2517fa3dfdf5|BO|DEFINITION")
public class ClinicItemChargeItemBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 允许修改计数标志 */
    @Column(name = "allow_modify_count_flag")
    @AutoGenerated(locked = true, uuid = "d7593e3b-9daa-4fda-bca1-7e33c6d5ad79")
    private Boolean allowModifyCountFlag;

    /** 院区ID列表 */
    @Column(name = "campus_id_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "37b3101a-de77-4fcb-afac-1ac338bea583")
    @Convert(converter = StringListConverter.class)
    private List<String> campusIdList;

    /** 收费项目数量 */
    @Column(name = "charge_item_count")
    @AutoGenerated(locked = true, uuid = "c86c5980-551a-438c-8bc0-83be64a3f417")
    private Long chargeItemCount;

    /** 收费项目ID */
    @Column(name = "charge_item_id")
    @AutoGenerated(locked = true, uuid = "097f39b8-57ca-474d-a532-57b412776202")
    private String chargeItemId;

    /** 诊疗医保代码 */
    @Column(name = "clinic_insurance_code")
    @AutoGenerated(locked = true, uuid = "ebb5706a-47fe-49e8-a193-450440b70cba")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @Column(name = "clinic_item_billing_type")
    @AutoGenerated(locked = true, uuid = "b2066a82-bd4b-41c0-82a5-1d625ffe2ad1")
    private String clinicItemBillingType;

    @ManyToOne
    @JoinColumn(name = "clinic_item_id", referencedColumnName = "clinic_item_id")
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO clinicItemDictionaryBO;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "e30d99a5-9524-461d-81df-74c5a89afc44")
    private Date createdAt;

    /** 创建者 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "5289341a-fd0f-430a-8698-a08ca724fb95")
    private String createdBy;

    /** 数字成像费用标志 */
    @Column(name = "digital_imaging_fee_flag")
    @AutoGenerated(locked = true, uuid = "e54e869c-8840-4c11-b9f0-e303cdff97ff")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @Column(name = "film_fee_type")
    @AutoGenerated(locked = true, uuid = "fe6da436-9c79-4899-8015-e6a04fc7af60")
    private String filmFeeType;

    /** 首次计费标志 */
    @Column(name = "first_time_billing_flag")
    @AutoGenerated(locked = true, uuid = "cb312a49-8f87-4c53-84d0-9b1f4505e0c9")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @Column(name = "graphic_fee_flag")
    @AutoGenerated(locked = true, uuid = "8ee8a3e2-d7f3-4eb2-92cc-ef5b4c7cc632")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "e8aa1448-3e8d-43cb-9641-c759b87b3848")
    @Id
    @NotNull(message = "主键不能为空")
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 执行科室id */
    @Column(name = "perform_department_id")
    @AutoGenerated(locked = true, uuid = "8d9b119f-38a2-4315-a917-7986695b2cf7")
    private String performDepartmentId;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "78fcef3d-8588-4a20-a7fd-043044fb297e")
    private Date updatedAt;

    /** 更新者 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "90505316-c9dd-4121-88b1-6b5f5d773363")
    private String updatedBy;

    /** 使用范围 */
    @Column(name = "use_scope_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "a2b34fa5-c7a7-4026-91c7-4e610672b660")
    @Convert(converter = StringListConverter.class)
    private List<String> useScopeList;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "6a44c84f-26cb-4fd7-bf08-f1e004e0b539|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public ClinicItemChargeItem convertToClinicItemChargeItem() {
        ClinicItemChargeItem entity = new ClinicItemChargeItem();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "chargeItemId",
                "campusIdList",
                "chargeItemCount",
                "filmFeeType",
                "graphicFeeFlag",
                "digitalImagingFeeFlag",
                "firstTimeBillingFlag",
                "performDepartmentId",
                "useScopeList",
                "allowModifyCountFlag",
                "clinicItemBillingType",
                "clinicInsuranceCode",
                "updatedBy",
                "createdBy",
                "createdAt",
                "updatedAt");
        ClinicItemDictionaryBO clinicItemDictionaryBO = this.getClinicItemDictionaryBO();
        entity.setClinicItemId(clinicItemDictionaryBO.getClinicItemId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Boolean getAllowModifyCountFlag() {
        return this.allowModifyCountFlag;
    }

    @AutoGenerated(locked = true)
    public List<String> getCampusIdList() {
        return this.campusIdList;
    }

    @AutoGenerated(locked = true)
    public Long getChargeItemCount() {
        return this.chargeItemCount;
    }

    @AutoGenerated(locked = true)
    public String getChargeItemId() {
        return this.chargeItemId;
    }

    @AutoGenerated(locked = true)
    public String getClinicInsuranceCode() {
        return this.clinicInsuranceCode;
    }

    @AutoGenerated(locked = true)
    public String getClinicItemBillingType() {
        return this.clinicItemBillingType;
    }

    @AutoGenerated(locked = true)
    public ClinicItemDictionaryBO getClinicItemDictionaryBO() {
        return this.clinicItemDictionaryBO;
    }

    @AutoGenerated(locked = true)
    public String getClinicItemId() {
        return this.getClinicItemDictionaryBO().getClinicItemId();
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getDigitalImagingFeeFlag() {
        return this.digitalImagingFeeFlag;
    }

    @AutoGenerated(locked = true)
    public String getFilmFeeType() {
        return this.filmFeeType;
    }

    @AutoGenerated(locked = true)
    public Boolean getFirstTimeBillingFlag() {
        return this.firstTimeBillingFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getGraphicFeeFlag() {
        return this.graphicFeeFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getPerformDepartmentId() {
        return this.performDepartmentId;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public List<String> getUseScopeList() {
        return this.useScopeList;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setAllowModifyCountFlag(Boolean allowModifyCountFlag) {
        this.allowModifyCountFlag = allowModifyCountFlag;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setCampusIdList(List<String> campusIdList) {
        this.campusIdList = campusIdList;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setChargeItemCount(Long chargeItemCount) {
        this.chargeItemCount = chargeItemCount;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setChargeItemId(String chargeItemId) {
        this.chargeItemId = chargeItemId;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setClinicInsuranceCode(String clinicInsuranceCode) {
        this.clinicInsuranceCode = clinicInsuranceCode;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setClinicItemBillingType(String clinicItemBillingType) {
        this.clinicItemBillingType = clinicItemBillingType;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setClinicItemDictionaryBO(
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        this.clinicItemDictionaryBO = clinicItemDictionaryBO;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
        this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setFilmFeeType(String filmFeeType) {
        this.filmFeeType = filmFeeType;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setFirstTimeBillingFlag(Boolean firstTimeBillingFlag) {
        this.firstTimeBillingFlag = firstTimeBillingFlag;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setGraphicFeeFlag(Boolean graphicFeeFlag) {
        this.graphicFeeFlag = graphicFeeFlag;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setId(String id) {
        this.id = id;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setPerformDepartmentId(String performDepartmentId) {
        this.performDepartmentId = performDepartmentId;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (ClinicItemChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBO setUseScopeList(List<String> useScopeList) {
        this.useScopeList = useScopeList;
        return (ClinicItemChargeItemBO) this;
    }
}
