package com.pulse.dictionary_business.manager.bo;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.pulse.dictionary_business.persist.dos.ExamDeviceChargeItem;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.eo.StringListConverter;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@DynamicInsert
@Getter
@Setter
@Table(name = "exam_device_charge_item")
@Entity
@AutoGenerated(locked = true, uuid = "2160d676-9dbd-4c0c-ad80-88f58ccc7095|BO|DEFINITION")
public class ExamDeviceChargeItemBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 院区ID列表 */
    @Column(name = "campus_id_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "706a61f1-95e2-4bdc-9ffd-a767ca7ffd00")
    @Convert(converter = StringListConverter.class)
    private List<String> campusIdList;

    /** 收费项目数量 */
    @Column(name = "charge_item_count")
    @AutoGenerated(locked = true, uuid = "c8036098-2208-41d6-9e10-9a1a230396ee")
    private Long chargeItemCount;

    /** 收费项目ID */
    @Column(name = "charge_item_id")
    @AutoGenerated(locked = true, uuid = "c8270664-4337-4e10-960a-42a1944a4ec1")
    private String chargeItemId;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "21cabfd5-1654-40bc-984c-4122c467f1b6")
    private Date createdAt;

    /** 创建者 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "d66abc06-483f-4f6d-a1d1-bcf95bcec595")
    private String createdBy;

    /** 数字成像费用标志 */
    @Column(name = "digital_imaging_fee_flag")
    @AutoGenerated(locked = true, uuid = "35cb5c69-996d-42cb-8c8e-c8e8823e2ea5")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @Column(name = "enable_flag")
    @AutoGenerated(locked = true, uuid = "a03d020e-a3fa-4abb-b4a7-83df7c7d3405")
    private Boolean enableFlag;

    /** 增强标志 */
    @Column(name = "enhanced_flag")
    @AutoGenerated(locked = true, uuid = "66381c7b-6e78-4d34-b997-9afc04848e33")
    private Boolean enhancedFlag;

    @ManyToOne
    @JoinColumn(name = "exam_device_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private ExamDeviceBO examDeviceBO;

    /** 胶片费类型 */
    @Column(name = "film_fee_type")
    @AutoGenerated(locked = true, uuid = "16476865-abbb-4e73-af95-53274db3648b")
    @Enumerated(EnumType.STRING)
    private FilmFeeTypeEnum filmFeeType;

    /** 图文费用标志 */
    @Column(name = "graphic_fee_flag")
    @AutoGenerated(locked = true, uuid = "ce46c03c-3ff5-4e0e-9aa3-856dda6eb216")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "7c1bf392-a208-4ade-afba-77c51396711b")
    @Id
    @NotNull(message = "主键不能为空")
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "0f082bb0-a100-4714-ba70-4eb11290e5ff")
    private Date updatedAt;

    /** 更新者 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "73b76eed-7109-4b8b-bf3b-0b8b106861f4")
    private String updatedBy;

    /** 使用范围 */
    @Column(name = "use_scope")
    @Valid
    @AutoGenerated(locked = true, uuid = "ffb00611-daa5-4bc8-b5e5-381b7b3296d8")
    @Convert(converter = StringListConverter.class)
    private List<String> useScopeList;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "3fb5792a-0c2a-4489-aa7f-b79554943ae3|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItem convertToExamDeviceChargeItem() {
        ExamDeviceChargeItem entity = new ExamDeviceChargeItem();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "chargeItemId",
                "campusIdList",
                "chargeItemCount",
                "filmFeeType",
                "graphicFeeFlag",
                "digitalImagingFeeFlag",
                "useScopeList",
                "enableFlag",
                "updatedBy",
                "createdBy",
                "enhancedFlag",
                "createdAt",
                "updatedAt");
        ExamDeviceBO examDeviceBO = this.getExamDeviceBO();
        entity.setExamDeviceId(examDeviceBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public List<String> getCampusIdList() {
        return this.campusIdList;
    }

    @AutoGenerated(locked = true)
    public Long getChargeItemCount() {
        return this.chargeItemCount;
    }

    @AutoGenerated(locked = true)
    public String getChargeItemId() {
        return this.chargeItemId;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getDigitalImagingFeeFlag() {
        return this.digitalImagingFeeFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnableFlag() {
        return this.enableFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnhancedFlag() {
        return this.enhancedFlag;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceBO getExamDeviceBO() {
        return this.examDeviceBO;
    }

    @AutoGenerated(locked = true)
    public String getExamDeviceId() {
        return this.getExamDeviceBO().getId();
    }

    @AutoGenerated(locked = true)
    public FilmFeeTypeEnum getFilmFeeType() {
        return this.filmFeeType;
    }

    @AutoGenerated(locked = true)
    public Boolean getGraphicFeeFlag() {
        return this.graphicFeeFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public List<String> getUseScopeList() {
        return this.useScopeList;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setCampusIdList(List<String> campusIdList) {
        this.campusIdList = campusIdList;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setChargeItemCount(Long chargeItemCount) {
        this.chargeItemCount = chargeItemCount;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setChargeItemId(String chargeItemId) {
        this.chargeItemId = chargeItemId;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
        this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setEnhancedFlag(Boolean enhancedFlag) {
        this.enhancedFlag = enhancedFlag;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setExamDeviceBO(ExamDeviceBO examDeviceBO) {
        this.examDeviceBO = examDeviceBO;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setFilmFeeType(FilmFeeTypeEnum filmFeeType) {
        this.filmFeeType = filmFeeType;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setGraphicFeeFlag(Boolean graphicFeeFlag) {
        this.graphicFeeFlag = graphicFeeFlag;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setId(String id) {
        this.id = id;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (ExamDeviceChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBO setUseScopeList(List<String> useScopeList) {
        this.useScopeList = useScopeList;
        return (ExamDeviceChargeItemBO) this;
    }
}
