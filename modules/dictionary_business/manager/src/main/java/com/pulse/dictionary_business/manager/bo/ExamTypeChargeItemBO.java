package com.pulse.dictionary_business.manager.bo;

import com.pulse.dictionary_business.persist.dos.ExamTypeChargeItem;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.eo.StringListConverter;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.Valid;

@DynamicInsert
@Getter
@Setter
@Table(name = "exam_type_charge_item")
@Entity
@AutoGenerated(locked = true, uuid = "dba6b71b-3e7f-4e5a-b1bd-d7fd4be83fdc|BO|DEFINITION")
public class ExamTypeChargeItemBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 院区ID列表 */
    @Column(name = "campus_id_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "2d41fab3-58c6-4604-a8d5-0321aa00e069")
    @Convert(converter = StringListConverter.class)
    private List<String> campusIdList;

    /** 收费项目数量 */
    @Column(name = "charge_item_count")
    @AutoGenerated(locked = true, uuid = "5ba65c28-097a-4bfc-ae03-f6c6b4b3a767")
    private Long chargeItemCount;

    /** 收费项目ID */
    @Column(name = "charge_item_id")
    @AutoGenerated(locked = true, uuid = "d6758c8a-edb1-4f13-b34c-78db32885382")
    private String chargeItemId;

    /** 收费部位数量 */
    @Column(name = "charge_part_number")
    @AutoGenerated(locked = true, uuid = "9153f94c-c29e-495b-ad14-feda75194173")
    private Long chargePartNumber;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "1f71e766-cceb-5d0e-a8c4-7f935fa93291")
    private Date createdAt;

    /** 创建者 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "457595e2-d930-4180-ba7a-93d42e694537")
    private String createdBy;

    /** 数字成像费用标志 */
    @Column(name = "digital_imaging_fee_flag")
    @AutoGenerated(locked = true, uuid = "dca52760-ac3c-4e99-8a92-66169efedc55")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @Column(name = "enable_flag")
    @AutoGenerated(locked = true, uuid = "3c51bdf1-8326-4a47-bbe8-4a3224375b3e")
    private Boolean enableFlag;

    /** 增强标志 */
    @Column(name = "enhanced_flag")
    @AutoGenerated(locked = true, uuid = "557b6791-df37-44f6-bd47-2cce192b19b7")
    private Boolean enhancedFlag;

    @ManyToOne
    @JoinColumn(name = "exam_type_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO examTypeDictionaryBO;

    /** 胶片费类型 */
    @Column(name = "film_fee_type")
    @AutoGenerated(locked = true, uuid = "0b3b7975-65d0-4de9-8a07-19e750702181")
    private String filmFeeType;

    /** 图文费用标志 */
    @Column(name = "graphic_fee_flag")
    @AutoGenerated(locked = true, uuid = "58cafedc-b663-4a3b-9d1e-5b3f28d60eb9")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "91fed477-b134-4368-b9d5-1f956a2d201f")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "a8c6ce94-9b52-5fcb-a804-d183a7155ba9")
    private Date updatedAt;

    /** 更新者 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "f8d18bad-2172-4bb1-9322-b4f8bd010280")
    private String updatedBy;

    /** 使用范围 */
    @Column(name = "use_scope")
    @Valid
    @AutoGenerated(locked = true, uuid = "d9ad596c-a81b-429f-ac1c-9281d015fbc6")
    @Convert(converter = StringListConverter.class)
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public ExamTypeChargeItem convertToExamTypeChargeItem() {
        ExamTypeChargeItem entity = new ExamTypeChargeItem();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "chargeItemId",
                "campusIdList",
                "chargeItemCount",
                "filmFeeType",
                "graphicFeeFlag",
                "digitalImagingFeeFlag",
                "useScopeList",
                "enableFlag",
                "chargePartNumber",
                "updatedBy",
                "createdBy",
                "enhancedFlag",
                "createdAt",
                "updatedAt");
        ExamTypeDictionaryBO examTypeDictionaryBO = this.getExamTypeDictionaryBO();
        entity.setExamTypeId(examTypeDictionaryBO.getId());
        return entity;
    }

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "d03ddffc-b385-4a22-b4dd-5137013ee2a9|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public List<String> getCampusIdList() {
        return this.campusIdList;
    }

    @AutoGenerated(locked = true)
    public Long getChargeItemCount() {
        return this.chargeItemCount;
    }

    @AutoGenerated(locked = true)
    public String getChargeItemId() {
        return this.chargeItemId;
    }

    @AutoGenerated(locked = true)
    public Long getChargePartNumber() {
        return this.chargePartNumber;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getDigitalImagingFeeFlag() {
        return this.digitalImagingFeeFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnableFlag() {
        return this.enableFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnhancedFlag() {
        return this.enhancedFlag;
    }

    @AutoGenerated(locked = true)
    public ExamTypeDictionaryBO getExamTypeDictionaryBO() {
        return this.examTypeDictionaryBO;
    }

    @AutoGenerated(locked = true)
    public String getExamTypeId() {
        return this.getExamTypeDictionaryBO().getId();
    }

    @AutoGenerated(locked = true)
    public String getFilmFeeType() {
        return this.filmFeeType;
    }

    @AutoGenerated(locked = true)
    public Boolean getGraphicFeeFlag() {
        return this.graphicFeeFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public List<String> getUseScopeList() {
        return this.useScopeList;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setCampusIdList(List<String> campusIdList) {
        this.campusIdList = campusIdList;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setChargeItemCount(Long chargeItemCount) {
        this.chargeItemCount = chargeItemCount;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setChargeItemId(String chargeItemId) {
        this.chargeItemId = chargeItemId;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setChargePartNumber(Long chargePartNumber) {
        this.chargePartNumber = chargePartNumber;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
        this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setEnhancedFlag(Boolean enhancedFlag) {
        this.enhancedFlag = enhancedFlag;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setExamTypeDictionaryBO(ExamTypeDictionaryBO examTypeDictionaryBO) {
        this.examTypeDictionaryBO = examTypeDictionaryBO;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setFilmFeeType(String filmFeeType) {
        this.filmFeeType = filmFeeType;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setGraphicFeeFlag(Boolean graphicFeeFlag) {
        this.graphicFeeFlag = graphicFeeFlag;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setId(String id) {
        this.id = id;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (ExamTypeChargeItemBO) this;
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBO setUseScopeList(List<String> useScopeList) {
        this.useScopeList = useScopeList;
        return (ExamTypeChargeItemBO) this;
    }
}
