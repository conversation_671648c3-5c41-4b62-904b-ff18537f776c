package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.persist.dos.ClinicItemChargeItem;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "63f5acad-245a-44f0-be86-f485f00c46b5|DTO|BASE_CONVERTER")
public class ClinicItemChargeItemBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBaseDto convertFromClinicItemChargeItemToClinicItemChargeItemBaseDto(
            ClinicItemChargeItem clinicItemChargeItem) {
        return convertFromClinicItemChargeItemToClinicItemChargeItemBaseDto(
                        List.of(clinicItemChargeItem))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ClinicItemChargeItemBaseDto>
            convertFromClinicItemChargeItemToClinicItemChargeItemBaseDto(
                    List<ClinicItemChargeItem> clinicItemChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicItemChargeItemList)) {
            return new ArrayList<>();
        }
        List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList = new ArrayList<>();
        for (ClinicItemChargeItem clinicItemChargeItem : clinicItemChargeItemList) {
            if (clinicItemChargeItem == null) {
                continue;
            }
            ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto =
                    new ClinicItemChargeItemBaseDto();
            clinicItemChargeItemBaseDto.setId(clinicItemChargeItem.getId());
            clinicItemChargeItemBaseDto.setClinicItemId(clinicItemChargeItem.getClinicItemId());
            clinicItemChargeItemBaseDto.setChargeItemId(clinicItemChargeItem.getChargeItemId());
            clinicItemChargeItemBaseDto.setCampusIdList(clinicItemChargeItem.getCampusIdList());
            clinicItemChargeItemBaseDto.setChargeItemCount(
                    clinicItemChargeItem.getChargeItemCount());
            clinicItemChargeItemBaseDto.setFilmFeeType(clinicItemChargeItem.getFilmFeeType());
            clinicItemChargeItemBaseDto.setGraphicFeeFlag(clinicItemChargeItem.getGraphicFeeFlag());
            clinicItemChargeItemBaseDto.setDigitalImagingFeeFlag(
                    clinicItemChargeItem.getDigitalImagingFeeFlag());
            clinicItemChargeItemBaseDto.setFirstTimeBillingFlag(
                    clinicItemChargeItem.getFirstTimeBillingFlag());
            clinicItemChargeItemBaseDto.setPerformDepartmentId(
                    clinicItemChargeItem.getPerformDepartmentId());
            clinicItemChargeItemBaseDto.setUseScopeList(clinicItemChargeItem.getUseScopeList());
            clinicItemChargeItemBaseDto.setAllowModifyCountFlag(
                    clinicItemChargeItem.getAllowModifyCountFlag());
            clinicItemChargeItemBaseDto.setClinicItemBillingType(
                    clinicItemChargeItem.getClinicItemBillingType());
            clinicItemChargeItemBaseDto.setClinicInsuranceCode(
                    clinicItemChargeItem.getClinicInsuranceCode());
            clinicItemChargeItemBaseDto.setUpdatedBy(clinicItemChargeItem.getUpdatedBy());
            clinicItemChargeItemBaseDto.setCreatedBy(clinicItemChargeItem.getCreatedBy());
            clinicItemChargeItemBaseDto.setCreatedAt(clinicItemChargeItem.getCreatedAt());
            clinicItemChargeItemBaseDto.setUpdatedAt(clinicItemChargeItem.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            clinicItemChargeItemBaseDtoList.add(clinicItemChargeItemBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return clinicItemChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
