package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemChargeItemExtDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "aea514f5-4684-49ca-88e4-ff676492f9be|DTO|BASE_CONVERTER")
public class ClinicItemChargeItemChargeItemExtDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoManager clinicItemChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemChargeItemExtDto
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemChargeItemExtDto(
                    ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto) {
        return convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemChargeItemExtDto(
                        List.of(clinicItemChargeItemBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ClinicItemChargeItemChargeItemExtDto>
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemChargeItemExtDto(
                    List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicItemChargeItemBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ClinicItemChargeItemChargeItemExtDto> clinicItemChargeItemChargeItemExtDtoList =
                new ArrayList<>();
        for (ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto :
                clinicItemChargeItemBaseDtoList) {
            if (clinicItemChargeItemBaseDto == null) {
                continue;
            }
            ClinicItemChargeItemChargeItemExtDto clinicItemChargeItemChargeItemExtDto =
                    new ClinicItemChargeItemChargeItemExtDto();
            clinicItemChargeItemChargeItemExtDto.setId(clinicItemChargeItemBaseDto.getId());
            clinicItemChargeItemChargeItemExtDto.setClinicItemId(
                    clinicItemChargeItemBaseDto.getClinicItemId());
            clinicItemChargeItemChargeItemExtDto.setCampusIdList(
                    clinicItemChargeItemBaseDto.getCampusIdList());
            clinicItemChargeItemChargeItemExtDto.setChargeItemCount(
                    clinicItemChargeItemBaseDto.getChargeItemCount());
            clinicItemChargeItemChargeItemExtDto.setFilmFeeType(
                    clinicItemChargeItemBaseDto.getFilmFeeType());
            clinicItemChargeItemChargeItemExtDto.setGraphicFeeFlag(
                    clinicItemChargeItemBaseDto.getGraphicFeeFlag());
            clinicItemChargeItemChargeItemExtDto.setDigitalImagingFeeFlag(
                    clinicItemChargeItemBaseDto.getDigitalImagingFeeFlag());
            clinicItemChargeItemChargeItemExtDto.setUseScopeList(
                    clinicItemChargeItemBaseDto.getUseScopeList());
            clinicItemChargeItemChargeItemExtDto.setUpdatedBy(
                    clinicItemChargeItemBaseDto.getUpdatedBy());
            clinicItemChargeItemChargeItemExtDto.setCreatedBy(
                    clinicItemChargeItemBaseDto.getCreatedBy());
            clinicItemChargeItemChargeItemExtDto.setCreatedAt(
                    clinicItemChargeItemBaseDto.getCreatedAt());
            clinicItemChargeItemChargeItemExtDto.setUpdatedAt(
                    clinicItemChargeItemBaseDto.getUpdatedAt());
            clinicItemChargeItemChargeItemExtDto.setFirstTimeBillingFlag(
                    clinicItemChargeItemBaseDto.getFirstTimeBillingFlag());
            clinicItemChargeItemChargeItemExtDto.setPerformDepartmentId(
                    clinicItemChargeItemBaseDto.getPerformDepartmentId());
            clinicItemChargeItemChargeItemExtDto.setAllowModifyCountFlag(
                    clinicItemChargeItemBaseDto.getAllowModifyCountFlag());
            clinicItemChargeItemChargeItemExtDto.setClinicItemBillingType(
                    clinicItemChargeItemBaseDto.getClinicItemBillingType());
            clinicItemChargeItemChargeItemExtDto.setClinicInsuranceCode(
                    clinicItemChargeItemBaseDto.getClinicInsuranceCode());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            clinicItemChargeItemChargeItemExtDtoList.add(clinicItemChargeItemChargeItemExtDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return clinicItemChargeItemChargeItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBaseDto
            convertFromClinicItemChargeItemChargeItemExtDtoToClinicItemChargeItemBaseDto(
                    ClinicItemChargeItemChargeItemExtDto clinicItemChargeItemChargeItemExtDto) {
        return convertFromClinicItemChargeItemChargeItemExtDtoToClinicItemChargeItemBaseDto(
                        List.of(clinicItemChargeItemChargeItemExtDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ClinicItemChargeItemBaseDto>
            convertFromClinicItemChargeItemChargeItemExtDtoToClinicItemChargeItemBaseDto(
                    List<ClinicItemChargeItemChargeItemExtDto>
                            clinicItemChargeItemChargeItemExtDtoList) {
        if (CollectionUtil.isEmpty(clinicItemChargeItemChargeItemExtDtoList)) {
            return new ArrayList<>();
        }
        return clinicItemChargeItemBaseDtoManager.getByIds(
                clinicItemChargeItemChargeItemExtDtoList.stream()
                        .map(ClinicItemChargeItemChargeItemExtDto::getId)
                        .collect(Collectors.toList()));
    }
}
