package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemListDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "0c4136e3-5347-44f1-a750-0da3c6d7c88f|DTO|BASE_CONVERTER")
public class ClinicItemChargeItemListDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoManager clinicItemChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemListDto
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemListDto(
                    ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto) {
        return convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemListDto(
                        List.of(clinicItemChargeItemBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ClinicItemChargeItemListDto>
            convertFromClinicItemChargeItemBaseDtoToClinicItemChargeItemListDto(
                    List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicItemChargeItemBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ClinicItemChargeItemListDto> clinicItemChargeItemListDtoList = new ArrayList<>();
        for (ClinicItemChargeItemBaseDto clinicItemChargeItemBaseDto :
                clinicItemChargeItemBaseDtoList) {
            if (clinicItemChargeItemBaseDto == null) {
                continue;
            }
            ClinicItemChargeItemListDto clinicItemChargeItemListDto =
                    new ClinicItemChargeItemListDto();
            clinicItemChargeItemListDto.setId(clinicItemChargeItemBaseDto.getId());
            clinicItemChargeItemListDto.setClinicItemId(
                    clinicItemChargeItemBaseDto.getClinicItemId());
            clinicItemChargeItemListDto.setCampusIdList(
                    clinicItemChargeItemBaseDto.getCampusIdList());
            clinicItemChargeItemListDto.setChargeItemCount(
                    clinicItemChargeItemBaseDto.getChargeItemCount());
            clinicItemChargeItemListDto.setFilmFeeType(
                    clinicItemChargeItemBaseDto.getFilmFeeType());
            clinicItemChargeItemListDto.setGraphicFeeFlag(
                    clinicItemChargeItemBaseDto.getGraphicFeeFlag());
            clinicItemChargeItemListDto.setDigitalImagingFeeFlag(
                    clinicItemChargeItemBaseDto.getDigitalImagingFeeFlag());
            clinicItemChargeItemListDto.setUseScopeList(
                    clinicItemChargeItemBaseDto.getUseScopeList());
            clinicItemChargeItemListDto.setUpdatedBy(clinicItemChargeItemBaseDto.getUpdatedBy());
            clinicItemChargeItemListDto.setCreatedBy(clinicItemChargeItemBaseDto.getCreatedBy());
            clinicItemChargeItemListDto.setCreatedAt(clinicItemChargeItemBaseDto.getCreatedAt());
            clinicItemChargeItemListDto.setUpdatedAt(clinicItemChargeItemBaseDto.getUpdatedAt());
            clinicItemChargeItemListDto.setFirstTimeBillingFlag(
                    clinicItemChargeItemBaseDto.getFirstTimeBillingFlag());
            clinicItemChargeItemListDto.setPerformDepartmentId(
                    clinicItemChargeItemBaseDto.getPerformDepartmentId());
            clinicItemChargeItemListDto.setAllowModifyCountFlag(
                    clinicItemChargeItemBaseDto.getAllowModifyCountFlag());
            clinicItemChargeItemListDto.setClinicItemBillingType(
                    clinicItemChargeItemBaseDto.getClinicItemBillingType());
            clinicItemChargeItemListDto.setClinicInsuranceCode(
                    clinicItemChargeItemBaseDto.getClinicInsuranceCode());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            clinicItemChargeItemListDtoList.add(clinicItemChargeItemListDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return clinicItemChargeItemListDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ClinicItemChargeItemBaseDto
            convertFromClinicItemChargeItemListDtoToClinicItemChargeItemBaseDto(
                    ClinicItemChargeItemListDto clinicItemChargeItemListDto) {
        return convertFromClinicItemChargeItemListDtoToClinicItemChargeItemBaseDto(
                        List.of(clinicItemChargeItemListDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ClinicItemChargeItemBaseDto>
            convertFromClinicItemChargeItemListDtoToClinicItemChargeItemBaseDto(
                    List<ClinicItemChargeItemListDto> clinicItemChargeItemListDtoList) {
        if (CollectionUtil.isEmpty(clinicItemChargeItemListDtoList)) {
            return new ArrayList<>();
        }
        return clinicItemChargeItemBaseDtoManager.getByIds(
                clinicItemChargeItemListDtoList.stream()
                        .map(ClinicItemChargeItemListDto::getId)
                        .collect(Collectors.toList()));
    }
}
