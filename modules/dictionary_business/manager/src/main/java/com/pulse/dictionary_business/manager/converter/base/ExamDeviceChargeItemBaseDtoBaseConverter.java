package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamDeviceChargeItem;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "0d3601d9-d9e8-4073-af38-308a9546f750|DTO|BASE_CONVERTER")
public class ExamDeviceChargeItemBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBaseDto convertFromExamDeviceChargeItemToExamDeviceChargeItemBaseDto(
            ExamDeviceChargeItem examDeviceChargeItem) {
        return convertFromExamDeviceChargeItemToExamDeviceChargeItemBaseDto(
                        List.of(examDeviceChargeItem))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamDeviceChargeItemBaseDto>
            convertFromExamDeviceChargeItemToExamDeviceChargeItemBaseDto(
                    List<ExamDeviceChargeItem> examDeviceChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examDeviceChargeItemList)) {
            return new ArrayList<>();
        }
        List<ExamDeviceChargeItemBaseDto> examDeviceChargeItemBaseDtoList = new ArrayList<>();
        for (ExamDeviceChargeItem examDeviceChargeItem : examDeviceChargeItemList) {
            if (examDeviceChargeItem == null) {
                continue;
            }
            ExamDeviceChargeItemBaseDto examDeviceChargeItemBaseDto =
                    new ExamDeviceChargeItemBaseDto();
            examDeviceChargeItemBaseDto.setId(examDeviceChargeItem.getId());
            examDeviceChargeItemBaseDto.setExamDeviceId(examDeviceChargeItem.getExamDeviceId());
            examDeviceChargeItemBaseDto.setChargeItemId(examDeviceChargeItem.getChargeItemId());
            examDeviceChargeItemBaseDto.setCampusIdList(examDeviceChargeItem.getCampusIdList());
            examDeviceChargeItemBaseDto.setChargeItemCount(
                    examDeviceChargeItem.getChargeItemCount());
            examDeviceChargeItemBaseDto.setFilmFeeType(examDeviceChargeItem.getFilmFeeType());
            examDeviceChargeItemBaseDto.setGraphicFeeFlag(examDeviceChargeItem.getGraphicFeeFlag());
            examDeviceChargeItemBaseDto.setDigitalImagingFeeFlag(
                    examDeviceChargeItem.getDigitalImagingFeeFlag());
            examDeviceChargeItemBaseDto.setUseScopeList(examDeviceChargeItem.getUseScopeList());
            examDeviceChargeItemBaseDto.setEnableFlag(examDeviceChargeItem.getEnableFlag());
            examDeviceChargeItemBaseDto.setUpdatedBy(examDeviceChargeItem.getUpdatedBy());
            examDeviceChargeItemBaseDto.setCreatedBy(examDeviceChargeItem.getCreatedBy());
            examDeviceChargeItemBaseDto.setEnhancedFlag(examDeviceChargeItem.getEnhancedFlag());
            examDeviceChargeItemBaseDto.setCreatedAt(examDeviceChargeItem.getCreatedAt());
            examDeviceChargeItemBaseDto.setUpdatedAt(examDeviceChargeItem.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examDeviceChargeItemBaseDtoList.add(examDeviceChargeItemBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examDeviceChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
