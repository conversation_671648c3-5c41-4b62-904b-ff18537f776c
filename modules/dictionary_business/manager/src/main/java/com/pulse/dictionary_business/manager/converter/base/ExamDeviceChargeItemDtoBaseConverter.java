package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ExamDeviceChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "d09aae3d-4d0c-4865-a77d-576e42ed7822|DTO|BASE_CONVERTER")
public class ExamDeviceChargeItemDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemBaseDtoManager examDeviceChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemDto convertFromExamDeviceChargeItemBaseDtoToExamDeviceChargeItemDto(
            ExamDeviceChargeItemBaseDto examDeviceChargeItemBaseDto) {
        return convertFromExamDeviceChargeItemBaseDtoToExamDeviceChargeItemDto(
                        List.of(examDeviceChargeItemBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ExamDeviceChargeItemDto>
            convertFromExamDeviceChargeItemBaseDtoToExamDeviceChargeItemDto(
                    List<ExamDeviceChargeItemBaseDto> examDeviceChargeItemBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examDeviceChargeItemBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ExamDeviceChargeItemDto> examDeviceChargeItemDtoList = new ArrayList<>();
        for (ExamDeviceChargeItemBaseDto examDeviceChargeItemBaseDto :
                examDeviceChargeItemBaseDtoList) {
            if (examDeviceChargeItemBaseDto == null) {
                continue;
            }
            ExamDeviceChargeItemDto examDeviceChargeItemDto = new ExamDeviceChargeItemDto();
            examDeviceChargeItemDto.setId(examDeviceChargeItemBaseDto.getId());
            examDeviceChargeItemDto.setCampusIdList(examDeviceChargeItemBaseDto.getCampusIdList());
            examDeviceChargeItemDto.setChargeItemCount(
                    examDeviceChargeItemBaseDto.getChargeItemCount());
            examDeviceChargeItemDto.setFilmFeeType(examDeviceChargeItemBaseDto.getFilmFeeType());
            examDeviceChargeItemDto.setGraphicFeeFlag(
                    examDeviceChargeItemBaseDto.getGraphicFeeFlag());
            examDeviceChargeItemDto.setDigitalImagingFeeFlag(
                    examDeviceChargeItemBaseDto.getDigitalImagingFeeFlag());
            examDeviceChargeItemDto.setUseScopeList(examDeviceChargeItemBaseDto.getUseScopeList());
            examDeviceChargeItemDto.setUpdatedBy(examDeviceChargeItemBaseDto.getUpdatedBy());
            examDeviceChargeItemDto.setCreatedBy(examDeviceChargeItemBaseDto.getCreatedBy());
            examDeviceChargeItemDto.setCreatedAt(examDeviceChargeItemBaseDto.getCreatedAt());
            examDeviceChargeItemDto.setUpdatedAt(examDeviceChargeItemBaseDto.getUpdatedAt());
            examDeviceChargeItemDto.setEnableFlag(examDeviceChargeItemBaseDto.getEnableFlag());
            examDeviceChargeItemDto.setEnhancedFlag(examDeviceChargeItemBaseDto.getEnhancedFlag());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examDeviceChargeItemDtoList.add(examDeviceChargeItemDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examDeviceChargeItemDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ExamDeviceChargeItemBaseDto
            convertFromExamDeviceChargeItemDtoToExamDeviceChargeItemBaseDto(
                    ExamDeviceChargeItemDto examDeviceChargeItemDto) {
        return convertFromExamDeviceChargeItemDtoToExamDeviceChargeItemBaseDto(
                        List.of(examDeviceChargeItemDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamDeviceChargeItemBaseDto>
            convertFromExamDeviceChargeItemDtoToExamDeviceChargeItemBaseDto(
                    List<ExamDeviceChargeItemDto> examDeviceChargeItemDtoList) {
        if (CollectionUtil.isEmpty(examDeviceChargeItemDtoList)) {
            return new ArrayList<>();
        }
        return examDeviceChargeItemBaseDtoManager.getByIds(
                examDeviceChargeItemDtoList.stream()
                        .map(ExamDeviceChargeItemDto::getId)
                        .collect(Collectors.toList()));
    }
}
