package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamTypeChargeItem;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "d489e16a-eded-48e3-9302-864c7505b033|DTO|BASE_CONVERTER")
public class ExamTypeChargeItemBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBaseDto convertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(
            ExamTypeChargeItem examTypeChargeItem) {
        return convertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(List.of(examTypeChargeItem))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamTypeChargeItemBaseDto> convertFromExamTypeChargeItemToExamTypeChargeItemBaseDto(
            List<ExamTypeChargeItem> examTypeChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examTypeChargeItemList)) {
            return new ArrayList<>();
        }
        List<ExamTypeChargeItemBaseDto> examTypeChargeItemBaseDtoList = new ArrayList<>();
        for (ExamTypeChargeItem examTypeChargeItem : examTypeChargeItemList) {
            if (examTypeChargeItem == null) {
                continue;
            }
            ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto = new ExamTypeChargeItemBaseDto();
            examTypeChargeItemBaseDto.setId(examTypeChargeItem.getId());
            examTypeChargeItemBaseDto.setExamTypeId(examTypeChargeItem.getExamTypeId());
            examTypeChargeItemBaseDto.setChargeItemId(examTypeChargeItem.getChargeItemId());
            examTypeChargeItemBaseDto.setCampusIdList(examTypeChargeItem.getCampusIdList());
            examTypeChargeItemBaseDto.setChargeItemCount(examTypeChargeItem.getChargeItemCount());
            examTypeChargeItemBaseDto.setFilmFeeType(examTypeChargeItem.getFilmFeeType());
            examTypeChargeItemBaseDto.setGraphicFeeFlag(examTypeChargeItem.getGraphicFeeFlag());
            examTypeChargeItemBaseDto.setDigitalImagingFeeFlag(
                    examTypeChargeItem.getDigitalImagingFeeFlag());
            examTypeChargeItemBaseDto.setUseScopeList(examTypeChargeItem.getUseScopeList());
            examTypeChargeItemBaseDto.setEnableFlag(examTypeChargeItem.getEnableFlag());
            examTypeChargeItemBaseDto.setChargePartNumber(examTypeChargeItem.getChargePartNumber());
            examTypeChargeItemBaseDto.setUpdatedBy(examTypeChargeItem.getUpdatedBy());
            examTypeChargeItemBaseDto.setCreatedBy(examTypeChargeItem.getCreatedBy());
            examTypeChargeItemBaseDto.setEnhancedFlag(examTypeChargeItem.getEnhancedFlag());
            examTypeChargeItemBaseDto.setCreatedAt(examTypeChargeItem.getCreatedAt());
            examTypeChargeItemBaseDto.setUpdatedAt(examTypeChargeItem.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examTypeChargeItemBaseDtoList.add(examTypeChargeItemBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examTypeChargeItemBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
