package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ExamTypeChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemDetailDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "1368c912-b649-41f5-adb3-2500f5152931|DTO|BASE_CONVERTER")
public class ExamTypeChargeItemDetailDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemBaseDtoManager examTypeChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemDetailDto
            convertFromExamTypeChargeItemBaseDtoToExamTypeChargeItemDetailDto(
                    ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto) {
        return convertFromExamTypeChargeItemBaseDtoToExamTypeChargeItemDetailDto(
                        List.of(examTypeChargeItemBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ExamTypeChargeItemDetailDto>
            convertFromExamTypeChargeItemBaseDtoToExamTypeChargeItemDetailDto(
                    List<ExamTypeChargeItemBaseDto> examTypeChargeItemBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examTypeChargeItemBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ExamTypeChargeItemDetailDto> examTypeChargeItemDetailDtoList = new ArrayList<>();
        for (ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto : examTypeChargeItemBaseDtoList) {
            if (examTypeChargeItemBaseDto == null) {
                continue;
            }
            ExamTypeChargeItemDetailDto examTypeChargeItemDetailDto =
                    new ExamTypeChargeItemDetailDto();
            examTypeChargeItemDetailDto.setId(examTypeChargeItemBaseDto.getId());
            examTypeChargeItemDetailDto.setCampusIdList(
                    examTypeChargeItemBaseDto.getCampusIdList());
            examTypeChargeItemDetailDto.setChargeItemCount(
                    examTypeChargeItemBaseDto.getChargeItemCount());
            examTypeChargeItemDetailDto.setFilmFeeType(examTypeChargeItemBaseDto.getFilmFeeType());
            examTypeChargeItemDetailDto.setGraphicFeeFlag(
                    examTypeChargeItemBaseDto.getGraphicFeeFlag());
            examTypeChargeItemDetailDto.setDigitalImagingFeeFlag(
                    examTypeChargeItemBaseDto.getDigitalImagingFeeFlag());
            examTypeChargeItemDetailDto.setUseScopeList(
                    examTypeChargeItemBaseDto.getUseScopeList());
            examTypeChargeItemDetailDto.setUpdatedBy(examTypeChargeItemBaseDto.getUpdatedBy());
            examTypeChargeItemDetailDto.setCreatedBy(examTypeChargeItemBaseDto.getCreatedBy());
            examTypeChargeItemDetailDto.setCreatedAt(examTypeChargeItemBaseDto.getCreatedAt());
            examTypeChargeItemDetailDto.setUpdatedAt(examTypeChargeItemBaseDto.getUpdatedAt());
            examTypeChargeItemDetailDto.setEnableFlag(examTypeChargeItemBaseDto.getEnableFlag());
            examTypeChargeItemDetailDto.setEnhancedFlag(
                    examTypeChargeItemBaseDto.getEnhancedFlag());
            examTypeChargeItemDetailDto.setChargePartNumber(
                    examTypeChargeItemBaseDto.getChargePartNumber());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examTypeChargeItemDetailDtoList.add(examTypeChargeItemDetailDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examTypeChargeItemDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ExamTypeChargeItemBaseDto
            convertFromExamTypeChargeItemDetailDtoToExamTypeChargeItemBaseDto(
                    ExamTypeChargeItemDetailDto examTypeChargeItemDetailDto) {
        return convertFromExamTypeChargeItemDetailDtoToExamTypeChargeItemBaseDto(
                        List.of(examTypeChargeItemDetailDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamTypeChargeItemBaseDto>
            convertFromExamTypeChargeItemDetailDtoToExamTypeChargeItemBaseDto(
                    List<ExamTypeChargeItemDetailDto> examTypeChargeItemDetailDtoList) {
        if (CollectionUtil.isEmpty(examTypeChargeItemDetailDtoList)) {
            return new ArrayList<>();
        }
        return examTypeChargeItemBaseDtoManager.getByIds(
                examTypeChargeItemDetailDtoList.stream()
                        .map(ExamTypeChargeItemDetailDto::getId)
                        .collect(Collectors.toList()));
    }
}
