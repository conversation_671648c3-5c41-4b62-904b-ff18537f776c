package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "63f5acad-245a-44f0-be86-f485f00c46b5|DTO|DEFINITION")
public class ClinicItemChargeItemBaseDto {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "4d89e0ef-49a5-4451-ba86-5919e97f510a")
    private Boolean allowModifyCountFlag;

    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d70281c8-81fd-45c3-9a1a-328db243ec40")
    private List<String> campusIdList;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "83e32e25-7c3a-4d43-a995-2df3f9de5a68")
    private Long chargeItemCount;

    /** 收费项目ID */
    @AutoGenerated(locked = true, uuid = "eb2ec276-5173-4748-8e82-db76cdb80730")
    private String chargeItemId;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "59777b51-cdd5-44f0-90fe-e7ce8de74fa5")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "70c18abc-7e1c-40a0-abd4-c518634729b6")
    private String clinicItemBillingType;

    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "9bd90e3e-4380-4beb-b216-3eeefb5d6d5a")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "11055253-9d4c-4d62-bf71-68fbb1f80553")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "a2c5b437-a743-4af7-bda8-847daa0af287")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "7dc9aa15-5023-40ec-bc12-5e2808caf15e")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "04f26ec2-3de0-4404-9b15-e5383bd99473")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "8c949bee-d7f4-47dd-894b-ca1d51aee17b")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "e08443ae-2677-45c2-b0c9-12a40f2d227a")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "0b166acc-264e-4a1f-ab8c-02d42887c585")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "0f5c488e-7719-4a33-93b7-212e46c65cc8")
    private String performDepartmentId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "aa8d8dbe-a952-4e2d-89db-4de8a05feb18")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "e60ff7ec-3064-41ef-b709-0a57fdaf7206")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e219bf62-2001-43e5-adbb-9f32b76382f3")
    private List<String> useScopeList;
}
