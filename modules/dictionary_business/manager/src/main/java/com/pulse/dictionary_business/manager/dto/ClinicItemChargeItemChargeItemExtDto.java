package com.pulse.dictionary_business.manager.dto;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "aea514f5-4684-49ca-88e4-ff676492f9be|DTO|DEFINITION")
public class ClinicItemChargeItemChargeItemExtDto {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "41e6a1c8-1d65-4746-9203-7e6c32231a8f")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "a8a2fc5a-da96-4e67-9e25-5deeeade903f")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "662335dd-9472-46d0-bc0f-3776c0c31270")
    private ChargeItemDto chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "d388b8a5-06b7-4820-9ced-a3f901a7c3b5")
    private Long chargeItemCount;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "b8490ede-20d0-42be-a32b-7631365d0751")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "8bc06aa0-8485-482a-aa9c-aa74a037fa4b")
    private String clinicItemBillingType;

    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "79ff1ef6-c0c7-488a-b871-b90e664332f8")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "78c664aa-8432-4e52-a544-5bb6331c6256")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "fff54d67-ed88-46be-9783-8daf5f915b87")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "88b46e9a-2e07-42ad-a87e-d5b1608aed84")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "ea518a4a-f483-44ec-b797-d6e6741cc8c8")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "62edad75-c7f4-421c-91ee-843b99a19ba2")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "984ecba5-b043-41e3-8a71-07c545d72d0b")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c3516d71-1d47-4a5b-960a-3fd069fede64")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "a242df54-aa9f-487b-8f7c-3a6706fd1237")
    private String performDepartmentId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b0f7e892-6513-4bc5-8ef5-0260d5832b18")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "a5660530-d480-482f-b1b3-3334681a6008")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7ad7e352-8e5d-4cdc-b2c3-8e49a2bbf60d")
    private List<String> useScopeList;
}
