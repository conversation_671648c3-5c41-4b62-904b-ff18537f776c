package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "b048b5df-0362-4fda-be8a-fa9bee3b89fc|DTO|DEFINITION")
public class ClinicItemChargeItemClinicItemExtDto {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "dcebf314-58f6-463c-a7a1-2f134a64fc58")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "f78583ce-e6dd-4b76-894f-75fc5ae071da")
    private List<String> campusIdList;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "e1ebbf44-8be9-4084-a750-ed8aa54f0851")
    private Long chargeItemCount;

    /** 收费项目ID */
    @AutoGenerated(locked = true, uuid = "128532dd-82af-4583-95ff-d9c1a4c1f411")
    private String chargeItemId;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "e4a4d6b4-23f4-40f0-af1e-b5d01715eeb9")
    private String clinicInsuranceCode;

    /** 诊疗项目id */
    @Valid
    @AutoGenerated(locked = true, uuid = "b1f5d963-9823-4b09-8674-401ce7694a5a")
    private ClinicItemDictionaryBaseDto clinicItem;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "29059216-4ffb-44c0-953b-4f67ea33d992")
    private String clinicItemBillingType;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "ab7a9313-6d57-4942-b1dd-ce6b2caf9a26")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "f180f624-ea3f-4848-8a6e-fbe4a91e7456")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "7cc8229a-592f-4897-8eff-49d2f45e0aba")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "f3baec75-5a26-422e-9e59-a8dfaa3e0c1c")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "91d7c00d-7e8e-4834-b30e-5d568870cb86")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "2e276639-b625-4550-bdfa-c2502a167190")
    private String performDepartmentId;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "196e5125-95b1-410e-9491-409ffc74ed12")
    private List<String> useScopeList;
}
