package com.pulse.dictionary_business.manager.dto;

import com.pulse.billing_public_config.manager.dto.ChargeItemClinicItemDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "0c4136e3-5347-44f1-a750-0da3c6d7c88f|DTO|DEFINITION")
public class ClinicItemChargeItemListDto {
    /** 允许修改计数标志 */
    @AutoGenerated(locked = true, uuid = "97454b74-1f23-4ed7-bbd9-a9cd68372efc")
    private Boolean allowModifyCountFlag;

    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "0c9760c4-45eb-48d5-8359-9068e53d42d2")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "9fb30e86-ff8d-496d-a40d-5cc88db3c7ea")
    private ChargeItemClinicItemDto chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "82a53168-3958-4494-86af-4739cf9e653c")
    private Long chargeItemCount;

    /** 诊疗医保代码 */
    @AutoGenerated(locked = true, uuid = "380a3152-e241-4bae-ba16-f2493782ba23")
    private String clinicInsuranceCode;

    /** 诊疗项目计费类型 */
    @AutoGenerated(locked = true, uuid = "8a200bf4-a90a-4e43-8314-b2b6603315fa")
    private String clinicItemBillingType;

    /** 诊疗项目id */
    @AutoGenerated(locked = true, uuid = "3ef7acb2-8cac-4b9d-920b-cf5d6ce5b930")
    private String clinicItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "680c7c1f-8884-4a89-a662-7dd825f30dee")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "7e0062d7-0d1c-474f-a4f0-61a80fddbafd")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "3a0a201a-d0d4-4ec3-a561-36ed77c8acea")
    private Boolean digitalImagingFeeFlag;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "a713c0f3-a23c-4a99-9f31-9933e08095c0")
    private String filmFeeType;

    /** 首次计费标志 */
    @AutoGenerated(locked = true, uuid = "0a6b3c9f-98f7-4e28-97e9-b7870b795b51")
    private Boolean firstTimeBillingFlag;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "6bdfe8bf-6640-498c-b96a-131e4823d9c9")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4c80d2e6-db4a-4c36-9faa-831efc5f7f40")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "0c267b21-9579-4aeb-a60f-88781a989325")
    private String performDepartmentId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "737794d2-500e-47c2-ad51-28ebd25e3342")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "1fcf2f63-d9be-4c67-92a9-69b2e47482eb")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e3cb5f0b-68f9-4bba-838b-318d1f9cf4e0")
    private List<String> useScopeList;
}
