package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "0d3601d9-d9e8-4073-af38-308a9546f750|DTO|DEFINITION")
public class ExamDeviceChargeItemBaseDto {
    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a0ba8f35-e515-46a0-a9ab-39f93be4ca88")
    private List<String> campusIdList;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "ef341c52-fb3f-4558-9fd2-4826f9d30d16")
    private Long chargeItemCount;

    /** 收费项目ID */
    @AutoGenerated(locked = true, uuid = "fcf50320-ad3f-41de-aa2e-bebd930b7cf7")
    private String chargeItemId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "4d886c4b-3792-4f47-bf9f-05f55b8576c9")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "1bf4bb97-0b3a-4633-9a6f-28002703e907")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "f462f1bf-7fe5-42cb-bfc4-5d95a98bb530")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "11b8da5e-97ea-4e6b-b128-3aab5dee78fd")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "5f3580f9-5d69-4026-81e7-c264adfa5a18")
    private Boolean enhancedFlag;

    /** 检查设备id */
    @AutoGenerated(locked = true, uuid = "f886689c-1f8a-4642-ab35-9ae703abafa0")
    private String examDeviceId;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "bcb6bcb2-46bc-4098-8c0a-8dffa87a8c16")
    private FilmFeeTypeEnum filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "19ca2b34-45b4-4b57-a3a4-6df993d44649")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5ac0abd3-1335-4d2f-807e-b4526d75f8e3")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b7d6c2b5-4067-4700-b2ce-eabdfca61e2f")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "6a105d49-8b94-407b-9b05-ffc79b0d7f36")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "525fe208-1335-4c7e-a0b0-4c31e33331ed")
    private List<String> useScopeList;
}
