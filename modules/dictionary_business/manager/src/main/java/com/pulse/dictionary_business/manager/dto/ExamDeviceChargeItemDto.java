package com.pulse.dictionary_business.manager.dto;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@AutoGenerated(locked = false, uuid = "d09aae3d-4d0c-4865-a77d-576e42ed7822|DTO|DEFINITION")
public class ExamDeviceChargeItemDto {
    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "*************-4f09-ab99-da28b251ab48")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "a994455d-846f-4c24-9b56-a835a0257add")
    private ChargeItemDto chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "860a9b37-5efd-4251-ac9e-68ea32f90ada")
    private Long chargeItemCount;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "31e45de9-637a-4c83-b881-ae6be6e29be5")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "0ee6a245-9317-4810-aa60-6782f67560dc")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "00b7373b-457a-4971-8b8a-0bc9e57bd9b1")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "c0193971-1634-4b0b-88e3-240e8d50b4b2")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "db4c33af-5e3c-40cb-add8-04a8dcf7d4a7")
    private Boolean enhancedFlag;

    /** 检查设备id */
    @Valid
    @AutoGenerated(locked = true, uuid = "d564c7c7-1127-4dbc-95ab-1e7023268169")
    private ExamDeviceBaseDto examDevice;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "599d8740-feed-43f7-ab55-74978df3c24c")
    private FilmFeeTypeEnum filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "8a1e2701-d8dc-484e-9f22-a3dcc2ff87eb")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "cd8b3b1b-8b03-465d-a89f-4c516d84ae4d")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "cb3e994a-7ccc-476b-94b9-b035392d4690")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "5d586328-fae3-4d51-8c99-76d2fe4d2737")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8e24dde9-0550-4f42-a780-ae1ae04cbc93")
    private List<String> useScopeList;
}
