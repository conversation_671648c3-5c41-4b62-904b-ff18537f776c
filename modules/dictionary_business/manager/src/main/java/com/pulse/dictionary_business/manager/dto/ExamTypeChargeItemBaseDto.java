package com.pulse.dictionary_business.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "d489e16a-eded-48e3-9302-864c7505b033|DTO|DEFINITION")
public class ExamTypeChargeItemBaseDto {
    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8f1f436a-2436-4a7c-99a6-0c884d8f9109")
    private List<String> campusIdList;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "39bafc57-c661-4746-b638-75c2a9b09ae3")
    private Long chargeItemCount;

    /** 收费项目ID */
    @AutoGenerated(locked = true, uuid = "5f4bd3d7-cabf-4dd0-8f74-6d35019c2a12")
    private String chargeItemId;

    /** 收费部位数量 */
    @AutoGenerated(locked = true, uuid = "89e3d3d2-4160-4765-baee-4b04e24196cf")
    private Long chargePartNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "ba7189e8-0f17-40c2-ae0c-6fe25b9dbe01")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "8f208dc6-4cec-409f-892c-dcdb0146ebfb")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "5554d8f6-7f2c-424e-ad0e-a67e77f7dd19")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "08c9f74d-85d5-4dc3-b080-799fcd8a2c13")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "8e97cbf3-3256-45bb-9ee9-f1fe5cc476d4")
    private Boolean enhancedFlag;

    /** 检查类型id */
    @AutoGenerated(locked = true, uuid = "c72790d4-3fe6-406e-9e2b-0207ff9b082d")
    private String examTypeId;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "bdcd3da9-d509-4325-bbf3-1c76f9aa08ed")
    private String filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "d4524f99-97bf-4f6b-81b4-5d9037b1152e")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9057daeb-6ee0-40fe-a24c-6ba79d2a9612")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "7e8fa146-da5d-405e-b0e8-d6d1635a09f8")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "84d293c7-59d2-4ea7-95ff-a68deeb0443d")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "fd04d85f-4892-46ee-aca2-8d757f0a7039")
    private List<String> useScopeList;
}
