package com.pulse.dictionary_business.manager.dto;

import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "1368c912-b649-41f5-adb3-2500f5152931|DTO|DEFINITION")
public class ExamTypeChargeItemDetailDto {
    /** 院区ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "27cda8b3-13a6-490f-a750-7f8781221f67")
    private List<String> campusIdList;

    /** 收费项目ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "60c20708-b886-4b94-ad8c-71cb7bbfe916")
    private ChargeItemDto chargeItem;

    /** 收费项目数量 */
    @AutoGenerated(locked = true, uuid = "a74c95c9-83db-4602-b7c3-68e05d5361e8")
    private Long chargeItemCount;

    /** 收费部位数量 */
    @AutoGenerated(locked = true, uuid = "7fde725d-b85c-4984-8aa3-12d17977a54a")
    private Long chargePartNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9911cca2-e564-4849-95d3-952e7d9302fe")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "79b5561f-3098-44ab-ae39-af30d7b26929")
    private String createdBy;

    /** 数字成像费用标志 */
    @AutoGenerated(locked = true, uuid = "4c385f71-30b6-42c8-9699-c21fa6ba8185")
    private Boolean digitalImagingFeeFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "a3a4ae09-8935-41a2-a413-f7009ce577b9")
    private Boolean enableFlag;

    /** 增强标志 */
    @AutoGenerated(locked = true, uuid = "d75d8e24-57fd-4524-922d-03ded78f4777")
    private Boolean enhancedFlag;

    /** 检查类型id */
    @Valid
    @AutoGenerated(locked = true, uuid = "a3561e26-f064-40ae-abd5-e00cc975a5e8")
    private ExamTypeDictionaryBaseDto examType;

    /** 胶片费类型 */
    @AutoGenerated(locked = true, uuid = "58320967-bfff-43fa-a6a9-9ba6683a436e")
    private String filmFeeType;

    /** 图文费用标志 */
    @AutoGenerated(locked = true, uuid = "19ec9dd7-ab24-49a8-b8c7-3feeff83571d")
    private Boolean graphicFeeFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a94c4405-78c7-4d5a-804e-9b4cf4d2f008")
    private String id;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "e1dc2616-880d-4420-bec2-b012af81e931")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "002b1420-55c5-4d0a-bcd5-72ce91451375")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4597f5b5-6c92-4ce0-895f-9f4ee425d581")
    private List<String> useScopeList;
}
