package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@TableName(value = "clinic_item_charge_item", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "67307b00-a0d0-42e5-b6f0-4f71ec408a2b|ENTITY|DEFINITION")
public class ClinicItemChargeItem {
    @AutoGenerated(locked = true, uuid = "d7593e3b-9daa-4fda-bca1-7e33c6d5ad79")
    @TableField(value = "allow_modify_count_flag")
    private Boolean allowModifyCountFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "37b3101a-de77-4fcb-afac-1ac338bea583")
    @TableField(value = "campus_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> campusIdList;

    @AutoGenerated(locked = true, uuid = "c86c5980-551a-438c-8bc0-83be64a3f417")
    @TableField(value = "charge_item_count")
    private Long chargeItemCount;

    @AutoGenerated(locked = true, uuid = "097f39b8-57ca-474d-a532-57b412776202")
    @TableField(value = "charge_item_id")
    private String chargeItemId;

    @AutoGenerated(locked = true, uuid = "ebb5706a-47fe-49e8-a193-450440b70cba")
    @TableField(value = "clinic_insurance_code")
    private String clinicInsuranceCode;

    @AutoGenerated(locked = true, uuid = "b2066a82-bd4b-41c0-82a5-1d625ffe2ad1")
    @TableField(value = "clinic_item_billing_type")
    private String clinicItemBillingType;

    @AutoGenerated(locked = true, uuid = "7cd39b4d-db66-42af-9a0a-9f8c0bff3e7b")
    @TableField(value = "clinic_item_id")
    private String clinicItemId;

    @AutoGenerated(locked = true, uuid = "e30d99a5-9524-461d-81df-74c5a89afc44")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "5289341a-fd0f-430a-8698-a08ca724fb95")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "e54e869c-8840-4c11-b9f0-e303cdff97ff")
    @TableField(value = "digital_imaging_fee_flag")
    private Boolean digitalImagingFeeFlag;

    @AutoGenerated(locked = true, uuid = "fe6da436-9c79-4899-8015-e6a04fc7af60")
    @TableField(value = "film_fee_type")
    private String filmFeeType;

    @AutoGenerated(locked = true, uuid = "cb312a49-8f87-4c53-84d0-9b1f4505e0c9")
    @TableField(value = "first_time_billing_flag")
    private Boolean firstTimeBillingFlag;

    @AutoGenerated(locked = true, uuid = "8ee8a3e2-d7f3-4eb2-92cc-ef5b4c7cc632")
    @TableField(value = "graphic_fee_flag")
    private Boolean graphicFeeFlag;

    @AutoGenerated(locked = true, uuid = "e8aa1448-3e8d-43cb-9641-c759b87b3848")
    @TableId(value = "id")
    @NotNull(message = "主键不能为空")
    private String id;

    @AutoGenerated(locked = true, uuid = "8d9b119f-38a2-4315-a917-7986695b2cf7")
    @TableField(value = "perform_department_id")
    private String performDepartmentId;

    @AutoGenerated(locked = true, uuid = "78fcef3d-8588-4a20-a7fd-043044fb297e")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "90505316-c9dd-4121-88b1-6b5f5d773363")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Valid
    @AutoGenerated(locked = true, uuid = "a2b34fa5-c7a7-4026-91c7-4e610672b660")
    @TableField(value = "use_scope_list", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
