package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@TableName(value = "exam_device_charge_item", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "b612e5ff-2e91-4f25-8755-456483fd7977|ENTITY|DEFINITION")
public class ExamDeviceChargeItem {
    @Valid
    @AutoGenerated(locked = true, uuid = "706a61f1-95e2-4bdc-9ffd-a767ca7ffd00")
    @TableField(value = "campus_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> campusIdList;

    @AutoGenerated(locked = true, uuid = "c8036098-2208-41d6-9e10-9a1a230396ee")
    @TableField(value = "charge_item_count")
    private Long chargeItemCount;

    @AutoGenerated(locked = true, uuid = "c8270664-4337-4e10-960a-42a1944a4ec1")
    @TableField(value = "charge_item_id")
    private String chargeItemId;

    @AutoGenerated(locked = true, uuid = "21cabfd5-1654-40bc-984c-4122c467f1b6")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "d66abc06-483f-4f6d-a1d1-bcf95bcec595")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "35cb5c69-996d-42cb-8c8e-c8e8823e2ea5")
    @TableField(value = "digital_imaging_fee_flag")
    private Boolean digitalImagingFeeFlag;

    @AutoGenerated(locked = true, uuid = "a03d020e-a3fa-4abb-b4a7-83df7c7d3405")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @AutoGenerated(locked = true, uuid = "66381c7b-6e78-4d34-b997-9afc04848e33")
    @TableField(value = "enhanced_flag")
    private Boolean enhancedFlag;

    @AutoGenerated(locked = true, uuid = "3268c38e-6070-4504-8afa-1000ec5a4bea")
    @TableField(value = "exam_device_id")
    private String examDeviceId;

    @AutoGenerated(locked = true, uuid = "16476865-abbb-4e73-af95-53274db3648b")
    @TableField(value = "film_fee_type")
    private FilmFeeTypeEnum filmFeeType;

    @AutoGenerated(locked = true, uuid = "ce46c03c-3ff5-4e0e-9aa3-856dda6eb216")
    @TableField(value = "graphic_fee_flag")
    private Boolean graphicFeeFlag;

    @AutoGenerated(locked = true, uuid = "7c1bf392-a208-4ade-afba-77c51396711b")
    @TableId(value = "id")
    @NotNull(message = "主键不能为空")
    private String id;

    @AutoGenerated(locked = true, uuid = "0f082bb0-a100-4714-ba70-4eb11290e5ff")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "73b76eed-7109-4b8b-bf3b-0b8b106861f4")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Valid
    @AutoGenerated(locked = true, uuid = "ffb00611-daa5-4bc8-b5e5-381b7b3296d8")
    @TableField(value = "use_scope", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
