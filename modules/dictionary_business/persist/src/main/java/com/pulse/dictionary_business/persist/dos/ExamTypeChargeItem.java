package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@TableName(value = "exam_type_charge_item", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "b3366c42-e463-4eac-96b7-53333548014b|ENTITY|DEFINITION")
public class ExamTypeChargeItem {
    @Valid
    @AutoGenerated(locked = true, uuid = "2d41fab3-58c6-4604-a8d5-0321aa00e069")
    @TableField(value = "campus_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> campusIdList;

    @AutoGenerated(locked = true, uuid = "5ba65c28-097a-4bfc-ae03-f6c6b4b3a767")
    @TableField(value = "charge_item_count")
    private Long chargeItemCount;

    @AutoGenerated(locked = true, uuid = "d6758c8a-edb1-4f13-b34c-78db32885382")
    @TableField(value = "charge_item_id")
    private String chargeItemId;

    @AutoGenerated(locked = true, uuid = "9153f94c-c29e-495b-ad14-feda75194173")
    @TableField(value = "charge_part_number")
    private Long chargePartNumber;

    @AutoGenerated(locked = true, uuid = "1f71e766-cceb-5d0e-a8c4-7f935fa93291")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "457595e2-d930-4180-ba7a-93d42e694537")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "dca52760-ac3c-4e99-8a92-66169efedc55")
    @TableField(value = "digital_imaging_fee_flag")
    private Boolean digitalImagingFeeFlag;

    @AutoGenerated(locked = true, uuid = "3c51bdf1-8326-4a47-bbe8-4a3224375b3e")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @AutoGenerated(locked = true, uuid = "557b6791-df37-44f6-bd47-2cce192b19b7")
    @TableField(value = "enhanced_flag")
    private Boolean enhancedFlag;

    @AutoGenerated(locked = true, uuid = "fb378eed-c117-43fb-a8dc-7a2ed73e8201")
    @TableField(value = "exam_type_id")
    private String examTypeId;

    @AutoGenerated(locked = true, uuid = "0b3b7975-65d0-4de9-8a07-19e750702181")
    @TableField(value = "film_fee_type")
    private String filmFeeType;

    @AutoGenerated(locked = true, uuid = "58cafedc-b663-4a3b-9d1e-5b3f28d60eb9")
    @TableField(value = "graphic_fee_flag")
    private Boolean graphicFeeFlag;

    @AutoGenerated(locked = true, uuid = "91fed477-b134-4368-b9d5-1f956a2d201f")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "a8c6ce94-9b52-5fcb-a804-d183a7155ba9")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "f8d18bad-2172-4bb1-9322-b4f8bd010280")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Valid
    @AutoGenerated(locked = true, uuid = "d9ad596c-a81b-429f-ac1c-9281d015fbc6")
    @TableField(value = "use_scope", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
