package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.service.converter.ClinicItemChargeItemBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "63f5acad-245a-44f0-be86-f485f00c46b5|DTO|SERVICE")
public class ClinicItemChargeItemBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ClinicItemChargeItemBaseDtoManager clinicItemChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ClinicItemChargeItemBaseDtoServiceConverter clinicItemChargeItemBaseDtoServiceConverter;

    @PublicInterface(
            id = "85982d7c-3786-457e-913f-d4c6b369e39e",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734998")
    @AutoGenerated(locked = false, uuid = "3297f12e-5f57-3fcf-8f62-2ec27ca86f1d")
    public List<ClinicItemChargeItemBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList =
                clinicItemChargeItemBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemBaseDtoServiceConverter.ClinicItemChargeItemBaseDtoConverter(
                clinicItemChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a1b7c77a-c432-4e7e-904f-9ca9d2f4e2f4",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007735001")
    @AutoGenerated(locked = false, uuid = "5776b433-8e07-3463-a807-ed617b510b68")
    public List<ClinicItemChargeItemBaseDto> getByClinicItemId(
            @NotNull(message = "诊疗项目id不能为空") String clinicItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicItemIds(Arrays.asList(clinicItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "f84f78f4-e495-47d6-b274-a4f85a247e18",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734991")
    @AutoGenerated(locked = false, uuid = "984b8938-6283-3916-b451-2e74270eaa90")
    public List<ClinicItemChargeItemBaseDto> getByChargeItemIds(
            @Valid @NotNull(message = "收费项目ID不能为空") List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        chargeItemId = new ArrayList<>(new HashSet<>(chargeItemId));
        List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList =
                clinicItemChargeItemBaseDtoManager.getByChargeItemIds(chargeItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemBaseDtoServiceConverter.ClinicItemChargeItemBaseDtoConverter(
                clinicItemChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "d3906277-70ef-4b7b-bee1-465a7c9b1ffa",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734994")
    @AutoGenerated(locked = false, uuid = "cbb54bb2-ede0-3be6-a22a-b41b3fd8a4e9")
    public ClinicItemChargeItemBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ClinicItemChargeItemBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "6578ab41-6d06-4943-8931-d9a38823f0c2",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734987")
    @AutoGenerated(locked = false, uuid = "ceffdcc2-4150-3c20-b9bb-c81eb57c906e")
    public List<ClinicItemChargeItemBaseDto> getByChargeItemId(
            @NotNull(message = "收费项目ID不能为空") String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByChargeItemIds(Arrays.asList(chargeItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "acb9e3f7-d6f5-4dfa-ba8c-f0e8451e5b76",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007735005")
    @AutoGenerated(locked = false, uuid = "e73efd84-f963-344b-bee4-079062515994")
    public List<ClinicItemChargeItemBaseDto> getByClinicItemIds(
            @Valid @NotNull(message = "诊疗项目id不能为空") List<String> clinicItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicItemId = new ArrayList<>(new HashSet<>(clinicItemId));
        List<ClinicItemChargeItemBaseDto> clinicItemChargeItemBaseDtoList =
                clinicItemChargeItemBaseDtoManager.getByClinicItemIds(clinicItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemBaseDtoServiceConverter.ClinicItemChargeItemBaseDtoConverter(
                clinicItemChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
