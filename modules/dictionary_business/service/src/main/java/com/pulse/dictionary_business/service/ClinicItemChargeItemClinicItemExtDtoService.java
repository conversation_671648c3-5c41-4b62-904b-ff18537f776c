package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ClinicItemChargeItemClinicItemExtDtoManager;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemClinicItemExtDto;
import com.pulse.dictionary_business.service.converter.ClinicItemChargeItemClinicItemExtDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "b048b5df-0362-4fda-be8a-fa9bee3b89fc|DTO|SERVICE")
public class ClinicItemChargeItemClinicItemExtDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ClinicItemChargeItemClinicItemExtDtoManager clinicItemChargeItemClinicItemExtDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ClinicItemChargeItemClinicItemExtDtoServiceConverter
            clinicItemChargeItemClinicItemExtDtoServiceConverter;

    @PublicInterface(
            id = "481c7b49-8570-4115-8a8a-ebd84e8dbbd6",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734914")
    @AutoGenerated(locked = false, uuid = "3297f12e-5f57-3fcf-8f62-2ec27ca86f1d")
    public List<ClinicItemChargeItemClinicItemExtDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ClinicItemChargeItemClinicItemExtDto> clinicItemChargeItemClinicItemExtDtoList =
                clinicItemChargeItemClinicItemExtDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemClinicItemExtDtoServiceConverter
                .ClinicItemChargeItemClinicItemExtDtoConverter(
                        clinicItemChargeItemClinicItemExtDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "65ef81d8-8ac4-4c9c-bbc6-1460ebe51548",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734924")
    @AutoGenerated(locked = false, uuid = "5776b433-8e07-3463-a807-ed617b510b68")
    public List<ClinicItemChargeItemClinicItemExtDto> getByClinicItemId(
            @NotNull(message = "诊疗项目id不能为空") String clinicItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicItemIds(Arrays.asList(clinicItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "e5a15c43-b07b-4977-9285-be4eb3c396a6",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734892")
    @AutoGenerated(locked = false, uuid = "984b8938-6283-3916-b451-2e74270eaa90")
    public List<ClinicItemChargeItemClinicItemExtDto> getByChargeItemIds(
            @Valid @NotNull(message = "收费项目ID不能为空") List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        chargeItemId = new ArrayList<>(new HashSet<>(chargeItemId));
        List<ClinicItemChargeItemClinicItemExtDto> clinicItemChargeItemClinicItemExtDtoList =
                clinicItemChargeItemClinicItemExtDtoManager.getByChargeItemIds(chargeItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemClinicItemExtDtoServiceConverter
                .ClinicItemChargeItemClinicItemExtDtoConverter(
                        clinicItemChargeItemClinicItemExtDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a7d24073-7ae0-4c52-a783-4bbeaf053693",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734903")
    @AutoGenerated(locked = false, uuid = "cbb54bb2-ede0-3be6-a22a-b41b3fd8a4e9")
    public ClinicItemChargeItemClinicItemExtDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ClinicItemChargeItemClinicItemExtDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "08b1c747-4443-41e5-9e4e-76ad751a4f9d",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734881")
    @AutoGenerated(locked = false, uuid = "ceffdcc2-4150-3c20-b9bb-c81eb57c906e")
    public List<ClinicItemChargeItemClinicItemExtDto> getByChargeItemId(
            @NotNull(message = "收费项目ID不能为空") String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByChargeItemIds(Arrays.asList(chargeItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "853271c4-a177-4daa-aec1-f09a50982d24",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1749007734935")
    @AutoGenerated(locked = false, uuid = "e73efd84-f963-344b-bee4-079062515994")
    public List<ClinicItemChargeItemClinicItemExtDto> getByClinicItemIds(
            @Valid @NotNull(message = "诊疗项目id不能为空") List<String> clinicItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicItemId = new ArrayList<>(new HashSet<>(clinicItemId));
        List<ClinicItemChargeItemClinicItemExtDto> clinicItemChargeItemClinicItemExtDtoList =
                clinicItemChargeItemClinicItemExtDtoManager.getByClinicItemIds(clinicItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return clinicItemChargeItemClinicItemExtDtoServiceConverter
                .ClinicItemChargeItemClinicItemExtDtoConverter(
                        clinicItemChargeItemClinicItemExtDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
