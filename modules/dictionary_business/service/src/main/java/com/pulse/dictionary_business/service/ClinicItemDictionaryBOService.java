package com.pulse.dictionary_business.service;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemGuideDescriptionBaseDto;
import com.pulse.dictionary_business.persist.dos.ClinicItemChargeItem;
import com.pulse.dictionary_business.persist.dos.ClinicItemDictionary;
import com.pulse.dictionary_business.persist.dos.ClinicItemGuideDescription;
import com.pulse.dictionary_business.persist.dos.ClinicItemPerformDepartment;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService;
import com.pulse.dictionary_business.service.bto.ChangeClinicItemAuditFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeClinicItemGuideDescriptionEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemDictionaryBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.EnableClinicItemBto;
import com.pulse.dictionary_business.service.bto.SaveClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.SaveClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemDetailBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemDictionaryBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemGuideDescriptionBto;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "f85d0833-6b18-49fe-8f64-2517fa3dfdf5|BO|SERVICE")
public class ClinicItemDictionaryBOService extends BaseClinicItemDictionaryBOService {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemGuideDescriptionBaseDtoService clinicItemGuideDescriptionBaseDtoService;

    /** 创建诊疗项目导医说明 */
    @PublicInterface(id = "ea089196-5f42-4dbc-96b4-42d2db8c3055", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "04e5103f-d9a9-480e-84b2-e1d2403b54ad")
    public String createClinicItemGuideDescription(
            @Valid @NotNull
                    CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            clinicItemGuideDescriptionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto =
                new CreateClinicItemGuideDescriptionBto();
        createClinicItemGuideDescriptionBto.setClinicItemGuideDescriptionBtoList(
                List.of(clinicItemGuideDescriptionBto));
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        clinicItemGuideDescriptionBto.getClinicItemId());
        createClinicItemGuideDescriptionBto.setClinicItemId(
                clinicItemDictionaryBaseDto.getClinicItemId());
        CreateClinicItemGuideDescriptionBoResult boResult =
                super.createClinicItemGuideDescriptionBase(createClinicItemGuideDescriptionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto */
        {
            for (CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto bto :
                    boResult
                            .<CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto>
                                    getBtoOfType(
                                            CreateClinicItemGuideDescriptionBto
                                                    .ClinicItemGuideDescriptionBto.class)) {
                AddedBto<
                                CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescriptionBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 启用/停用诊疗项目 */
    @PublicInterface(id = "850b3d18-f155-4751-90fb-87a906bd32da", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "33eb9949-0f56-41f7-9c8a-674edf1d955b")
    public String enableClinicItem(@Valid @NotNull EnableClinicItemBto enableClinicItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        enableClinicItemBto.getClinicItemId());
        EnableClinicItemBoResult boResult = super.enableClinicItemBase(enableClinicItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 EnableClinicItemBto */
        {
            EnableClinicItemBto bto =
                    boResult.<EnableClinicItemBto>getBtoOfType(EnableClinicItemBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<EnableClinicItemBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建诊疗项目对应收费项目 */
    @PublicInterface(id = "1c03cebc-1a46-4885-8ec4-c2f0897325d6", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b2fb92a6-3f51-41ad-a5c0-db63cc991830")
    public String createClinicItemChargeItem(
            @Valid @NotNull
                    CreateClinicItemChargeItemBto.ClinicItemChargeItemBto clinicItemChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateClinicItemChargeItemBto createClinicItemChargeItemBto =
                new CreateClinicItemChargeItemBto();
        createClinicItemChargeItemBto.setClinicItemChargeItemBtoList(
                List.of(clinicItemChargeItemBto));
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        clinicItemChargeItemBto.getClinicItemId());
        createClinicItemChargeItemBto.setClinicItemId(
                clinicItemDictionaryBaseDto.getClinicItemId());
        CreateClinicItemChargeItemBoResult boResult =
                super.createClinicItemChargeItemBase(createClinicItemChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateClinicItemChargeItemBto.ClinicItemChargeItemBto */
        {
            for (CreateClinicItemChargeItemBto.ClinicItemChargeItemBto bto :
                    boResult.<CreateClinicItemChargeItemBto.ClinicItemChargeItemBto>getBtoOfType(
                            CreateClinicItemChargeItemBto.ClinicItemChargeItemBto.class)) {
                AddedBto<
                                CreateClinicItemChargeItemBto.ClinicItemChargeItemBto,
                                ClinicItemChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新诊疗项目明细（包括收费项目、执行科室） */
    @PublicInterface(id = "490ff3fd-b2f7-43b5-ad76-33b972083b3e", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b660cb89-f358-4d3a-ad04-8062207ed3df")
    public String updateClinicItemDetail(
            @Valid @NotNull UpdateClinicItemDetailBto updateClinicItemDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        updateClinicItemDetailBto.getClinicItemId());
        UpdateClinicItemDetailBoResult boResult =
                super.updateClinicItemDetailBase(updateClinicItemDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateClinicItemDetailBto.ClinicItemChargeItemBto */
        {
            for (UpdateClinicItemDetailBto.ClinicItemChargeItemBto bto :
                    boResult.<UpdateClinicItemDetailBto.ClinicItemChargeItemBto>getBtoOfType(
                            UpdateClinicItemDetailBto.ClinicItemChargeItemBto.class)) {
                UpdatedBto<
                                UpdateClinicItemDetailBto.ClinicItemChargeItemBto,
                                ClinicItemChargeItem,
                                ClinicItemChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<UpdateClinicItemDetailBto.ClinicItemChargeItemBto, ClinicItemChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ClinicItemChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(ClinicItemChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto */
        {
            for (UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto bto :
                    boResult.<UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto>getBtoOfType(
                            UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto.class)) {
                UpdatedBto<
                                UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto,
                                ClinicItemPerformDepartment,
                                ClinicItemPerformDepartmentBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto,
                                ClinicItemPerformDepartmentBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemPerformDepartmentBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemPerformDepartment entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemPerformDepartmentBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ClinicItemPerformDepartment> deletedEntityList =
                    boResult.getDeletedEntityList(ClinicItemPerformDepartment.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 UpdateClinicItemDetailBto */
        {
            UpdateClinicItemDetailBto bto =
                    boResult
                            .<UpdateClinicItemDetailBto>getBtoOfType(
                                    UpdateClinicItemDetailBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateClinicItemDetailBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新导医说明 */
    @PublicInterface(id = "a9fb7e17-9b22-4413-b852-95078c92d5f6", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "cacc098d-447f-4df4-9473-8de0c6e4e584")
    public String updateClinicItemGuideDescription(
            @Valid @NotNull
                    UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            clinicItemGuideDescriptionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto =
                new UpdateClinicItemGuideDescriptionBto();
        updateClinicItemGuideDescriptionBto.setClinicItemGuideDescriptionBtoList(
                List.of(clinicItemGuideDescriptionBto));
        ClinicItemGuideDescriptionBaseDto clinicItemGuideDescriptionBaseDto =
                clinicItemGuideDescriptionBaseDtoService.getById(
                        clinicItemGuideDescriptionBto.getId());
        if (clinicItemGuideDescriptionBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        clinicItemGuideDescriptionBaseDto.getClinicItemId());
        updateClinicItemGuideDescriptionBto.setClinicItemId(
                clinicItemDictionaryBaseDto.getClinicItemId());
        UpdateClinicItemGuideDescriptionBoResult boResult =
                super.updateClinicItemGuideDescriptionBase(updateClinicItemGuideDescriptionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto */
        {
            for (UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto bto :
                    boResult
                            .<UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto>
                                    getBtoOfType(
                                            UpdateClinicItemGuideDescriptionBto
                                                    .ClinicItemGuideDescriptionBto.class)) {
                UpdatedBto<
                                UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescription,
                                ClinicItemGuideDescriptionBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemGuideDescription entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更改诊疗项目审核状态 */
    @PublicInterface(id = "df1a5973-19b5-44da-b790-26caf9218d28", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "d8e43205-f7f9-48af-b92c-8c50519694b9")
    public String changeClinicItemAuditFlag(
            @Valid @NotNull ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        changeClinicItemAuditFlagBto.getClinicItemId());
        ChangeClinicItemAuditFlagBoResult boResult =
                super.changeClinicItemAuditFlagBase(changeClinicItemAuditFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeClinicItemAuditFlagBto */
        {
            ChangeClinicItemAuditFlagBto bto =
                    boResult
                            .<ChangeClinicItemAuditFlagBto>getBtoOfType(
                                    ChangeClinicItemAuditFlagBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<ChangeClinicItemAuditFlagBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更改导医说明停用表示 */
    @PublicInterface(id = "2544f84b-08fb-4b3a-9e0c-625b9e2146c1", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "e6d54bf4-105e-4c14-8e69-475354610c0d")
    public String changeClinicItemGuideDescriptionEnableFlag(
            @Valid @NotNull
                    ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto
                            clinicItemGuideDescriptionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ChangeClinicItemGuideDescriptionEnableFlagBto
                changeClinicItemGuideDescriptionEnableFlagBto =
                        new ChangeClinicItemGuideDescriptionEnableFlagBto();
        changeClinicItemGuideDescriptionEnableFlagBto.setClinicItemGuideDescriptionBtoList(
                List.of(clinicItemGuideDescriptionBto));
        ClinicItemGuideDescriptionBaseDto clinicItemGuideDescriptionBaseDto =
                clinicItemGuideDescriptionBaseDtoService.getById(
                        clinicItemGuideDescriptionBto.getId());
        if (clinicItemGuideDescriptionBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        clinicItemGuideDescriptionBaseDto.getClinicItemId());
        changeClinicItemGuideDescriptionEnableFlagBto.setClinicItemId(
                clinicItemDictionaryBaseDto.getClinicItemId());
        ChangeClinicItemGuideDescriptionEnableFlagBoResult boResult =
                super.changeClinicItemGuideDescriptionEnableFlagBase(
                        changeClinicItemGuideDescriptionEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto */
        {
            for (ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto bto :
                    boResult
                            .<ChangeClinicItemGuideDescriptionEnableFlagBto
                                            .ClinicItemGuideDescriptionBto>
                                    getBtoOfType(
                                            ChangeClinicItemGuideDescriptionEnableFlagBto
                                                    .ClinicItemGuideDescriptionBto.class)) {
                UpdatedBto<
                                ChangeClinicItemGuideDescriptionEnableFlagBto
                                        .ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescription,
                                ClinicItemGuideDescriptionBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemGuideDescription entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新诊疗项目字典 */
    @PublicInterface(id = "94d0dd21-924c-4600-b243-1f6858ed9b38", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "ec02ec61-1767-4d21-a404-e200db567f13")
    public String updateClinicItemDictionary(
            @Valid @NotNull UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto =
                clinicItemDictionaryBaseDtoService.getByClinicItemId(
                        updateClinicItemDictionaryBto.getClinicItemId());
        UpdateClinicItemDictionaryBoResult boResult =
                super.updateClinicItemDictionaryBase(updateClinicItemDictionaryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateClinicItemDictionaryBto */
        {
            UpdateClinicItemDictionaryBto bto =
                    boResult
                            .<UpdateClinicItemDictionaryBto>getBtoOfType(
                                    UpdateClinicItemDictionaryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateClinicItemDictionaryBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建诊疗项目字典 */
    @PublicInterface(id = "108d59ae-5e96-4f15-b9be-e1be9eea6ce8", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "ecbec004-5a2f-428f-955e-4f992c9efb08")
    public String createClinicItemDictionary(
            @Valid @NotNull CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateClinicItemDictionaryBoResult boResult =
                super.createClinicItemDictionaryBase(createClinicItemDictionaryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateClinicItemDictionaryBto */
        {
            CreateClinicItemDictionaryBto bto =
                    boResult
                            .<CreateClinicItemDictionaryBto>getBtoOfType(
                                    CreateClinicItemDictionaryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateClinicItemDictionaryBto, ClinicItemDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 CreateClinicItemDictionaryBto.ClinicItemChargeItemBto */
        {
            for (CreateClinicItemDictionaryBto.ClinicItemChargeItemBto bto :
                    boResult.<CreateClinicItemDictionaryBto.ClinicItemChargeItemBto>getBtoOfType(
                            CreateClinicItemDictionaryBto.ClinicItemChargeItemBto.class)) {
                AddedBto<
                                CreateClinicItemDictionaryBto.ClinicItemChargeItemBto,
                                ClinicItemChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (createClinicItemDictionaryBto != null) {
                        if (!createClinicItemDictionaryBto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择医嘱项目适用院区外的值");
                        }
                    }
                }
            }
        }
        /** 处理 CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto */
        {
            for (CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto bto :
                    boResult
                            .<CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto>
                                    getBtoOfType(
                                            CreateClinicItemDictionaryBto
                                                    .ClinicItemPerformDepartmentBto.class)) {
                AddedBto<
                                CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto,
                                ClinicItemPerformDepartmentBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemPerformDepartmentBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto */
        {
            for (CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto bto :
                    boResult
                            .<CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto>
                                    getBtoOfType(
                                            CreateClinicItemDictionaryBto
                                                    .ClinicItemGuideDescriptionBto.class)) {
                AddedBto<
                                CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescriptionBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存诊疗项目导医说明 */
    @PublicInterface(id = "aded2534-515f-417b-a615-b3468085f5e5", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "eda49dfd-4184-400f-8975-ee28c2707286")
    public String saveClinicItemGuideDescription(
            @Valid @NotNull SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto = null;
        if (saveClinicItemGuideDescriptionBto.getClinicItemId() != null) {
            clinicItemDictionaryBaseDto =
                    clinicItemDictionaryBaseDtoService.getByClinicItemId(
                            saveClinicItemGuideDescriptionBto.getClinicItemId());
        }
        SaveClinicItemGuideDescriptionBoResult boResult =
                super.saveClinicItemGuideDescriptionBase(saveClinicItemGuideDescriptionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto */
        {
            for (SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto bto :
                    boResult
                            .<SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto>
                                    getBtoOfType(
                                            SaveClinicItemGuideDescriptionBto
                                                    .ClinicItemGuideDescriptionBto.class)) {
                UpdatedBto<
                                SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescription,
                                ClinicItemGuideDescriptionBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                                ClinicItemGuideDescriptionBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemGuideDescription entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemGuideDescriptionBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ClinicItemGuideDescription> deletedEntityList =
                    boResult.getDeletedEntityList(ClinicItemGuideDescription.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveClinicItemGuideDescriptionBto */
        {
            SaveClinicItemGuideDescriptionBto bto =
                    boResult
                            .<SaveClinicItemGuideDescriptionBto>getBtoOfType(
                                    SaveClinicItemGuideDescriptionBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            SaveClinicItemGuideDescriptionBto,
                            ClinicItemDictionary,
                            ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveClinicItemGuideDescriptionBto, ClinicItemDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存诊疗项目收费项目 */
    @PublicInterface(id = "b07df487-53f8-4911-9987-e54b888bbf2a", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "f0a7eed8-2bf4-4167-84ef-f7bb533aee6f")
    public String saveClinicItemChargeItem(
            @Valid @NotNull SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ClinicItemDictionaryBaseDto clinicItemDictionaryBaseDto = null;
        if (saveClinicItemChargeItemBto.getClinicItemId() != null) {
            clinicItemDictionaryBaseDto =
                    clinicItemDictionaryBaseDtoService.getByClinicItemId(
                            saveClinicItemChargeItemBto.getClinicItemId());
        }
        SaveClinicItemChargeItemBoResult boResult =
                super.saveClinicItemChargeItemBase(saveClinicItemChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveClinicItemChargeItemBto.ClinicItemChargeItemBto */
        {
            for (SaveClinicItemChargeItemBto.ClinicItemChargeItemBto bto :
                    boResult.<SaveClinicItemChargeItemBto.ClinicItemChargeItemBto>getBtoOfType(
                            SaveClinicItemChargeItemBto.ClinicItemChargeItemBto.class)) {
                UpdatedBto<
                                SaveClinicItemChargeItemBto.ClinicItemChargeItemBto,
                                ClinicItemChargeItem,
                                ClinicItemChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                SaveClinicItemChargeItemBto.ClinicItemChargeItemBto,
                                ClinicItemChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ClinicItemChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    if (clinicItemDictionaryBaseDto != null) {
                        if (!clinicItemDictionaryBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择医嘱项目适用院区外的值");
                        }
                    }
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ClinicItemChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (clinicItemDictionaryBaseDto != null) {
                        if (!clinicItemDictionaryBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择医嘱项目适用院区外的值");
                        }
                    }
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ClinicItemChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(ClinicItemChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveClinicItemChargeItemBto */
        {
            SaveClinicItemChargeItemBto bto =
                    boResult
                            .<SaveClinicItemChargeItemBto>getBtoOfType(
                                    SaveClinicItemChargeItemBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveClinicItemChargeItemBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveClinicItemChargeItemBto, ClinicItemDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ClinicItemDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ClinicItemDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getClinicItemId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
