package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceOrderLimitBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamDevice;
import com.pulse.dictionary_business.persist.dos.ExamDeviceChargeItem;
import com.pulse.dictionary_business.persist.dos.ExamDeviceDocumentTemplate;
import com.pulse.dictionary_business.persist.dos.ExamDeviceOrderLimit;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceChargeItemEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceOrderLimitEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceOrderLimitBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceChargeItemListBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceDocumentTemplateBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceOrderLimitListBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceChargeItemBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceOrderLimitBto;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "2160d676-9dbd-4c0c-ad80-88f58ccc7095|BO|SERVICE")
public class ExamDeviceBOService extends BaseExamDeviceBOService {
    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceBaseDtoService examDeviceBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemBaseDtoService examDeviceChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceOrderLimitBaseDtoService examDeviceOrderLimitBaseDtoService;

    /** 修改检查设备开单限制 */
    @PublicInterface(id = "01c0cc11-323c-4d35-8c6b-be2597b653a4", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "0ab1f89a-996a-4c22-9634-9d5acf0f7bf4")
    public String updateExamDeviceOrderLimit(
            @Valid @NotNull
                    UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto examDeviceOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto =
                new UpdateExamDeviceOrderLimitBto();
        updateExamDeviceOrderLimitBto.setExamDeviceOrderLimitBtoList(
                List.of(examDeviceOrderLimitBto));
        ExamDeviceOrderLimitBaseDto examDeviceOrderLimitBaseDto =
                examDeviceOrderLimitBaseDtoService.getById(examDeviceOrderLimitBto.getId());
        if (examDeviceOrderLimitBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceOrderLimitBaseDto.getExamDeviceId());
        updateExamDeviceOrderLimitBto.setId(examDeviceBaseDto.getId());
        UpdateExamDeviceOrderLimitBoResult boResult =
                super.updateExamDeviceOrderLimitBase(updateExamDeviceOrderLimitBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto */
        {
            for (UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto bto :
                    boResult.<UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto>getBtoOfType(
                            UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto.class)) {
                UpdatedBto<
                                UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                                ExamDeviceOrderLimit,
                                ExamDeviceOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改检查设备停用标识 */
    @PublicInterface(id = "f4af0afd-22e8-49a7-a225-26f8c1fac6c0", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "4e2738cb-5d01-4219-adf2-f77efc0597d7")
    public String changeExamDeviceEnableFlag(
            @Valid @NotNull ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(changeExamDeviceEnableFlagBto.getId());
        ChangeExamDeviceEnableFlagBoResult boResult =
                super.changeExamDeviceEnableFlagBase(changeExamDeviceEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamDeviceEnableFlagBto */
        {
            ChangeExamDeviceEnableFlagBto bto =
                    boResult
                            .<ChangeExamDeviceEnableFlagBto>getBtoOfType(
                                    ChangeExamDeviceEnableFlagBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<ChangeExamDeviceEnableFlagBto, ExamDevice, ExamDeviceBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamDeviceBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamDevice entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查设备开单限制 */
    @PublicInterface(id = "99d94f14-927d-4a65-b4ca-984a127448d5", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "4fd06538-be88-4311-9b4b-2c68b1612436")
    public String createExamDeviceOrderLimit(
            @Valid @NotNull
                    CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto examDeviceOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto =
                new CreateExamDeviceOrderLimitBto();
        createExamDeviceOrderLimitBto.setExamDeviceOrderLimitBtoList(
                List.of(examDeviceOrderLimitBto));
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceOrderLimitBto.getExamDeviceId());
        createExamDeviceOrderLimitBto.setId(examDeviceBaseDto.getId());
        CreateExamDeviceOrderLimitBoResult boResult =
                super.createExamDeviceOrderLimitBase(createExamDeviceOrderLimitBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto */
        {
            for (CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto bto :
                    boResult.<CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto>getBtoOfType(
                            CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto.class)) {
                AddedBto<
                                CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                                ExamDeviceOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查设备收费项目列表 */
    @PublicInterface(id = "0cf6c876-0800-41e8-b706-73f5a9542f7b", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "904e287b-be2a-4cc9-88d6-04a9883e277f")
    public String saveExamDeviceChargeItemList(
            @Valid @NotNull SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamDeviceBaseDto examDeviceBaseDto = null;
        if (saveExamDeviceChargeItemListBto.getId() != null) {
            examDeviceBaseDto =
                    examDeviceBaseDtoService.getById(saveExamDeviceChargeItemListBto.getId());
        }
        SaveExamDeviceChargeItemListBoResult boResult =
                super.saveExamDeviceChargeItemListBase(saveExamDeviceChargeItemListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto */
        {
            for (SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto bto :
                    boResult.<SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto>getBtoOfType(
                            SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto.class)) {
                UpdatedBto<
                                SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto,
                                ExamDeviceChargeItem,
                                ExamDeviceChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto,
                                ExamDeviceChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    if (examDeviceBaseDto != null && CollUtil.isNotEmpty(bo.getCampusIdList())) {
                        if (!examDeviceBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查设备适用院区外的值");
                        }
                    }
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (examDeviceBaseDto != null && CollUtil.isNotEmpty(bo.getCampusIdList())) {
                        if (!examDeviceBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查设备适用院区外的值");
                        }
                    }
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ExamDeviceChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(ExamDeviceChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveExamDeviceChargeItemListBto */
        {
            SaveExamDeviceChargeItemListBto bto =
                    boResult
                            .<SaveExamDeviceChargeItemListBto>getBtoOfType(
                                    SaveExamDeviceChargeItemListBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveExamDeviceChargeItemListBto, ExamDevice, ExamDeviceBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveExamDeviceChargeItemListBto, ExamDeviceBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamDeviceBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamDevice entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamDeviceBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查设备 */
    @PublicInterface(id = "bf64cd3d-e270-434f-9e5e-1b7fe6d8b944", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "9729af19-0422-4667-a838-70cfa79946b7")
    public String createExamDevice(@Valid @NotNull CreateExamDeviceBto createExamDeviceBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamDeviceBoResult boResult = super.createExamDeviceBase(createExamDeviceBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamDeviceBto */
        {
            CreateExamDeviceBto bto =
                    boResult.<CreateExamDeviceBto>getBtoOfType(CreateExamDeviceBto.class).stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateExamDeviceBto, ExamDeviceBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                ExamDeviceBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 CreateExamDeviceBto.ExamDeviceDocumentTemplateBto */
        {
            CreateExamDeviceBto.ExamDeviceDocumentTemplateBto bto =
                    boResult
                            .<CreateExamDeviceBto.ExamDeviceDocumentTemplateBto>getBtoOfType(
                                    CreateExamDeviceBto.ExamDeviceDocumentTemplateBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<
                            CreateExamDeviceBto.ExamDeviceDocumentTemplateBto,
                            ExamDeviceDocumentTemplateBO>
                    addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                ExamDeviceDocumentTemplateBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 CreateExamDeviceBto.ExamDeviceChargeItemBto */
        {
            for (CreateExamDeviceBto.ExamDeviceChargeItemBto bto :
                    boResult.<CreateExamDeviceBto.ExamDeviceChargeItemBto>getBtoOfType(
                            CreateExamDeviceBto.ExamDeviceChargeItemBto.class)) {
                AddedBto<CreateExamDeviceBto.ExamDeviceChargeItemBto, ExamDeviceChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (createExamDeviceBto != null && CollUtil.isNotEmpty(bo.getCampusIdList())) {
                        if (!createExamDeviceBto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查设备适用院区外的值");
                        }
                    }
                }
            }
        }
        /** 处理 CreateExamDeviceBto.ExamDeviceOrderLimitBto */
        {
            for (CreateExamDeviceBto.ExamDeviceOrderLimitBto bto :
                    boResult.<CreateExamDeviceBto.ExamDeviceOrderLimitBto>getBtoOfType(
                            CreateExamDeviceBto.ExamDeviceOrderLimitBto.class)) {
                AddedBto<CreateExamDeviceBto.ExamDeviceOrderLimitBto, ExamDeviceOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改检查设备开单限制停用标识 */
    @PublicInterface(id = "eb452fd3-8159-4d1a-a80b-af17fdbecc70", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "aa70bd91-7213-48b4-8263-0f0332935514")
    public String changeExamDeviceOrderLimitEnableFlag(
            @Valid @NotNull
                    ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto
                            examDeviceOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ChangeExamDeviceOrderLimitEnableFlagBto changeExamDeviceOrderLimitEnableFlagBto =
                new ChangeExamDeviceOrderLimitEnableFlagBto();
        changeExamDeviceOrderLimitEnableFlagBto.setExamDeviceOrderLimitBtoList(
                List.of(examDeviceOrderLimitBto));
        ExamDeviceOrderLimitBaseDto examDeviceOrderLimitBaseDto =
                examDeviceOrderLimitBaseDtoService.getById(examDeviceOrderLimitBto.getId());
        if (examDeviceOrderLimitBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceOrderLimitBaseDto.getExamDeviceId());
        changeExamDeviceOrderLimitEnableFlagBto.setId(examDeviceBaseDto.getId());
        ChangeExamDeviceOrderLimitEnableFlagBoResult boResult =
                super.changeExamDeviceOrderLimitEnableFlagBase(
                        changeExamDeviceOrderLimitEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto */
        {
            for (ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto bto :
                    boResult
                            .<ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto>
                                    getBtoOfType(
                                            ChangeExamDeviceOrderLimitEnableFlagBto
                                                    .ExamDeviceOrderLimitBto.class)) {
                UpdatedBto<
                                ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto,
                                ExamDeviceOrderLimit,
                                ExamDeviceOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改检查设备收费项目启用标记 */
    @PublicInterface(id = "7d9a7c0e-f71a-4832-8272-f71998e52a82", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b6404c09-d1c5-41aa-8b48-def6004994a5")
    public String changeExamDeviceChargeItemEnableFlag(
            @Valid @NotNull
                    ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto
                            examDeviceChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ChangeExamDeviceChargeItemEnableFlagBto changeExamDeviceChargeItemEnableFlagBto =
                new ChangeExamDeviceChargeItemEnableFlagBto();
        changeExamDeviceChargeItemEnableFlagBto.setExamDeviceChargeItemBtoList(
                List.of(examDeviceChargeItemBto));
        ExamDeviceChargeItemBaseDto examDeviceChargeItemBaseDto =
                examDeviceChargeItemBaseDtoService.getById(examDeviceChargeItemBto.getId());
        if (examDeviceChargeItemBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceChargeItemBaseDto.getExamDeviceId());
        changeExamDeviceChargeItemEnableFlagBto.setId(examDeviceBaseDto.getId());
        ChangeExamDeviceChargeItemEnableFlagBoResult boResult =
                super.changeExamDeviceChargeItemEnableFlagBase(
                        changeExamDeviceChargeItemEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto */
        {
            for (ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto bto :
                    boResult
                            .<ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto>
                                    getBtoOfType(
                                            ChangeExamDeviceChargeItemEnableFlagBto
                                                    .ExamDeviceChargeItemBto.class)) {
                UpdatedBto<
                                ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto,
                                ExamDeviceChargeItem,
                                ExamDeviceChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查设备文书模板 */
    @PublicInterface(id = "68c6f1ca-6091-48b8-9c94-016152031dfc", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b8fb9e78-a064-4261-bd94-2998ac478692")
    public String saveExamDeviceDocumentTemplate(
            @Valid @NotNull
                    SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto
                            examDeviceDocumentTemplateBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto =
                new SaveExamDeviceDocumentTemplateBto();
        saveExamDeviceDocumentTemplateBto.setExamDeviceDocumentTemplateBto(
                examDeviceDocumentTemplateBto);
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceDocumentTemplateBto.getExamDeviceId());
        saveExamDeviceDocumentTemplateBto.setId(examDeviceBaseDto.getId());
        SaveExamDeviceDocumentTemplateBoResult boResult =
                super.saveExamDeviceDocumentTemplateBase(saveExamDeviceDocumentTemplateBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto */
        {
            SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto bto =
                    boResult
                            .<SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto>
                                    getBtoOfType(
                                            SaveExamDeviceDocumentTemplateBto
                                                    .ExamDeviceDocumentTemplateBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto,
                            ExamDeviceDocumentTemplate,
                            ExamDeviceDocumentTemplateBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<
                            SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto,
                            ExamDeviceDocumentTemplateBO>
                    addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamDeviceDocumentTemplateBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamDeviceDocumentTemplate entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamDeviceDocumentTemplateBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查项目收费项目 */
    @PublicInterface(id = "9bf4c258-89e6-42db-a072-e077e9c74a5c", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "be9e7c9c-51a0-4905-842e-9ba464ccf780")
    public String createExamDeviceChargeItem(
            @Valid @NotNull
                    CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto examDeviceChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto =
                new CreateExamDeviceChargeItemBto();
        createExamDeviceChargeItemBto.setExamDeviceChargeItemBtoList(
                List.of(examDeviceChargeItemBto));
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceChargeItemBto.getExamDeviceId());
        createExamDeviceChargeItemBto.setId(examDeviceBaseDto.getId());
        CreateExamDeviceChargeItemBoResult boResult =
                super.createExamDeviceChargeItemBase(createExamDeviceChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto */
        {
            for (CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto bto :
                    boResult.<CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto>getBtoOfType(
                            CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto.class)) {
                AddedBto<
                                CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                                ExamDeviceChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (examDeviceBaseDto != null) {
                        if (!examDeviceBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查设备适用院区外的值");
                        }
                    }
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新检查设备收费项目 */
    @PublicInterface(id = "2dfdd512-ef50-478b-b5b0-140ef7cad511", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "cb09aab2-4031-483a-bb33-296219bc1859")
    public String updateExamDeviceChargeItem(
            @Valid @NotNull
                    UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto examDeviceChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto =
                new UpdateExamDeviceChargeItemBto();
        updateExamDeviceChargeItemBto.setExamDeviceChargeItemBtoList(
                List.of(examDeviceChargeItemBto));
        ExamDeviceChargeItemBaseDto examDeviceChargeItemBaseDto =
                examDeviceChargeItemBaseDtoService.getById(examDeviceChargeItemBto.getId());
        if (examDeviceChargeItemBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(examDeviceChargeItemBaseDto.getExamDeviceId());
        updateExamDeviceChargeItemBto.setId(examDeviceBaseDto.getId());
        UpdateExamDeviceChargeItemBoResult boResult =
                super.updateExamDeviceChargeItemBase(updateExamDeviceChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto */
        {
            for (UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto bto :
                    boResult.<UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto>getBtoOfType(
                            UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto.class)) {
                UpdatedBto<
                                UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                                ExamDeviceChargeItem,
                                ExamDeviceChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    if (examDeviceBaseDto != null && CollUtil.isNotEmpty(bo.getCampusIdList())) {
                        if (!examDeviceBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查设备适用院区外的值");
                        }
                    }
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查设备开单限制 */
    @PublicInterface(id = "c960946a-8eed-47b3-b6ea-7f6c1b885f99", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "e2865df2-bc43-49dd-a139-0177089c9f95")
    public String saveExamDeviceOrderLimitList(
            @Valid @NotNull SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamDeviceBaseDto examDeviceBaseDto = null;
        if (saveExamDeviceOrderLimitListBto.getId() != null) {
            examDeviceBaseDto =
                    examDeviceBaseDtoService.getById(saveExamDeviceOrderLimitListBto.getId());
        }
        SaveExamDeviceOrderLimitListBoResult boResult =
                super.saveExamDeviceOrderLimitListBase(saveExamDeviceOrderLimitListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto */
        {
            for (SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto bto :
                    boResult.<SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto>getBtoOfType(
                            SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto.class)) {
                UpdatedBto<
                                SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto,
                                ExamDeviceOrderLimit,
                                ExamDeviceOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<
                                SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto,
                                ExamDeviceOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamDeviceOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamDeviceOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ExamDeviceOrderLimit> deletedEntityList =
                    boResult.getDeletedEntityList(ExamDeviceOrderLimit.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveExamDeviceOrderLimitListBto */
        {
            SaveExamDeviceOrderLimitListBto bto =
                    boResult
                            .<SaveExamDeviceOrderLimitListBto>getBtoOfType(
                                    SaveExamDeviceOrderLimitListBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveExamDeviceOrderLimitListBto, ExamDevice, ExamDeviceBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveExamDeviceOrderLimitListBto, ExamDeviceBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamDeviceBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamDevice entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamDeviceBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新检查设备 */
    @PublicInterface(id = "6ebc8026-a557-4eb2-afb6-f54353976f3d", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "fb21da45-b038-4387-bd3f-df5800b5f9e0")
    public String updateExamDevice(@Valid @NotNull UpdateExamDeviceBto updateExamDeviceBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamDeviceBaseDto examDeviceBaseDto =
                examDeviceBaseDtoService.getById(updateExamDeviceBto.getId());
        UpdateExamDeviceBoResult boResult = super.updateExamDeviceBase(updateExamDeviceBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamDeviceBto */
        {
            UpdateExamDeviceBto bto =
                    boResult.<UpdateExamDeviceBto>getBtoOfType(UpdateExamDeviceBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateExamDeviceBto, ExamDevice, ExamDeviceBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamDeviceBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamDevice entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
