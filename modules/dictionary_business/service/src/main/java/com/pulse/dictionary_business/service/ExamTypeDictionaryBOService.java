package com.pulse.dictionary_business.service;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeOrderLimitBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamTypeChargeItem;
import com.pulse.dictionary_business.persist.dos.ExamTypeDictionary;
import com.pulse.dictionary_business.persist.dos.ExamTypeDocumentTemplate;
import com.pulse.dictionary_business.persist.dos.ExamTypeOrderLimit;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeChargeItemEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeDictionaryEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeOrderLimitEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeDictionaryBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeOrderLimitBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeChargeItemListBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeDocumentTemplateBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeOrderLimitListBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeChargeItemBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeDictionaryBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeOrderLimitBto;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "dba6b71b-3e7f-4e5a-b1bd-d7fd4be83fdc|BO|SERVICE")
public class ExamTypeDictionaryBOService extends BaseExamTypeDictionaryBOService {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemBaseDtoService examTypeChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoService examTypeDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeOrderLimitBaseDtoService examTypeOrderLimitBaseDtoService;

    /** 修改检查类型开单限制停用标识 */
    @PublicInterface(id = "a533680d-2e6b-45da-b5b1-69d2cc8a0b8c", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "1d244286-b253-4932-afc2-02627e181a5b")
    public String changeExamTypeOrderLimitEnableFlag(
            @Valid @NotNull
                    ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto
                            examTypeOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ChangeExamTypeOrderLimitEnableFlagBto changeExamTypeOrderLimitEnableFlagBto =
                new ChangeExamTypeOrderLimitEnableFlagBto();
        changeExamTypeOrderLimitEnableFlagBto.setExamTypeOrderLimitBtoList(
                List.of(examTypeOrderLimitBto));
        ExamTypeOrderLimitBaseDto examTypeOrderLimitBaseDto =
                examTypeOrderLimitBaseDtoService.getById(examTypeOrderLimitBto.getId());
        if (examTypeOrderLimitBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeOrderLimitBaseDto.getExamTypeId());
        changeExamTypeOrderLimitEnableFlagBto.setId(examTypeDictionaryBaseDto.getId());
        ChangeExamTypeOrderLimitEnableFlagBoResult boResult =
                super.changeExamTypeOrderLimitEnableFlagBase(changeExamTypeOrderLimitEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto */
        {
            for (ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto bto :
                    boResult
                            .<ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto>
                                    getBtoOfType(
                                            ChangeExamTypeOrderLimitEnableFlagBto
                                                    .ExamTypeOrderLimitBto.class)) {
                UpdatedBto<
                                ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto,
                                ExamTypeOrderLimit,
                                ExamTypeOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改检查类型字典启用标识 */
    @PublicInterface(id = "6215a05d-18db-46e6-b526-22e531a4ed2b", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "36011a56-379a-49c2-8ac4-de1e1bf190a8")
    public String changeExamTypeDictionaryEnableFlag(
            @Valid @NotNull
                    ChangeExamTypeDictionaryEnableFlagBto changeExamTypeDictionaryEnableFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(
                        changeExamTypeDictionaryEnableFlagBto.getId());
        ChangeExamTypeDictionaryEnableFlagBoResult boResult =
                super.changeExamTypeDictionaryEnableFlagBase(changeExamTypeDictionaryEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamTypeDictionaryEnableFlagBto */
        {
            ChangeExamTypeDictionaryEnableFlagBto bto =
                    boResult
                            .<ChangeExamTypeDictionaryEnableFlagBto>getBtoOfType(
                                    ChangeExamTypeDictionaryEnableFlagBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            ChangeExamTypeDictionaryEnableFlagBto,
                            ExamTypeDictionary,
                            ExamTypeDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamTypeDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改检查类型收费项目启用标识 */
    @PublicInterface(id = "d495229a-b280-4a57-9079-c2422033671e", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "37908edd-6cf5-4678-bb14-75a4915a639a")
    public String changeExamTypeChargeItemEnableFlag(
            @Valid @NotNull
                    ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto
                            examTypeChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ChangeExamTypeChargeItemEnableFlagBto changeExamTypeChargeItemEnableFlagBto =
                new ChangeExamTypeChargeItemEnableFlagBto();
        changeExamTypeChargeItemEnableFlagBto.setExamTypeChargeItemBtoList(
                List.of(examTypeChargeItemBto));
        ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto =
                examTypeChargeItemBaseDtoService.getById(examTypeChargeItemBto.getId());
        if (examTypeChargeItemBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeChargeItemBaseDto.getExamTypeId());
        changeExamTypeChargeItemEnableFlagBto.setId(examTypeDictionaryBaseDto.getId());
        ChangeExamTypeChargeItemEnableFlagBoResult boResult =
                super.changeExamTypeChargeItemEnableFlagBase(changeExamTypeChargeItemEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto */
        {
            for (ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto bto :
                    boResult
                            .<ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto>
                                    getBtoOfType(
                                            ChangeExamTypeChargeItemEnableFlagBto
                                                    .ExamTypeChargeItemBto.class)) {
                UpdatedBto<
                                ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto,
                                ExamTypeChargeItem,
                                ExamTypeChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查类型文书模板 */
    @PublicInterface(id = "4aa95908-1168-43bb-8436-773593f430c3", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "3d961d3b-9f27-460a-8357-c0e411c39621")
    public String saveExamTypeDocumentTemplate(
            @Valid @NotNull
                    SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto
                            examTypeDocumentTemplateBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto =
                new SaveExamTypeDocumentTemplateBto();
        saveExamTypeDocumentTemplateBto.setExamTypeDocumentTemplateBto(examTypeDocumentTemplateBto);
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(
                        examTypeDocumentTemplateBto.getExamTypeId());
        saveExamTypeDocumentTemplateBto.setId(examTypeDictionaryBaseDto.getId());
        SaveExamTypeDocumentTemplateBoResult boResult =
                super.saveExamTypeDocumentTemplateBase(saveExamTypeDocumentTemplateBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto */
        {
            SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto bto =
                    boResult
                            .<SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto>
                                    getBtoOfType(
                                            SaveExamTypeDocumentTemplateBto
                                                    .ExamTypeDocumentTemplateBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto,
                            ExamTypeDocumentTemplate,
                            ExamTypeDocumentTemplateBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<
                            SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto,
                            ExamTypeDocumentTemplateBO>
                    addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamTypeDocumentTemplateBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamTypeDocumentTemplate entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamTypeDocumentTemplateBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新检查类型信息 */
    @PublicInterface(id = "597d3ff1-bb0e-4b03-9362-ae4db9baa67d", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "4ed90d46-34bb-4c54-89e9-d4e511aa2f6b")
    public String updateExamTypeDictionary(
            @Valid @NotNull UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(updateExamTypeDictionaryBto.getId());
        UpdateExamTypeDictionaryBoResult boResult =
                super.updateExamTypeDictionaryBase(updateExamTypeDictionaryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamTypeDictionaryBto */
        {
            UpdateExamTypeDictionaryBto bto =
                    boResult
                            .<UpdateExamTypeDictionaryBto>getBtoOfType(
                                    UpdateExamTypeDictionaryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateExamTypeDictionaryBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamTypeDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查类型开单限制 */
    @PublicInterface(id = "b3481634-4975-46ac-abef-cfcd33dc111f", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "6beb81a2-e93b-4ce5-b937-936dcbbc60ed")
    public String createExamTypeOrderLimit(
            @Valid @NotNull
                    CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto = new CreateExamTypeOrderLimitBto();
        createExamTypeOrderLimitBto.setExamTypeOrderLimitBtoList(List.of(examTypeOrderLimitBto));
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeOrderLimitBto.getExamTypeId());
        createExamTypeOrderLimitBto.setId(examTypeDictionaryBaseDto.getId());
        CreateExamTypeOrderLimitBoResult boResult =
                super.createExamTypeOrderLimitBase(createExamTypeOrderLimitBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto */
        {
            for (CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto bto :
                    boResult.<CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto>getBtoOfType(
                            CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto.class)) {
                AddedBto<CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查类型收费项目（追加新增） */
    @PublicInterface(id = "f32e3e15-6a31-428c-964c-ccd6c1dacb21", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "6deb4a36-eb7b-43be-a785-0ac4fc195d7b")
    public String createExamTypeChargeItem(
            @Valid @NotNull
                    CreateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamTypeChargeItemBto createExamTypeChargeItemBto = new CreateExamTypeChargeItemBto();
        createExamTypeChargeItemBto.setExamTypeChargeItemBtoList(List.of(examTypeChargeItemBto));
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeChargeItemBto.getExamTypeId());
        createExamTypeChargeItemBto.setId(examTypeDictionaryBaseDto.getId());
        CreateExamTypeChargeItemBoResult boResult =
                super.createExamTypeChargeItemBase(createExamTypeChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamTypeChargeItemBto.ExamTypeChargeItemBto */
        {
            for (CreateExamTypeChargeItemBto.ExamTypeChargeItemBto bto :
                    boResult.<CreateExamTypeChargeItemBto.ExamTypeChargeItemBto>getBtoOfType(
                            CreateExamTypeChargeItemBto.ExamTypeChargeItemBto.class)) {
                AddedBto<CreateExamTypeChargeItemBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查类型开单限制 */
    @PublicInterface(id = "27b8e720-8a2f-4ba2-b597-e8072ecb04ac", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "927dd9b0-808f-4abe-a1ce-d42112ddeaa3")
    public String saveExamTypeOrderLimitList(
            @Valid @NotNull SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto = null;
        if (saveExamTypeOrderLimitListBto.getId() != null) {
            examTypeDictionaryBaseDto =
                    examTypeDictionaryBaseDtoService.getById(saveExamTypeOrderLimitListBto.getId());
        }
        SaveExamTypeOrderLimitListBoResult boResult =
                super.saveExamTypeOrderLimitListBase(saveExamTypeOrderLimitListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto */
        {
            for (SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto bto :
                    boResult.<SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto>getBtoOfType(
                            SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto.class)) {
                UpdatedBto<
                                SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto,
                                ExamTypeOrderLimit,
                                ExamTypeOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ExamTypeOrderLimit> deletedEntityList =
                    boResult.getDeletedEntityList(ExamTypeOrderLimit.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveExamTypeOrderLimitListBto */
        {
            SaveExamTypeOrderLimitListBto bto =
                    boResult
                            .<SaveExamTypeOrderLimitListBto>getBtoOfType(
                                    SaveExamTypeOrderLimitListBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveExamTypeOrderLimitListBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveExamTypeOrderLimitListBto, ExamTypeDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamTypeDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新检查类型开单限制 */
    @PublicInterface(id = "ca83d936-7cb4-4cc0-822d-7def989129d7", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "955c8f91-5bcf-4d34-adbb-ded502b6e661")
    public String updateExamTypeOrderLimit(
            @Valid @NotNull
                    UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto = new UpdateExamTypeOrderLimitBto();
        updateExamTypeOrderLimitBto.setExamTypeOrderLimitBtoList(List.of(examTypeOrderLimitBto));
        ExamTypeOrderLimitBaseDto examTypeOrderLimitBaseDto =
                examTypeOrderLimitBaseDtoService.getById(examTypeOrderLimitBto.getId());
        if (examTypeOrderLimitBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeOrderLimitBaseDto.getExamTypeId());
        updateExamTypeOrderLimitBto.setId(examTypeDictionaryBaseDto.getId());
        UpdateExamTypeOrderLimitBoResult boResult =
                super.updateExamTypeOrderLimitBase(updateExamTypeOrderLimitBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto */
        {
            for (UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto bto :
                    boResult.<UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto>getBtoOfType(
                            UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto.class)) {
                UpdatedBto<
                                UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto,
                                ExamTypeOrderLimit,
                                ExamTypeOrderLimitBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeOrderLimit entity = updatedBto.getEntity();
                    // 其他自定义操作...
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 保存检查类型收费项目列表 */
    @PublicInterface(id = "1ef68167-d516-4218-9a9c-aa074b64e56e", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "c11eb555-7484-42d5-b671-b11aa64e176e")
    public String saveExamTypeChargeItemList(
            @Valid @NotNull SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto = null;
        if (saveExamTypeChargeItemListBto.getId() != null) {
            examTypeDictionaryBaseDto =
                    examTypeDictionaryBaseDtoService.getById(saveExamTypeChargeItemListBto.getId());
        }
        SaveExamTypeChargeItemListBoResult boResult =
                super.saveExamTypeChargeItemListBase(saveExamTypeChargeItemListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto */
        {
            for (SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto bto :
                    boResult.<SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto>getBtoOfType(
                            SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto.class)) {
                UpdatedBto<
                                SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto,
                                ExamTypeChargeItem,
                                ExamTypeChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    if (examTypeDictionaryBaseDto != null) {
                        if (!examTypeDictionaryBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查类型适用院区外的值");
                        }
                    }
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (examTypeDictionaryBaseDto != null) {
                        if (!examTypeDictionaryBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查类型适用院区外的值");
                        }
                    }
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<ExamTypeChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(ExamTypeChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 SaveExamTypeChargeItemListBto */
        {
            SaveExamTypeChargeItemListBto bto =
                    boResult
                            .<SaveExamTypeChargeItemListBto>getBtoOfType(
                                    SaveExamTypeChargeItemListBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<SaveExamTypeChargeItemListBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<SaveExamTypeChargeItemListBto, ExamTypeDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                ExamTypeDictionary entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建检查类型字典 */
    @PublicInterface(id = "04ed6d54-2c49-4fb0-b9a1-13d49f9e70dd", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "dd7d1ff5-89b1-45a9-8406-7435addcd49e")
    public String createExamTypeDictionary(
            @Valid @NotNull CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {

        createExamTypeDictionaryBto.setEnableFlag(Boolean.TRUE);
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateExamTypeDictionaryBoResult boResult =
                super.createExamTypeDictionaryBase(createExamTypeDictionaryBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateExamTypeDictionaryBto */
        {
            CreateExamTypeDictionaryBto bto =
                    boResult
                            .<CreateExamTypeDictionaryBto>getBtoOfType(
                                    CreateExamTypeDictionaryBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateExamTypeDictionaryBto, ExamTypeDictionaryBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                ExamTypeDictionaryBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto */
        {
            CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto bto =
                    boResult
                            .<CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto>getBtoOfType(
                                    CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<
                            CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto,
                            ExamTypeDocumentTemplateBO>
                    addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                ExamTypeDocumentTemplateBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto */
        {
            for (CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto bto :
                    boResult.<CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto>getBtoOfType(
                            CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto.class)) {
                AddedBto<
                                CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto,
                                ExamTypeMethodDictionaryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeMethodDictionaryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto */
        {
            for (CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto bto :
                    boResult.<CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto>getBtoOfType(
                            CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto.class)) {
                AddedBto<CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeOrderLimitBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto */
        {
            for (CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto bto :
                    boResult.<CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto>getBtoOfType(
                            CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto.class)) {
                AddedBto<
                                CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto,
                                ExamTypeBodyDictionaryBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeBodyDictionaryBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
        }
        /** 处理 CreateExamTypeDictionaryBto.ExamTypeChargeItemBto */
        {
            for (CreateExamTypeDictionaryBto.ExamTypeChargeItemBto bto :
                    boResult.<CreateExamTypeDictionaryBto.ExamTypeChargeItemBto>getBtoOfType(
                            CreateExamTypeDictionaryBto.ExamTypeChargeItemBto.class)) {
                AddedBto<CreateExamTypeDictionaryBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (created) { // getAddedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                    if (createExamTypeDictionaryBto != null) {
                        if (!createExamTypeDictionaryBto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查类型适用院区外的值");
                        }
                    }
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新检查类型收费项目 */
    @PublicInterface(id = "2512118c-4b16-46f9-b755-c1d8ec076553", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "ed15b43c-ee8a-4450-a354-902a6fceeb8e")
    public String updateExamTypeChargeItem(
            @Valid @NotNull
                    UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto = new UpdateExamTypeChargeItemBto();
        updateExamTypeChargeItemBto.setExamTypeChargeItemBtoList(List.of(examTypeChargeItemBto));
        ExamTypeChargeItemBaseDto examTypeChargeItemBaseDto =
                examTypeChargeItemBaseDtoService.getById(examTypeChargeItemBto.getId());
        if (examTypeChargeItemBaseDto == null) {
            throw new IgnoredException(400, "参数错误");
        }
        ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto =
                examTypeDictionaryBaseDtoService.getById(examTypeChargeItemBaseDto.getExamTypeId());
        updateExamTypeChargeItemBto.setId(examTypeDictionaryBaseDto.getId());
        UpdateExamTypeChargeItemBoResult boResult =
                super.updateExamTypeChargeItemBase(updateExamTypeChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto */
        {
            for (UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto bto :
                    boResult.<UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto>getBtoOfType(
                            UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto.class)) {
                UpdatedBto<
                                UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto,
                                ExamTypeChargeItem,
                                ExamTypeChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    ExamTypeChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    ExamTypeChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                    if (examTypeDictionaryBaseDto != null) {
                        if (!examTypeDictionaryBaseDto
                                .getCampusIdList()
                                .containsAll(bo.getCampusIdList())) {
                            throw new IgnoredException(
                                    ErrorCode.WRONG_PARAMETER, "收费项目适用院区不允许选择检查类型适用院区外的值");
                        }
                    }
                }
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
