package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.ClinicItemDictionaryBO;
import com.pulse.dictionary_business.persist.dos.ClinicItemChargeItem;
import com.pulse.dictionary_business.persist.dos.ClinicItemDictionary;
import com.pulse.dictionary_business.persist.dos.ClinicItemGuideDescription;
import com.pulse.dictionary_business.persist.dos.ClinicItemPerformDepartment;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.ChangeClinicItemAuditFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.ChangeClinicItemGuideDescriptionEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.CreateClinicItemChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.CreateClinicItemDictionaryBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.CreateClinicItemGuideDescriptionBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.EnableClinicItemBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.SaveClinicItemChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.SaveClinicItemGuideDescriptionBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.UpdateClinicItemDetailBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.UpdateClinicItemDictionaryBoResult;
import com.pulse.dictionary_business.service.base.BaseClinicItemDictionaryBOService.UpdateClinicItemGuideDescriptionBoResult;
import com.pulse.dictionary_business.service.bto.ChangeClinicItemAuditFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeClinicItemGuideDescriptionEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemDictionaryBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.EnableClinicItemBto;
import com.pulse.dictionary_business.service.bto.SaveClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.SaveClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemDetailBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemDictionaryBto;
import com.pulse.dictionary_business.service.bto.UpdateClinicItemGuideDescriptionBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "4a946048-c2a4-394f-b591-04fd7bd293b7")
public class BaseClinicItemDictionaryBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 更改诊疗项目审核状态 */
    @AutoGenerated(locked = true)
    protected ChangeClinicItemAuditFlagBoResult changeClinicItemAuditFlagBase(
            ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
        if (changeClinicItemAuditFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeClinicItemAuditFlagBoResult boResult = new ChangeClinicItemAuditFlagBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateChangeClinicItemAuditFlagOnMissThrowEx(
                        boResult, changeClinicItemAuditFlagBto);
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 更改导医说明停用表示 */
    @AutoGenerated(locked = true)
    protected ChangeClinicItemGuideDescriptionEnableFlagBoResult
            changeClinicItemGuideDescriptionEnableFlagBase(
                    ChangeClinicItemGuideDescriptionEnableFlagBto
                            changeClinicItemGuideDescriptionEnableFlagBto) {
        if (changeClinicItemGuideDescriptionEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeClinicItemGuideDescriptionEnableFlagBoResult boResult =
                new ChangeClinicItemGuideDescriptionEnableFlagBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateChangeClinicItemGuideDescriptionEnableFlagOnMissThrowEx(
                        boResult, changeClinicItemGuideDescriptionEnableFlagBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemGuideDescriptionEnableFlagBto,
                                    "__$validPropertySet"),
                    "clinicItemGuideDescriptionBtoList")) {
                updateClinicItemGuideDescriptionBtoOnMissThrowEx(
                        boResult,
                        changeClinicItemGuideDescriptionEnableFlagBto,
                        clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 创建诊疗项目对应收费项目 */
    @AutoGenerated(locked = true)
    protected CreateClinicItemChargeItemBoResult createClinicItemChargeItemBase(
            CreateClinicItemChargeItemBto createClinicItemChargeItemBto) {
        if (createClinicItemChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateClinicItemChargeItemBoResult boResult = new CreateClinicItemChargeItemBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateCreateClinicItemChargeItemOnMissThrowEx(
                        boResult, createClinicItemChargeItemBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemChargeItemBto, "__$validPropertySet"),
                    "clinicItemChargeItemBtoList")) {
                createClinicItemChargeItemBto(
                        boResult, createClinicItemChargeItemBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 创建对象ClinicItemChargeItemBto */
    @AutoGenerated(locked = true)
    private void createClinicItemChargeItemBto(
            CreateClinicItemChargeItemBoResult boResult,
            CreateClinicItemChargeItemBto createClinicItemChargeItemBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createClinicItemChargeItemBto.getClinicItemChargeItemBtoList())) {
            for (CreateClinicItemChargeItemBto.ClinicItemChargeItemBto item :
                    createClinicItemChargeItemBto.getClinicItemChargeItemBtoList()) {
                ClinicItemChargeItemBO subBo = new ClinicItemChargeItemBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemId")) {
                    subBo.setChargeItemId(item.getChargeItemId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemCount")) {
                    subBo.setChargeItemCount(item.getChargeItemCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "filmFeeType")) {
                    subBo.setFilmFeeType(item.getFilmFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "graphicFeeFlag")) {
                    subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "digitalImagingFeeFlag")) {
                    subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "firstTimeBillingFlag")) {
                    subBo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "performDepartmentId")) {
                    subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "allowModifyCountFlag")) {
                    subBo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "clinicItemBillingType")) {
                    subBo.setClinicItemBillingType(item.getClinicItemBillingType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "clinicInsuranceCode")) {
                    subBo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                }
                subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("clinic_item_charge_item")));
                clinicItemDictionaryBO.getClinicItemChargeItemBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ClinicItemChargeItemBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createClinicItemChargeItemBtoOnDuplicateThrowEx(
            BaseClinicItemDictionaryBOService.CreateClinicItemDictionaryBoResult boResult,
            CreateClinicItemDictionaryBto createClinicItemDictionaryBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createClinicItemDictionaryBto.getClinicItemChargeItemBtoList())) {
            for (CreateClinicItemDictionaryBto.ClinicItemChargeItemBto item :
                    createClinicItemDictionaryBto.getClinicItemChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemChargeItemBO> any =
                        clinicItemDictionaryBO.getClinicItemChargeItemBOSet().stream()
                                .filter(
                                        clinicItemChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "clinic_item_charge_item");
                        throw new IgnoredException(400, "诊疗项目收费项目已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "clinic_item_charge_item",
                                any.get().getId());
                        throw new IgnoredException(400, "诊疗项目收费项目已存在");
                    }
                } else {
                    ClinicItemChargeItemBO subBo = new ClinicItemChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firstTimeBillingFlag")) {
                        subBo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "allowModifyCountFlag")) {
                        subBo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicItemBillingType")) {
                        subBo.setClinicItemBillingType(item.getClinicItemBillingType());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("clinic_item_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createClinicItemChargeItemBtoOnDuplicateUpdate(
            BaseClinicItemDictionaryBOService.SaveClinicItemChargeItemBoResult boResult,
            SaveClinicItemChargeItemBto saveClinicItemChargeItemBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isEmpty(saveClinicItemChargeItemBto.getClinicItemChargeItemBtoList())) {
            saveClinicItemChargeItemBto.setClinicItemChargeItemBtoList(List.of());
        }
        clinicItemDictionaryBO
                .getClinicItemChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveClinicItemChargeItemBto
                                            .getClinicItemChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    clinicItemChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (clinicItemChargeItemBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            clinicItemChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToClinicItemChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveClinicItemChargeItemBto.getClinicItemChargeItemBtoList())) {
            for (SaveClinicItemChargeItemBto.ClinicItemChargeItemBto item :
                    saveClinicItemChargeItemBto.getClinicItemChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemChargeItemBO> any =
                        clinicItemDictionaryBO.getClinicItemChargeItemBOSet().stream()
                                .filter(
                                        clinicItemChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ClinicItemChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firstTimeBillingFlag")) {
                            bo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "allowModifyCountFlag")) {
                            bo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicItemBillingType")) {
                            bo.setClinicItemBillingType(item.getClinicItemBillingType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                    } else {
                        ClinicItemChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firstTimeBillingFlag")) {
                            bo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "allowModifyCountFlag")) {
                            bo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicItemBillingType")) {
                            bo.setClinicItemBillingType(item.getClinicItemBillingType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                    }
                } else {
                    ClinicItemChargeItemBO subBo = new ClinicItemChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firstTimeBillingFlag")) {
                        subBo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "allowModifyCountFlag")) {
                        subBo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicItemBillingType")) {
                        subBo.setClinicItemBillingType(item.getClinicItemBillingType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicInsuranceCode")) {
                        subBo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("clinic_item_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createClinicItemChargeItemBtoOnDuplicateUpdate(
            UpdateClinicItemDetailBoResult boResult,
            UpdateClinicItemDetailBto updateClinicItemDetailBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isEmpty(updateClinicItemDetailBto.getClinicItemChargeItemBtoList())) {
            updateClinicItemDetailBto.setClinicItemChargeItemBtoList(List.of());
        }
        clinicItemDictionaryBO
                .getClinicItemChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    updateClinicItemDetailBto
                                            .getClinicItemChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    clinicItemChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (clinicItemChargeItemBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            clinicItemChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToClinicItemChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(updateClinicItemDetailBto.getClinicItemChargeItemBtoList())) {
            for (UpdateClinicItemDetailBto.ClinicItemChargeItemBto item :
                    updateClinicItemDetailBto.getClinicItemChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemChargeItemBO> any =
                        clinicItemDictionaryBO.getClinicItemChargeItemBOSet().stream()
                                .filter(
                                        clinicItemChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ClinicItemChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firstTimeBillingFlag")) {
                            bo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "allowModifyCountFlag")) {
                            bo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicItemBillingType")) {
                            bo.setClinicItemBillingType(item.getClinicItemBillingType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                    } else {
                        ClinicItemChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firstTimeBillingFlag")) {
                            bo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "allowModifyCountFlag")) {
                            bo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicItemBillingType")) {
                            bo.setClinicItemBillingType(item.getClinicItemBillingType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clinicInsuranceCode")) {
                            bo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                        }
                    }
                } else {
                    ClinicItemChargeItemBO subBo = new ClinicItemChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firstTimeBillingFlag")) {
                        subBo.setFirstTimeBillingFlag(item.getFirstTimeBillingFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "allowModifyCountFlag")) {
                        subBo.setAllowModifyCountFlag(item.getAllowModifyCountFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicItemBillingType")) {
                        subBo.setClinicItemBillingType(item.getClinicItemBillingType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clinicInsuranceCode")) {
                        subBo.setClinicInsuranceCode(item.getClinicInsuranceCode());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("clinic_item_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建诊疗项目字典 */
    @AutoGenerated(locked = true)
    protected CreateClinicItemDictionaryBoResult createClinicItemDictionaryBase(
            CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
        if (createClinicItemDictionaryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateClinicItemDictionaryBoResult boResult = new CreateClinicItemDictionaryBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                createCreateClinicItemDictionaryOnDuplicateThrowEx(
                        boResult, createClinicItemDictionaryBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemChargeItemBtoList")) {
                createClinicItemChargeItemBtoOnDuplicateThrowEx(
                        boResult, createClinicItemDictionaryBto, clinicItemDictionaryBO);
            }
        }
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemPerformDepartmentBtoList")) {
                createClinicItemPerformDepartmentBtoOnDuplicateThrowEx(
                        boResult, createClinicItemDictionaryBto, clinicItemDictionaryBO);
            }
        }
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemGuideDescriptionBtoList")) {
                createClinicItemGuideDescriptionBtoOnDuplicateThrowEx(
                        boResult, createClinicItemDictionaryBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 创建诊疗项目导医说明 */
    @AutoGenerated(locked = true)
    protected CreateClinicItemGuideDescriptionBoResult createClinicItemGuideDescriptionBase(
            CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto) {
        if (createClinicItemGuideDescriptionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateClinicItemGuideDescriptionBoResult boResult =
                new CreateClinicItemGuideDescriptionBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateCreateClinicItemGuideDescriptionOnMissThrowEx(
                        boResult, createClinicItemGuideDescriptionBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemGuideDescriptionBto, "__$validPropertySet"),
                    "clinicItemGuideDescriptionBtoList")) {
                createClinicItemGuideDescriptionBtoOnDuplicateThrowEx(
                        boResult, createClinicItemGuideDescriptionBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 创建对象:ClinicItemGuideDescriptionBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createClinicItemGuideDescriptionBtoOnDuplicateThrowEx(
            CreateClinicItemDictionaryBoResult boResult,
            CreateClinicItemDictionaryBto createClinicItemDictionaryBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createClinicItemDictionaryBto.getClinicItemGuideDescriptionBtoList())) {
            for (CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto item :
                    createClinicItemDictionaryBto.getClinicItemGuideDescriptionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemGuideDescriptionBO> any =
                        clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().stream()
                                .filter(
                                        clinicItemGuideDescriptionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemGuideDescriptionBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "clinic_item_guide_description");
                        throw new IgnoredException(400, "诊疗项目导医说明已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "clinic_item_guide_description",
                                any.get().getId());
                        throw new IgnoredException(400, "诊疗项目导医说明已存在");
                    }
                } else {
                    ClinicItemGuideDescriptionBO subBo = new ClinicItemGuideDescriptionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "itemType")) {
                        subBo.setItemType(item.getItemType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideUseScopeList")) {
                        subBo.setGuideUseScopeList(item.getGuideUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationType")) {
                        subBo.setOrganizationType(item.getOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        subBo.setOrderDepartmentId(item.getOrderDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "notice")) {
                        subBo.setNotice(item.getNotice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examInstruction")) {
                        subBo.setExamInstruction(item.getExamInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideInfo")) {
                        subBo.setGuideInfo(item.getGuideInfo());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "navigationAddress")) {
                        subBo.setNavigationAddress(item.getNavigationAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "clinic_item_guide_description")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemGuideDescriptionBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createClinicItemGuideDescriptionBtoOnDuplicateThrowEx(
            CreateClinicItemGuideDescriptionBoResult boResult,
            CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList())) {
            for (CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto item :
                    createClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemGuideDescriptionBO> any =
                        clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().stream()
                                .filter(
                                        clinicItemGuideDescriptionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemGuideDescriptionBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "clinic_item_guide_description");
                        throw new IgnoredException(400, "诊疗项目导医说明已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "clinic_item_guide_description",
                                any.get().getId());
                        throw new IgnoredException(400, "诊疗项目导医说明已存在");
                    }
                } else {
                    ClinicItemGuideDescriptionBO subBo = new ClinicItemGuideDescriptionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "itemType")) {
                        subBo.setItemType(item.getItemType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideUseScopeList")) {
                        subBo.setGuideUseScopeList(item.getGuideUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationType")) {
                        subBo.setOrganizationType(item.getOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "notice")) {
                        subBo.setNotice(item.getNotice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examInstruction")) {
                        subBo.setExamInstruction(item.getExamInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideInfo")) {
                        subBo.setGuideInfo(item.getGuideInfo());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "navigationAddress")) {
                        subBo.setNavigationAddress(item.getNavigationAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        subBo.setOrderDepartmentId(item.getOrderDepartmentId());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "clinic_item_guide_description")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemGuideDescriptionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createClinicItemGuideDescriptionBtoOnDuplicateUpdate(
            BaseClinicItemDictionaryBOService.SaveClinicItemGuideDescriptionBoResult boResult,
            SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isEmpty(
                saveClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList())) {
            saveClinicItemGuideDescriptionBto.setClinicItemGuideDescriptionBtoList(List.of());
        }
        clinicItemDictionaryBO
                .getClinicItemGuideDescriptionBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveClinicItemGuideDescriptionBto
                                            .getClinicItemGuideDescriptionBtoList()
                                            .stream()
                                            .filter(
                                                    clinicItemGuideDescriptionBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (clinicItemGuideDescriptionBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            clinicItemGuideDescriptionBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToClinicItemGuideDescription());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList())) {
            for (SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto item :
                    saveClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemGuideDescriptionBO> any =
                        clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().stream()
                                .filter(
                                        clinicItemGuideDescriptionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemGuideDescriptionBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ClinicItemGuideDescriptionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemGuideDescription());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "itemType")) {
                            bo.setItemType(item.getItemType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guideUseScopeList")) {
                            bo.setGuideUseScopeList(item.getGuideUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationType")) {
                            bo.setOrganizationType(item.getOrganizationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "notice")) {
                            bo.setNotice(item.getNotice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examInstruction")) {
                            bo.setExamInstruction(item.getExamInstruction());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guideInfo")) {
                            bo.setGuideInfo(item.getGuideInfo());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "navigationAddress")) {
                            bo.setNavigationAddress(item.getNavigationAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "orderDepartmentId")) {
                            bo.setOrderDepartmentId(item.getOrderDepartmentId());
                        }
                    } else {
                        ClinicItemGuideDescriptionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemGuideDescription());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "itemType")) {
                            bo.setItemType(item.getItemType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guideUseScopeList")) {
                            bo.setGuideUseScopeList(item.getGuideUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationType")) {
                            bo.setOrganizationType(item.getOrganizationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "notice")) {
                            bo.setNotice(item.getNotice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examInstruction")) {
                            bo.setExamInstruction(item.getExamInstruction());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guideInfo")) {
                            bo.setGuideInfo(item.getGuideInfo());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "navigationAddress")) {
                            bo.setNavigationAddress(item.getNavigationAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "orderDepartmentId")) {
                            bo.setOrderDepartmentId(item.getOrderDepartmentId());
                        }
                    }
                } else {
                    ClinicItemGuideDescriptionBO subBo = new ClinicItemGuideDescriptionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "itemType")) {
                        subBo.setItemType(item.getItemType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideUseScopeList")) {
                        subBo.setGuideUseScopeList(item.getGuideUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationType")) {
                        subBo.setOrganizationType(item.getOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "notice")) {
                        subBo.setNotice(item.getNotice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examInstruction")) {
                        subBo.setExamInstruction(item.getExamInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guideInfo")) {
                        subBo.setGuideInfo(item.getGuideInfo());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "navigationAddress")) {
                        subBo.setNavigationAddress(item.getNavigationAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        subBo.setOrderDepartmentId(item.getOrderDepartmentId());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "clinic_item_guide_description")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemPerformDepartmentBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createClinicItemPerformDepartmentBtoOnDuplicateThrowEx(
            CreateClinicItemDictionaryBoResult boResult,
            CreateClinicItemDictionaryBto createClinicItemDictionaryBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createClinicItemDictionaryBto.getClinicItemPerformDepartmentBtoList())) {
            for (CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto item :
                    createClinicItemDictionaryBto.getClinicItemPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemPerformDepartmentBO> any =
                        clinicItemDictionaryBO.getClinicItemPerformDepartmentBOSet().stream()
                                .filter(
                                        clinicItemPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "clinic_item_perform_department");
                        throw new IgnoredException(400, "诊疗项目执行科室已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "clinic_item_perform_department",
                                any.get().getId());
                        throw new IgnoredException(400, "诊疗项目执行科室已存在");
                    }
                } else {
                    ClinicItemPerformDepartmentBO subBo = new ClinicItemPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useOrganizationType")) {
                        subBo.setUseOrganizationType(item.getUseOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        subBo.setOrderDepartmentId(item.getOrderDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("clinic_item_perform_department"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemPerformDepartmentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ClinicItemPerformDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createClinicItemPerformDepartmentBtoOnDuplicateUpdate(
            UpdateClinicItemDetailBoResult boResult,
            UpdateClinicItemDetailBto updateClinicItemDetailBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isEmpty(
                updateClinicItemDetailBto.getClinicItemPerformDepartmentBtoList())) {
            updateClinicItemDetailBto.setClinicItemPerformDepartmentBtoList(List.of());
        }
        clinicItemDictionaryBO
                .getClinicItemPerformDepartmentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    updateClinicItemDetailBto
                                            .getClinicItemPerformDepartmentBtoList()
                                            .stream()
                                            .filter(
                                                    clinicItemPerformDepartmentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (clinicItemPerformDepartmentBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            clinicItemPerformDepartmentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToClinicItemPerformDepartment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                updateClinicItemDetailBto.getClinicItemPerformDepartmentBtoList())) {
            for (UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto item :
                    updateClinicItemDetailBto.getClinicItemPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ClinicItemPerformDepartmentBO> any =
                        clinicItemDictionaryBO.getClinicItemPerformDepartmentBOSet().stream()
                                .filter(
                                        clinicItemPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                clinicItemPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ClinicItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useOrganizationType")) {
                            bo.setUseOrganizationType(item.getUseOrganizationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "orderDepartmentId")) {
                            bo.setOrderDepartmentId(item.getOrderDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                    } else {
                        ClinicItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToClinicItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useOrganizationType")) {
                            bo.setUseOrganizationType(item.getUseOrganizationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "orderDepartmentId")) {
                            bo.setOrderDepartmentId(item.getOrderDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                    }
                } else {
                    ClinicItemPerformDepartmentBO subBo = new ClinicItemPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useOrganizationType")) {
                        subBo.setUseOrganizationType(item.getUseOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        subBo.setOrderDepartmentId(item.getOrderDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    subBo.setClinicItemDictionaryBO(clinicItemDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("clinic_item_perform_department"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    clinicItemDictionaryBO.getClinicItemPerformDepartmentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO createCreateClinicItemDictionaryOnDuplicateThrowEx(
            CreateClinicItemDictionaryBoResult boResult,
            CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createClinicItemDictionaryBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            createClinicItemDictionaryBto.getClinicItemId());
            if (clinicItemDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'clinic_item_id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (clinicItemDictionaryBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        clinicItemDictionaryBO.getClinicItemId(),
                        "clinic_item_dictionary");
                throw new IgnoredException(400, "临床诊疗项目字典已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "clinic_item_dictionary",
                        clinicItemDictionaryBO.getClinicItemId(),
                        "clinic_item_dictionary");
                throw new IgnoredException(400, "临床诊疗项目字典已存在");
            }
        } else {
            clinicItemDictionaryBO = new ClinicItemDictionaryBO();
            if (pkExist) {
                clinicItemDictionaryBO.setClinicItemId(
                        createClinicItemDictionaryBto.getClinicItemId());
            } else {
                clinicItemDictionaryBO.setClinicItemId(
                        String.valueOf(this.idGenerator.allocateId("clinic_item_dictionary")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "itemType")) {
                clinicItemDictionaryBO.setItemType(createClinicItemDictionaryBto.getItemType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemName")) {
                clinicItemDictionaryBO.setClinicItemName(
                        createClinicItemDictionaryBto.getClinicItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "inputCode")) {
                clinicItemDictionaryBO.setInputCode(createClinicItemDictionaryBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "remark")) {
                clinicItemDictionaryBO.setRemark(createClinicItemDictionaryBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "enableFlag")) {
                clinicItemDictionaryBO.setEnableFlag(createClinicItemDictionaryBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "institutionId")) {
                clinicItemDictionaryBO.setInstitutionId(
                        createClinicItemDictionaryBto.getInstitutionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "auditFlag")) {
                clinicItemDictionaryBO.setAuditFlag(createClinicItemDictionaryBto.getAuditFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "description")) {
                clinicItemDictionaryBO.setDescription(
                        createClinicItemDictionaryBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "limitGender")) {
                clinicItemDictionaryBO.setLimitGender(
                        createClinicItemDictionaryBto.getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "includeCurrentDepartmentFlag")) {
                clinicItemDictionaryBO.setIncludeCurrentDepartmentFlag(
                        createClinicItemDictionaryBto.getIncludeCurrentDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "rescueFlag")) {
                clinicItemDictionaryBO.setRescueFlag(createClinicItemDictionaryBto.getRescueFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "billingAttribute")) {
                clinicItemDictionaryBO.setBillingAttribute(
                        createClinicItemDictionaryBto.getBillingAttribute());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "specialNeedFlag")) {
                clinicItemDictionaryBO.setSpecialNeedFlag(
                        createClinicItemDictionaryBto.getSpecialNeedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "onlySelectSettingDepartmentFlag")) {
                clinicItemDictionaryBO.setOnlySelectSettingDepartmentFlag(
                        createClinicItemDictionaryBto.getOnlySelectSettingDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "operationCode")) {
                clinicItemDictionaryBO.setOperationCode(
                        createClinicItemDictionaryBto.getOperationCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "useScopeList")) {
                clinicItemDictionaryBO.setUseScopeList(
                        createClinicItemDictionaryBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "campusIdList")) {
                clinicItemDictionaryBO.setCampusIdList(
                        createClinicItemDictionaryBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "auditDate")) {
                clinicItemDictionaryBO.setAuditDate(createClinicItemDictionaryBto.getAuditDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "auditOperatorId")) {
                clinicItemDictionaryBO.setAuditOperatorId(
                        createClinicItemDictionaryBto.getAuditOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "ageMinLimit")) {
                clinicItemDictionaryBO.setAgeMinLimit(
                        createClinicItemDictionaryBto.getAgeMinLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "ageMaxLimit")) {
                clinicItemDictionaryBO.setAgeMaxLimit(
                        createClinicItemDictionaryBto.getAgeMaxLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemCatalogId")) {
                clinicItemDictionaryBO.setClinicItemCatalogId(
                        createClinicItemDictionaryBto.getClinicItemCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "standardCode")) {
                clinicItemDictionaryBO.setStandardCode(
                        createClinicItemDictionaryBto.getStandardCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "frequency")) {
                clinicItemDictionaryBO.setFrequency(createClinicItemDictionaryBto.getFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "unit")) {
                clinicItemDictionaryBO.setUnit(createClinicItemDictionaryBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "printFlag")) {
                clinicItemDictionaryBO.setPrintFlag(createClinicItemDictionaryBto.getPrintFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "exclusionType")) {
                clinicItemDictionaryBO.setExclusionType(
                        createClinicItemDictionaryBto.getExclusionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "exclusionTime")) {
                clinicItemDictionaryBO.setExclusionTime(
                        createClinicItemDictionaryBto.getExclusionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "delayDays")) {
                clinicItemDictionaryBO.setDelayDays(createClinicItemDictionaryBto.getDelayDays());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "outpDefaultPerformDepartmentId")) {
                clinicItemDictionaryBO.setOutpDefaultPerformDepartmentId(
                        createClinicItemDictionaryBto.getOutpDefaultPerformDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "inpDefaultPerformDepartmentType")) {
                clinicItemDictionaryBO.setInpDefaultPerformDepartmentType(
                        createClinicItemDictionaryBto.getInpDefaultPerformDepartmentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "frequencyNotAllowedModifyFlag")) {
                clinicItemDictionaryBO.setFrequencyNotAllowedModifyFlag(
                        createClinicItemDictionaryBto.getFrequencyNotAllowedModifyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "doubleSignatureFlag")) {
                clinicItemDictionaryBO.setDoubleSignatureFlag(
                        createClinicItemDictionaryBto.getDoubleSignatureFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "limitWardIdList")) {
                clinicItemDictionaryBO.setLimitWardIdList(
                        createClinicItemDictionaryBto.getLimitWardIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "pdaPerformFlag")) {
                clinicItemDictionaryBO.setPdaPerformFlag(
                        createClinicItemDictionaryBto.getPdaPerformFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "cardPrintTypeList")) {
                clinicItemDictionaryBO.setCardPrintTypeList(
                        createClinicItemDictionaryBto.getCardPrintTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "orderFrequencyBillingType")) {
                clinicItemDictionaryBO.setOrderFrequencyBillingType(
                        createClinicItemDictionaryBto.getOrderFrequencyBillingType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "billingInterval")) {
                clinicItemDictionaryBO.setBillingInterval(
                        createClinicItemDictionaryBto.getBillingInterval());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "alias")) {
                clinicItemDictionaryBO.setAlias(createClinicItemDictionaryBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "createdBy")) {
                clinicItemDictionaryBO.setCreatedBy(createClinicItemDictionaryBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "updatedBy")) {
                clinicItemDictionaryBO.setUpdatedBy(createClinicItemDictionaryBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "itemSpecification")) {
                clinicItemDictionaryBO.setItemSpecification(
                        createClinicItemDictionaryBto.getItemSpecification());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createClinicItemDictionaryBto, "__$validPropertySet"),
                    "sortNumber")) {
                clinicItemDictionaryBO.setSortNumber(createClinicItemDictionaryBto.getSortNumber());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createClinicItemDictionaryBto);
            addedBto.setBo(clinicItemDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return clinicItemDictionaryBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO createSaveClinicItemChargeItemOnDuplicateUpdate(
            SaveClinicItemChargeItemBoResult boResult,
            SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveClinicItemChargeItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            saveClinicItemChargeItemBto.getClinicItemId());
            if (clinicItemDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'clinic_item_id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (clinicItemDictionaryBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
                updatedBto.setBto(saveClinicItemChargeItemBto);
                updatedBto.setBo(clinicItemDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
                updatedBto.setBto(saveClinicItemChargeItemBto);
                updatedBto.setBo(clinicItemDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            clinicItemDictionaryBO = new ClinicItemDictionaryBO();
            if (pkExist) {
                clinicItemDictionaryBO.setClinicItemId(
                        saveClinicItemChargeItemBto.getClinicItemId());
            } else {
                clinicItemDictionaryBO.setClinicItemId(
                        String.valueOf(this.idGenerator.allocateId("clinic_item_dictionary")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveClinicItemChargeItemBto);
            addedBto.setBo(clinicItemDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return clinicItemDictionaryBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO createSaveClinicItemGuideDescriptionOnDuplicateUpdate(
            SaveClinicItemGuideDescriptionBoResult boResult,
            SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveClinicItemGuideDescriptionBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            saveClinicItemGuideDescriptionBto.getClinicItemId());
            if (clinicItemDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'clinic_item_id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (clinicItemDictionaryBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
                updatedBto.setBto(saveClinicItemGuideDescriptionBto);
                updatedBto.setBo(clinicItemDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
                updatedBto.setBto(saveClinicItemGuideDescriptionBto);
                updatedBto.setBo(clinicItemDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            clinicItemDictionaryBO = new ClinicItemDictionaryBO();
            if (pkExist) {
                clinicItemDictionaryBO.setClinicItemId(
                        saveClinicItemGuideDescriptionBto.getClinicItemId());
            } else {
                clinicItemDictionaryBO.setClinicItemId(
                        String.valueOf(this.idGenerator.allocateId("clinic_item_dictionary")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveClinicItemGuideDescriptionBto);
            addedBto.setBo(clinicItemDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return clinicItemDictionaryBO;
    }

    /** 启用/停用诊疗项目 */
    @AutoGenerated(locked = true)
    protected EnableClinicItemBoResult enableClinicItemBase(
            EnableClinicItemBto enableClinicItemBto) {
        if (enableClinicItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        EnableClinicItemBoResult boResult = new EnableClinicItemBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateEnableClinicItemOnMissThrowEx(boResult, enableClinicItemBto);
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 保存诊疗项目收费项目 */
    @AutoGenerated(locked = true)
    protected SaveClinicItemChargeItemBoResult saveClinicItemChargeItemBase(
            SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
        if (saveClinicItemChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveClinicItemChargeItemBoResult boResult = new SaveClinicItemChargeItemBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                createSaveClinicItemChargeItemOnDuplicateUpdate(
                        boResult, saveClinicItemChargeItemBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveClinicItemChargeItemBto, "__$validPropertySet"),
                    "clinicItemChargeItemBtoList")) {
                createClinicItemChargeItemBtoOnDuplicateUpdate(
                        boResult, saveClinicItemChargeItemBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 保存诊疗项目导医说明 */
    @AutoGenerated(locked = true)
    protected SaveClinicItemGuideDescriptionBoResult saveClinicItemGuideDescriptionBase(
            SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
        if (saveClinicItemGuideDescriptionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveClinicItemGuideDescriptionBoResult boResult =
                new SaveClinicItemGuideDescriptionBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                createSaveClinicItemGuideDescriptionOnDuplicateUpdate(
                        boResult, saveClinicItemGuideDescriptionBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveClinicItemGuideDescriptionBto, "__$validPropertySet"),
                    "clinicItemGuideDescriptionBtoList")) {
                createClinicItemGuideDescriptionBtoOnDuplicateUpdate(
                        boResult, saveClinicItemGuideDescriptionBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 更新对象:changeClinicItemAuditFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateChangeClinicItemAuditFlagOnMissThrowEx(
            BaseClinicItemDictionaryBOService.ChangeClinicItemAuditFlagBoResult boResult,
            ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeClinicItemAuditFlagBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            changeClinicItemAuditFlagBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(changeClinicItemAuditFlagBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemAuditFlagBto, "__$validPropertySet"),
                    "auditFlag")) {
                clinicItemDictionaryBO.setAuditFlag(changeClinicItemAuditFlagBto.getAuditFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemAuditFlagBto, "__$validPropertySet"),
                    "auditDate")) {
                clinicItemDictionaryBO.setAuditDate(changeClinicItemAuditFlagBto.getAuditDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemAuditFlagBto, "__$validPropertySet"),
                    "auditOperatorId")) {
                clinicItemDictionaryBO.setAuditOperatorId(
                        changeClinicItemAuditFlagBto.getAuditOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemAuditFlagBto, "__$validPropertySet"),
                    "useScopeList")) {
                clinicItemDictionaryBO.setUseScopeList(
                        changeClinicItemAuditFlagBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeClinicItemAuditFlagBto, "__$validPropertySet"),
                    "campusIdList")) {
                clinicItemDictionaryBO.setCampusIdList(
                        changeClinicItemAuditFlagBto.getCampusIdList());
            }
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:changeClinicItemGuideDescriptionEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateChangeClinicItemGuideDescriptionEnableFlagOnMissThrowEx(
            ChangeClinicItemGuideDescriptionEnableFlagBoResult boResult,
            ChangeClinicItemGuideDescriptionEnableFlagBto
                    changeClinicItemGuideDescriptionEnableFlagBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeClinicItemGuideDescriptionEnableFlagBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            changeClinicItemGuideDescriptionEnableFlagBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(changeClinicItemGuideDescriptionEnableFlagBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return clinicItemDictionaryBO;
        }
    }

    /** 更新诊疗项目明细（包括收费项目、执行科室） */
    @AutoGenerated(locked = true)
    protected UpdateClinicItemDetailBoResult updateClinicItemDetailBase(
            UpdateClinicItemDetailBto updateClinicItemDetailBto) {
        if (updateClinicItemDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateClinicItemDetailBoResult boResult = new UpdateClinicItemDetailBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateUpdateClinicItemDetailOnMissThrowEx(boResult, updateClinicItemDetailBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "clinicItemChargeItemBtoList")) {
                createClinicItemChargeItemBtoOnDuplicateUpdate(
                        boResult, updateClinicItemDetailBto, clinicItemDictionaryBO);
            }
        }
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "clinicItemPerformDepartmentBtoList")) {
                createClinicItemPerformDepartmentBtoOnDuplicateUpdate(
                        boResult, updateClinicItemDetailBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 更新诊疗项目字典 */
    @AutoGenerated(locked = true)
    protected UpdateClinicItemDictionaryBoResult updateClinicItemDictionaryBase(
            UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
        if (updateClinicItemDictionaryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateClinicItemDictionaryBoResult boResult = new UpdateClinicItemDictionaryBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateUpdateClinicItemDictionaryOnMissThrowEx(
                        boResult, updateClinicItemDictionaryBto);
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 更新导医说明 */
    @AutoGenerated(locked = true)
    protected UpdateClinicItemGuideDescriptionBoResult updateClinicItemGuideDescriptionBase(
            UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto) {
        if (updateClinicItemGuideDescriptionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateClinicItemGuideDescriptionBoResult boResult =
                new UpdateClinicItemGuideDescriptionBoResult();
        ClinicItemDictionaryBO clinicItemDictionaryBO =
                updateUpdateClinicItemGuideDescriptionOnMissThrowEx(
                        boResult, updateClinicItemGuideDescriptionBto);
        if (clinicItemDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemGuideDescriptionBto, "__$validPropertySet"),
                    "clinicItemGuideDescriptionBtoList")) {
                updateClinicItemGuideDescriptionBtoOnMissThrowEx(
                        boResult, updateClinicItemGuideDescriptionBto, clinicItemDictionaryBO);
            }
        }
        boResult.setRootBo(clinicItemDictionaryBO);
        return boResult;
    }

    /** 更新对象:clinicItemGuideDescriptionBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateClinicItemGuideDescriptionBtoOnMissThrowEx(
            BaseClinicItemDictionaryBOService.ChangeClinicItemGuideDescriptionEnableFlagBoResult
                    boResult,
            ChangeClinicItemGuideDescriptionEnableFlagBto
                    changeClinicItemGuideDescriptionEnableFlagBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                changeClinicItemGuideDescriptionEnableFlagBto
                        .getClinicItemGuideDescriptionBtoList())) {
            for (ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto bto :
                    changeClinicItemGuideDescriptionEnableFlagBto
                            .getClinicItemGuideDescriptionBtoList()) {
                Optional<ClinicItemGuideDescriptionBO> any =
                        clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().stream()
                                .filter(
                                        clinicItemGuideDescriptionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                clinicItemGuideDescriptionBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ClinicItemGuideDescriptionBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToClinicItemGuideDescription());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:clinicItemGuideDescriptionBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateClinicItemGuideDescriptionBtoOnMissThrowEx(
            BaseClinicItemDictionaryBOService.UpdateClinicItemGuideDescriptionBoResult boResult,
            UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto,
            ClinicItemDictionaryBO clinicItemDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                updateClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList())) {
            for (UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto bto :
                    updateClinicItemGuideDescriptionBto.getClinicItemGuideDescriptionBtoList()) {
                Optional<ClinicItemGuideDescriptionBO> any =
                        clinicItemDictionaryBO.getClinicItemGuideDescriptionBOSet().stream()
                                .filter(
                                        clinicItemGuideDescriptionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                clinicItemGuideDescriptionBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ClinicItemGuideDescriptionBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToClinicItemGuideDescription());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "guideUseScopeList")) {
                        bo.setGuideUseScopeList(bto.getGuideUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "organizationType")) {
                        bo.setOrganizationType(bto.getOrganizationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "organizationId")) {
                        bo.setOrganizationId(bto.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "performDepartmentId")) {
                        bo.setPerformDepartmentId(bto.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "notice")) {
                        bo.setNotice(bto.getNotice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "examInstruction")) {
                        bo.setExamInstruction(bto.getExamInstruction());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "guideInfo")) {
                        bo.setGuideInfo(bto.getGuideInfo());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "navigationAddress")) {
                        bo.setNavigationAddress(bto.getNavigationAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "campusId")) {
                        bo.setCampusId(bto.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "orderDepartmentId")) {
                        bo.setOrderDepartmentId(bto.getOrderDepartmentId());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:createClinicItemChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateCreateClinicItemChargeItemOnMissThrowEx(
            BaseClinicItemDictionaryBOService.CreateClinicItemChargeItemBoResult boResult,
            CreateClinicItemChargeItemBto createClinicItemChargeItemBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createClinicItemChargeItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            createClinicItemChargeItemBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(createClinicItemChargeItemBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:createClinicItemGuideDescription,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateCreateClinicItemGuideDescriptionOnMissThrowEx(
            BaseClinicItemDictionaryBOService.CreateClinicItemGuideDescriptionBoResult boResult,
            CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createClinicItemGuideDescriptionBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            createClinicItemGuideDescriptionBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(createClinicItemGuideDescriptionBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:enableClinicItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateEnableClinicItemOnMissThrowEx(
            BaseClinicItemDictionaryBOService.EnableClinicItemBoResult boResult,
            EnableClinicItemBto enableClinicItemBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (enableClinicItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(enableClinicItemBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(enableClinicItemBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(enableClinicItemBto, "__$validPropertySet"),
                    "enableFlag")) {
                clinicItemDictionaryBO.setEnableFlag(enableClinicItemBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(enableClinicItemBto, "__$validPropertySet"),
                    "disabledReason")) {
                clinicItemDictionaryBO.setDisabledReason(enableClinicItemBto.getDisabledReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(enableClinicItemBto, "__$validPropertySet"),
                    "updatedBy")) {
                clinicItemDictionaryBO.setUpdatedBy(enableClinicItemBto.getUpdatedBy());
            }
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:updateClinicItemDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateUpdateClinicItemDetailOnMissThrowEx(
            BaseClinicItemDictionaryBOService.UpdateClinicItemDetailBoResult boResult,
            UpdateClinicItemDetailBto updateClinicItemDetailBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateClinicItemDetailBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            updateClinicItemDetailBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(updateClinicItemDetailBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "itemType")) {
                clinicItemDictionaryBO.setItemType(updateClinicItemDetailBto.getItemType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "clinicItemName")) {
                clinicItemDictionaryBO.setClinicItemName(
                        updateClinicItemDetailBto.getClinicItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "clinicItemCatalogId")) {
                clinicItemDictionaryBO.setClinicItemCatalogId(
                        updateClinicItemDetailBto.getClinicItemCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "standardCode")) {
                clinicItemDictionaryBO.setStandardCode(updateClinicItemDetailBto.getStandardCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "frequency")) {
                clinicItemDictionaryBO.setFrequency(updateClinicItemDetailBto.getFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "unit")) {
                clinicItemDictionaryBO.setUnit(updateClinicItemDetailBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "inputCode")) {
                clinicItemDictionaryBO.setInputCode(updateClinicItemDetailBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "description")) {
                clinicItemDictionaryBO.setDescription(updateClinicItemDetailBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "remark")) {
                clinicItemDictionaryBO.setRemark(updateClinicItemDetailBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "enableFlag")) {
                clinicItemDictionaryBO.setEnableFlag(updateClinicItemDetailBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "institutionId")) {
                clinicItemDictionaryBO.setInstitutionId(
                        updateClinicItemDetailBto.getInstitutionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "auditFlag")) {
                clinicItemDictionaryBO.setAuditFlag(updateClinicItemDetailBto.getAuditFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "auditDate")) {
                clinicItemDictionaryBO.setAuditDate(updateClinicItemDetailBto.getAuditDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "printFlag")) {
                clinicItemDictionaryBO.setPrintFlag(updateClinicItemDetailBto.getPrintFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "auditOperatorId")) {
                clinicItemDictionaryBO.setAuditOperatorId(
                        updateClinicItemDetailBto.getAuditOperatorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "limitGender")) {
                clinicItemDictionaryBO.setLimitGender(updateClinicItemDetailBto.getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "ageMinLimit")) {
                clinicItemDictionaryBO.setAgeMinLimit(updateClinicItemDetailBto.getAgeMinLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "ageMaxLimit")) {
                clinicItemDictionaryBO.setAgeMaxLimit(updateClinicItemDetailBto.getAgeMaxLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "includeCurrentDepartmentFlag")) {
                clinicItemDictionaryBO.setIncludeCurrentDepartmentFlag(
                        updateClinicItemDetailBto.getIncludeCurrentDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "rescueFlag")) {
                clinicItemDictionaryBO.setRescueFlag(updateClinicItemDetailBto.getRescueFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "billingAttribute")) {
                clinicItemDictionaryBO.setBillingAttribute(
                        updateClinicItemDetailBto.getBillingAttribute());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "specialNeedFlag")) {
                clinicItemDictionaryBO.setSpecialNeedFlag(
                        updateClinicItemDetailBto.getSpecialNeedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "onlySelectSettingDepartmentFlag")) {
                clinicItemDictionaryBO.setOnlySelectSettingDepartmentFlag(
                        updateClinicItemDetailBto.getOnlySelectSettingDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "operationCode")) {
                clinicItemDictionaryBO.setOperationCode(
                        updateClinicItemDetailBto.getOperationCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "disabledReason")) {
                clinicItemDictionaryBO.setDisabledReason(
                        updateClinicItemDetailBto.getDisabledReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "useScopeList")) {
                clinicItemDictionaryBO.setUseScopeList(updateClinicItemDetailBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "campusIdList")) {
                clinicItemDictionaryBO.setCampusIdList(updateClinicItemDetailBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "exclusionType")) {
                clinicItemDictionaryBO.setExclusionType(
                        updateClinicItemDetailBto.getExclusionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "exclusionTime")) {
                clinicItemDictionaryBO.setExclusionTime(
                        updateClinicItemDetailBto.getExclusionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "delayDays")) {
                clinicItemDictionaryBO.setDelayDays(updateClinicItemDetailBto.getDelayDays());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "outpDefaultPerformDepartmentId")) {
                clinicItemDictionaryBO.setOutpDefaultPerformDepartmentId(
                        updateClinicItemDetailBto.getOutpDefaultPerformDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "inpDefaultPerformDepartmentType")) {
                clinicItemDictionaryBO.setInpDefaultPerformDepartmentType(
                        updateClinicItemDetailBto.getInpDefaultPerformDepartmentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "frequencyNotAllowedModifyFlag")) {
                clinicItemDictionaryBO.setFrequencyNotAllowedModifyFlag(
                        updateClinicItemDetailBto.getFrequencyNotAllowedModifyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "doubleSignatureFlag")) {
                clinicItemDictionaryBO.setDoubleSignatureFlag(
                        updateClinicItemDetailBto.getDoubleSignatureFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "limitWardIdList")) {
                clinicItemDictionaryBO.setLimitWardIdList(
                        updateClinicItemDetailBto.getLimitWardIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "pdaPerformFlag")) {
                clinicItemDictionaryBO.setPdaPerformFlag(
                        updateClinicItemDetailBto.getPdaPerformFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "cardPrintTypeList")) {
                clinicItemDictionaryBO.setCardPrintTypeList(
                        updateClinicItemDetailBto.getCardPrintTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "orderFrequencyBillingType")) {
                clinicItemDictionaryBO.setOrderFrequencyBillingType(
                        updateClinicItemDetailBto.getOrderFrequencyBillingType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "billingInterval")) {
                clinicItemDictionaryBO.setBillingInterval(
                        updateClinicItemDetailBto.getBillingInterval());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "alias")) {
                clinicItemDictionaryBO.setAlias(updateClinicItemDetailBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "createdBy")) {
                clinicItemDictionaryBO.setCreatedBy(updateClinicItemDetailBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "updatedBy")) {
                clinicItemDictionaryBO.setUpdatedBy(updateClinicItemDetailBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "itemSpecification")) {
                clinicItemDictionaryBO.setItemSpecification(
                        updateClinicItemDetailBto.getItemSpecification());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDetailBto, "__$validPropertySet"),
                    "sortNumber")) {
                clinicItemDictionaryBO.setSortNumber(updateClinicItemDetailBto.getSortNumber());
            }
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:updateClinicItemDictionary,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateUpdateClinicItemDictionaryOnMissThrowEx(
            BaseClinicItemDictionaryBOService.UpdateClinicItemDictionaryBoResult boResult,
            UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateClinicItemDictionaryBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            updateClinicItemDictionaryBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(updateClinicItemDictionaryBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemName")) {
                clinicItemDictionaryBO.setClinicItemName(
                        updateClinicItemDictionaryBto.getClinicItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "clinicItemCatalogId")) {
                clinicItemDictionaryBO.setClinicItemCatalogId(
                        updateClinicItemDictionaryBto.getClinicItemCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "standardCode")) {
                clinicItemDictionaryBO.setStandardCode(
                        updateClinicItemDictionaryBto.getStandardCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "itemSpecification")) {
                clinicItemDictionaryBO.setItemSpecification(
                        updateClinicItemDictionaryBto.getItemSpecification());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "frequency")) {
                clinicItemDictionaryBO.setFrequency(updateClinicItemDictionaryBto.getFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "unit")) {
                clinicItemDictionaryBO.setUnit(updateClinicItemDictionaryBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "inputCode")) {
                clinicItemDictionaryBO.setInputCode(updateClinicItemDictionaryBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "description")) {
                clinicItemDictionaryBO.setDescription(
                        updateClinicItemDictionaryBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "remark")) {
                clinicItemDictionaryBO.setRemark(updateClinicItemDictionaryBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "limitGender")) {
                clinicItemDictionaryBO.setLimitGender(
                        updateClinicItemDictionaryBto.getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "printFlag")) {
                clinicItemDictionaryBO.setPrintFlag(updateClinicItemDictionaryBto.getPrintFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "ageMinLimit")) {
                clinicItemDictionaryBO.setAgeMinLimit(
                        updateClinicItemDictionaryBto.getAgeMinLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "ageMaxLimit")) {
                clinicItemDictionaryBO.setAgeMaxLimit(
                        updateClinicItemDictionaryBto.getAgeMaxLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "includeCurrentDepartmentFlag")) {
                clinicItemDictionaryBO.setIncludeCurrentDepartmentFlag(
                        updateClinicItemDictionaryBto.getIncludeCurrentDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "rescueFlag")) {
                clinicItemDictionaryBO.setRescueFlag(updateClinicItemDictionaryBto.getRescueFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "billingAttribute")) {
                clinicItemDictionaryBO.setBillingAttribute(
                        updateClinicItemDictionaryBto.getBillingAttribute());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "specialNeedFlag")) {
                clinicItemDictionaryBO.setSpecialNeedFlag(
                        updateClinicItemDictionaryBto.getSpecialNeedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "onlySelectSettingDepartmentFlag")) {
                clinicItemDictionaryBO.setOnlySelectSettingDepartmentFlag(
                        updateClinicItemDictionaryBto.getOnlySelectSettingDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "operationCode")) {
                clinicItemDictionaryBO.setOperationCode(
                        updateClinicItemDictionaryBto.getOperationCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "useScopeList")) {
                clinicItemDictionaryBO.setUseScopeList(
                        updateClinicItemDictionaryBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "campusIdList")) {
                clinicItemDictionaryBO.setCampusIdList(
                        updateClinicItemDictionaryBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "exclusionType")) {
                clinicItemDictionaryBO.setExclusionType(
                        updateClinicItemDictionaryBto.getExclusionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "exclusionTime")) {
                clinicItemDictionaryBO.setExclusionTime(
                        updateClinicItemDictionaryBto.getExclusionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "delayDays")) {
                clinicItemDictionaryBO.setDelayDays(updateClinicItemDictionaryBto.getDelayDays());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "outpDefaultPerformDepartmentId")) {
                clinicItemDictionaryBO.setOutpDefaultPerformDepartmentId(
                        updateClinicItemDictionaryBto.getOutpDefaultPerformDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "inpDefaultPerformDepartmentType")) {
                clinicItemDictionaryBO.setInpDefaultPerformDepartmentType(
                        updateClinicItemDictionaryBto.getInpDefaultPerformDepartmentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "frequencyNotAllowedModifyFlag")) {
                clinicItemDictionaryBO.setFrequencyNotAllowedModifyFlag(
                        updateClinicItemDictionaryBto.getFrequencyNotAllowedModifyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "doubleSignatureFlag")) {
                clinicItemDictionaryBO.setDoubleSignatureFlag(
                        updateClinicItemDictionaryBto.getDoubleSignatureFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "limitWardIdList")) {
                clinicItemDictionaryBO.setLimitWardIdList(
                        updateClinicItemDictionaryBto.getLimitWardIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "pdaPerformFlag")) {
                clinicItemDictionaryBO.setPdaPerformFlag(
                        updateClinicItemDictionaryBto.getPdaPerformFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "cardPrintTypeList")) {
                clinicItemDictionaryBO.setCardPrintTypeList(
                        updateClinicItemDictionaryBto.getCardPrintTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "orderFrequencyBillingType")) {
                clinicItemDictionaryBO.setOrderFrequencyBillingType(
                        updateClinicItemDictionaryBto.getOrderFrequencyBillingType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "billingInterval")) {
                clinicItemDictionaryBO.setBillingInterval(
                        updateClinicItemDictionaryBto.getBillingInterval());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "alias")) {
                clinicItemDictionaryBO.setAlias(updateClinicItemDictionaryBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "createdBy")) {
                clinicItemDictionaryBO.setCreatedBy(updateClinicItemDictionaryBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "updatedBy")) {
                clinicItemDictionaryBO.setUpdatedBy(updateClinicItemDictionaryBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateClinicItemDictionaryBto, "__$validPropertySet"),
                    "sortNumber")) {
                clinicItemDictionaryBO.setSortNumber(updateClinicItemDictionaryBto.getSortNumber());
            }
            return clinicItemDictionaryBO;
        }
    }

    /** 更新对象:updateClinicItemGuideDescription,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ClinicItemDictionaryBO updateUpdateClinicItemGuideDescriptionOnMissThrowEx(
            UpdateClinicItemGuideDescriptionBoResult boResult,
            UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto) {
        ClinicItemDictionaryBO clinicItemDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateClinicItemGuideDescriptionBto.getClinicItemId() == null);
        if (!allNull && !found) {
            clinicItemDictionaryBO =
                    ClinicItemDictionaryBO.getByClinicItemId(
                            updateClinicItemGuideDescriptionBto.getClinicItemId());
            found = true;
        }
        if (clinicItemDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(clinicItemDictionaryBO.convertToClinicItemDictionary());
            updatedBto.setBto(updateClinicItemGuideDescriptionBto);
            updatedBto.setBo(clinicItemDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return clinicItemDictionaryBO;
        }
    }

    public static class CreateClinicItemDictionaryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateClinicItemDictionaryBto, ClinicItemDictionaryBO> getCreatedBto(
                CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
            return this.getAddedResult(createClinicItemDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItemBO>
                getCreatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return this.getAddedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartmentBO>
                getCreatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return this.getAddedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getCreatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return this.getAddedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemChargeItem getDeleted_ClinicItemChargeItem() {
            return (ClinicItemChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemPerformDepartment getDeleted_ClinicItemPerformDepartment() {
            return (ClinicItemPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemGuideDescription getDeleted_ClinicItemGuideDescription() {
            return (ClinicItemGuideDescription)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemGuideDescription.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemDictionaryBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
            return super.getUpdatedResult(createClinicItemDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItem,
                        ClinicItemChargeItemBO>
                getUpdatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUpdatedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartment,
                        ClinicItemPerformDepartmentBO>
                getUpdatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return super.getUpdatedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescription,
                        ClinicItemGuideDescriptionBO>
                getUpdatedBto(
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateClinicItemDictionaryBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(CreateClinicItemDictionaryBto createClinicItemDictionaryBto) {
            return super.getUnmodifiedResult(createClinicItemDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItemBO>
                getUnmodifiedBto(
                        CreateClinicItemDictionaryBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUnmodifiedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartmentBO>
                getUnmodifiedBto(
                        CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return super.getUnmodifiedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getUnmodifiedBto(
                        CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(clinicItemGuideDescriptionBto);
        }
    }

    public static class UpdateClinicItemDictionaryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateClinicItemDictionaryBto, ClinicItemDictionaryBO> getCreatedBto(
                UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
            return this.getAddedResult(updateClinicItemDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateClinicItemDictionaryBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
            return super.getUpdatedResult(updateClinicItemDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateClinicItemDictionaryBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto) {
            return super.getUnmodifiedResult(updateClinicItemDictionaryBto);
        }
    }

    public static class ChangeClinicItemAuditFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeClinicItemAuditFlagBto, ClinicItemDictionaryBO> getCreatedBto(
                ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
            return this.getAddedResult(changeClinicItemAuditFlagBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeClinicItemAuditFlagBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
            return super.getUpdatedResult(changeClinicItemAuditFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeClinicItemAuditFlagBto, ClinicItemDictionaryBO> getUnmodifiedBto(
                ChangeClinicItemAuditFlagBto changeClinicItemAuditFlagBto) {
            return super.getUnmodifiedResult(changeClinicItemAuditFlagBto);
        }
    }

    public static class EnableClinicItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<EnableClinicItemBto, ClinicItemDictionaryBO> getCreatedBto(
                EnableClinicItemBto enableClinicItemBto) {
            return this.getAddedResult(enableClinicItemBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<EnableClinicItemBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(EnableClinicItemBto enableClinicItemBto) {
            return super.getUpdatedResult(enableClinicItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<EnableClinicItemBto, ClinicItemDictionaryBO> getUnmodifiedBto(
                EnableClinicItemBto enableClinicItemBto) {
            return super.getUnmodifiedResult(enableClinicItemBto);
        }
    }

    public static class SaveClinicItemChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveClinicItemChargeItemBto.ClinicItemChargeItemBto, ClinicItemChargeItemBO>
                getCreatedBto(
                        SaveClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return this.getAddedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveClinicItemChargeItemBto, ClinicItemDictionaryBO> getCreatedBto(
                SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
            return this.getAddedResult(saveClinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemChargeItem getDeleted_ClinicItemChargeItem() {
            return (ClinicItemChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveClinicItemChargeItemBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItem,
                        ClinicItemChargeItemBO>
                getUpdatedBto(
                        SaveClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUpdatedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveClinicItemChargeItemBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
            return super.getUpdatedResult(saveClinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveClinicItemChargeItemBto.ClinicItemChargeItemBto, ClinicItemChargeItemBO>
                getUnmodifiedBto(
                        SaveClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUnmodifiedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveClinicItemChargeItemBto, ClinicItemDictionaryBO> getUnmodifiedBto(
                SaveClinicItemChargeItemBto saveClinicItemChargeItemBto) {
            return super.getUnmodifiedResult(saveClinicItemChargeItemBto);
        }
    }

    public static class SaveClinicItemGuideDescriptionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getCreatedBto(
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return this.getAddedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveClinicItemGuideDescriptionBto, ClinicItemDictionaryBO> getCreatedBto(
                SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
            return this.getAddedResult(saveClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemGuideDescription getDeleted_ClinicItemGuideDescription() {
            return (ClinicItemGuideDescription)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemGuideDescription.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescription,
                        ClinicItemGuideDescriptionBO>
                getUpdatedBto(
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveClinicItemGuideDescriptionBto,
                        ClinicItemDictionary,
                        ClinicItemDictionaryBO>
                getUpdatedBto(SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(saveClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getUnmodifiedBto(
                        SaveClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveClinicItemGuideDescriptionBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(
                        SaveClinicItemGuideDescriptionBto saveClinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(saveClinicItemGuideDescriptionBto);
        }
    }

    public static class ChangeClinicItemGuideDescriptionEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getCreatedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return this.getAddedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeClinicItemGuideDescriptionEnableFlagBto, ClinicItemDictionaryBO>
                getCreatedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto
                                changeClinicItemGuideDescriptionEnableFlagBto) {
            return this.getAddedResult(changeClinicItemGuideDescriptionEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemGuideDescription getDeleted_ClinicItemGuideDescription() {
            return (ClinicItemGuideDescription)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemGuideDescription.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescription,
                        ClinicItemGuideDescriptionBO>
                getUpdatedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeClinicItemGuideDescriptionEnableFlagBto,
                        ClinicItemDictionary,
                        ClinicItemDictionaryBO>
                getUpdatedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto
                                changeClinicItemGuideDescriptionEnableFlagBto) {
            return super.getUpdatedResult(changeClinicItemGuideDescriptionEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getUnmodifiedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeClinicItemGuideDescriptionEnableFlagBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(
                        ChangeClinicItemGuideDescriptionEnableFlagBto
                                changeClinicItemGuideDescriptionEnableFlagBto) {
            return super.getUnmodifiedResult(changeClinicItemGuideDescriptionEnableFlagBto);
        }
    }

    public static class CreateClinicItemGuideDescriptionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getCreatedBto(
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return this.getAddedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateClinicItemGuideDescriptionBto, ClinicItemDictionaryBO> getCreatedBto(
                CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto) {
            return this.getAddedResult(createClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemGuideDescription getDeleted_ClinicItemGuideDescription() {
            return (ClinicItemGuideDescription)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemGuideDescription.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescription,
                        ClinicItemGuideDescriptionBO>
                getUpdatedBto(
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemGuideDescriptionBto,
                        ClinicItemDictionary,
                        ClinicItemDictionaryBO>
                getUpdatedBto(
                        CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(createClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getUnmodifiedBto(
                        CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateClinicItemGuideDescriptionBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(
                        CreateClinicItemGuideDescriptionBto createClinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(createClinicItemGuideDescriptionBto);
        }
    }

    public static class UpdateClinicItemGuideDescriptionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getCreatedBto(
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return this.getAddedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateClinicItemGuideDescriptionBto, ClinicItemDictionaryBO> getCreatedBto(
                UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto) {
            return this.getAddedResult(updateClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemGuideDescription getDeleted_ClinicItemGuideDescription() {
            return (ClinicItemGuideDescription)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemGuideDescription.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescription,
                        ClinicItemGuideDescriptionBO>
                getUpdatedBto(
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateClinicItemGuideDescriptionBto,
                        ClinicItemDictionary,
                        ClinicItemDictionaryBO>
                getUpdatedBto(
                        UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto) {
            return super.getUpdatedResult(updateClinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto,
                        ClinicItemGuideDescriptionBO>
                getUnmodifiedBto(
                        UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                                clinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(clinicItemGuideDescriptionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateClinicItemGuideDescriptionBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(
                        UpdateClinicItemGuideDescriptionBto updateClinicItemGuideDescriptionBto) {
            return super.getUnmodifiedResult(updateClinicItemGuideDescriptionBto);
        }
    }

    public static class CreateClinicItemChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItemBO>
                getCreatedBto(
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return this.getAddedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateClinicItemChargeItemBto, ClinicItemDictionaryBO> getCreatedBto(
                CreateClinicItemChargeItemBto createClinicItemChargeItemBto) {
            return this.getAddedResult(createClinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemChargeItem getDeleted_ClinicItemChargeItem() {
            return (ClinicItemChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItem,
                        ClinicItemChargeItemBO>
                getUpdatedBto(
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUpdatedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateClinicItemChargeItemBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(CreateClinicItemChargeItemBto createClinicItemChargeItemBto) {
            return super.getUpdatedResult(createClinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItemBO>
                getUnmodifiedBto(
                        CreateClinicItemChargeItemBto.ClinicItemChargeItemBto
                                clinicItemChargeItemBto) {
            return super.getUnmodifiedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateClinicItemChargeItemBto, ClinicItemDictionaryBO>
                getUnmodifiedBto(CreateClinicItemChargeItemBto createClinicItemChargeItemBto) {
            return super.getUnmodifiedResult(createClinicItemChargeItemBto);
        }
    }

    public static class UpdateClinicItemDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ClinicItemDictionaryBO getRootBo() {
            return (ClinicItemDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateClinicItemDetailBto.ClinicItemChargeItemBto, ClinicItemChargeItemBO>
                getCreatedBto(
                        UpdateClinicItemDetailBto.ClinicItemChargeItemBto clinicItemChargeItemBto) {
            return this.getAddedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartmentBO>
                getCreatedBto(
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return this.getAddedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateClinicItemDetailBto, ClinicItemDictionaryBO> getCreatedBto(
                UpdateClinicItemDetailBto updateClinicItemDetailBto) {
            return this.getAddedResult(updateClinicItemDetailBto);
        }

        @AutoGenerated(locked = true)
        public ClinicItemChargeItem getDeleted_ClinicItemChargeItem() {
            return (ClinicItemChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemPerformDepartment getDeleted_ClinicItemPerformDepartment() {
            return (ClinicItemPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ClinicItemPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public ClinicItemDictionary getDeleted_ClinicItemDictionary() {
            return (ClinicItemDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ClinicItemDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateClinicItemDetailBto.ClinicItemChargeItemBto,
                        ClinicItemChargeItem,
                        ClinicItemChargeItemBO>
                getUpdatedBto(
                        UpdateClinicItemDetailBto.ClinicItemChargeItemBto clinicItemChargeItemBto) {
            return super.getUpdatedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartment,
                        ClinicItemPerformDepartmentBO>
                getUpdatedBto(
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return super.getUpdatedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateClinicItemDetailBto, ClinicItemDictionary, ClinicItemDictionaryBO>
                getUpdatedBto(UpdateClinicItemDetailBto updateClinicItemDetailBto) {
            return super.getUpdatedResult(updateClinicItemDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateClinicItemDetailBto.ClinicItemChargeItemBto, ClinicItemChargeItemBO>
                getUnmodifiedBto(
                        UpdateClinicItemDetailBto.ClinicItemChargeItemBto clinicItemChargeItemBto) {
            return super.getUnmodifiedResult(clinicItemChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto,
                        ClinicItemPerformDepartmentBO>
                getUnmodifiedBto(
                        UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto
                                clinicItemPerformDepartmentBto) {
            return super.getUnmodifiedResult(clinicItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateClinicItemDetailBto, ClinicItemDictionaryBO> getUnmodifiedBto(
                UpdateClinicItemDetailBto updateClinicItemDetailBto) {
            return super.getUnmodifiedResult(updateClinicItemDetailBto);
        }
    }
}
