package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.ExamDeviceBO;
import com.pulse.dictionary_business.persist.dos.ExamDevice;
import com.pulse.dictionary_business.persist.dos.ExamDeviceChargeItem;
import com.pulse.dictionary_business.persist.dos.ExamDeviceDocumentTemplate;
import com.pulse.dictionary_business.persist.dos.ExamDeviceOrderLimit;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.ChangeExamDeviceChargeItemEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.ChangeExamDeviceEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.ChangeExamDeviceOrderLimitEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.CreateExamDeviceBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.CreateExamDeviceChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.CreateExamDeviceOrderLimitBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.SaveExamDeviceChargeItemListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.SaveExamDeviceDocumentTemplateBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.SaveExamDeviceOrderLimitListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.UpdateExamDeviceBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.UpdateExamDeviceChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseExamDeviceBOService.UpdateExamDeviceOrderLimitBoResult;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceChargeItemEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamDeviceOrderLimitEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceOrderLimitBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceChargeItemListBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceDocumentTemplateBto;
import com.pulse.dictionary_business.service.bto.SaveExamDeviceOrderLimitListBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceChargeItemBto;
import com.pulse.dictionary_business.service.bto.UpdateExamDeviceOrderLimitBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "6ad745b8-f809-385b-86c7-90aac7d61066")
public class BaseExamDeviceBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 修改检查设备收费项目启用标记 */
    @AutoGenerated(locked = true)
    protected ChangeExamDeviceChargeItemEnableFlagBoResult changeExamDeviceChargeItemEnableFlagBase(
            ChangeExamDeviceChargeItemEnableFlagBto changeExamDeviceChargeItemEnableFlagBto) {
        if (changeExamDeviceChargeItemEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamDeviceChargeItemEnableFlagBoResult boResult =
                new ChangeExamDeviceChargeItemEnableFlagBoResult();
        ExamDeviceBO examDeviceBO =
                updateChangeExamDeviceChargeItemEnableFlagOnMissThrowEx(
                        boResult, changeExamDeviceChargeItemEnableFlagBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamDeviceChargeItemEnableFlagBto, "__$validPropertySet"),
                    "examDeviceChargeItemBtoList")) {
                updateExamDeviceChargeItemBtoOnMissThrowEx(
                        boResult, changeExamDeviceChargeItemEnableFlagBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 修改检查设备停用标识 */
    @AutoGenerated(locked = true)
    protected ChangeExamDeviceEnableFlagBoResult changeExamDeviceEnableFlagBase(
            ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
        if (changeExamDeviceEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamDeviceEnableFlagBoResult boResult = new ChangeExamDeviceEnableFlagBoResult();
        ExamDeviceBO examDeviceBO =
                updateChangeExamDeviceEnableFlagOnMissThrowEx(
                        boResult, changeExamDeviceEnableFlagBto);
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 修改检查设备开单限制停用标识 */
    @AutoGenerated(locked = true)
    protected ChangeExamDeviceOrderLimitEnableFlagBoResult changeExamDeviceOrderLimitEnableFlagBase(
            ChangeExamDeviceOrderLimitEnableFlagBto changeExamDeviceOrderLimitEnableFlagBto) {
        if (changeExamDeviceOrderLimitEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamDeviceOrderLimitEnableFlagBoResult boResult =
                new ChangeExamDeviceOrderLimitEnableFlagBoResult();
        ExamDeviceBO examDeviceBO =
                updateChangeExamDeviceOrderLimitEnableFlagOnMissThrowEx(
                        boResult, changeExamDeviceOrderLimitEnableFlagBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamDeviceOrderLimitEnableFlagBto, "__$validPropertySet"),
                    "examDeviceOrderLimitBtoList")) {
                updateExamDeviceOrderLimitBtoOnMissThrowEx(
                        boResult, changeExamDeviceOrderLimitEnableFlagBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO createCreateExamDeviceOnDuplicateThrowEx(
            CreateExamDeviceBoResult boResult, CreateExamDeviceBto createExamDeviceBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createExamDeviceBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(createExamDeviceBto.getId());
            if (examDeviceBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examDeviceBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", examDeviceBO.getId(), "exam_device");
                throw new IgnoredException(400, "检查类型设备已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "exam_device",
                        examDeviceBO.getId(),
                        "exam_device");
                throw new IgnoredException(400, "检查类型设备已存在");
            }
        } else {
            examDeviceBO = new ExamDeviceBO();
            if (pkExist) {
                examDeviceBO.setId(createExamDeviceBto.getId());
            } else {
                examDeviceBO.setId(String.valueOf(this.idGenerator.allocateId("exam_device")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "examTypeId")) {
                examDeviceBO.setExamTypeId(createExamDeviceBto.getExamTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "sortNumber")) {
                examDeviceBO.setSortNumber(createExamDeviceBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "deviceId")) {
                examDeviceBO.setDeviceId(createExamDeviceBto.getDeviceId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "deviceName")) {
                examDeviceBO.setDeviceName(createExamDeviceBto.getDeviceName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "inputCode")) {
                examDeviceBO.setInputCode(createExamDeviceBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "campusIdList")) {
                examDeviceBO.setCampusIdList(createExamDeviceBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "billingMode")) {
                examDeviceBO.setBillingMode(createExamDeviceBto.getBillingMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "updatedBy")) {
                examDeviceBO.setUpdatedBy(createExamDeviceBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "createdBy")) {
                examDeviceBO.setCreatedBy(createExamDeviceBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "enableFlag")) {
                examDeviceBO.setEnableFlag(createExamDeviceBto.getEnableFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createExamDeviceBto);
            addedBto.setBo(examDeviceBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examDeviceBO;
    }

    /** 创建检查设备 */
    @AutoGenerated(locked = true)
    protected CreateExamDeviceBoResult createExamDeviceBase(
            CreateExamDeviceBto createExamDeviceBto) {
        if (createExamDeviceBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamDeviceBoResult boResult = new CreateExamDeviceBoResult();
        ExamDeviceBO examDeviceBO =
                createCreateExamDeviceOnDuplicateThrowEx(boResult, createExamDeviceBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "examDeviceDocumentTemplateBto")) {
                createExamDeviceDocumentTemplateBtoOnDuplicateThrowEx(
                        boResult, createExamDeviceBto, examDeviceBO);
            }
        }
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "examDeviceChargeItemBtoList")) {
                createExamDeviceChargeItemBto(boResult, createExamDeviceBto, examDeviceBO);
            }
        }
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamDeviceBto, "__$validPropertySet"),
                    "examDeviceOrderLimitBtoList")) {
                createExamDeviceOrderLimitBto(boResult, createExamDeviceBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 创建检查项目收费项目 */
    @AutoGenerated(locked = true)
    protected CreateExamDeviceChargeItemBoResult createExamDeviceChargeItemBase(
            CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto) {
        if (createExamDeviceChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamDeviceChargeItemBoResult boResult = new CreateExamDeviceChargeItemBoResult();
        ExamDeviceBO examDeviceBO =
                updateCreateExamDeviceChargeItemOnMissThrowEx(
                        boResult, createExamDeviceChargeItemBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamDeviceChargeItemBto, "__$validPropertySet"),
                    "examDeviceChargeItemBtoList")) {
                createExamDeviceChargeItemBto(
                        boResult, createExamDeviceChargeItemBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 创建对象ExamDeviceChargeItemBto */
    @AutoGenerated(locked = true)
    private void createExamDeviceChargeItemBto(
            CreateExamDeviceBoResult boResult,
            CreateExamDeviceBto createExamDeviceBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(createExamDeviceBto.getExamDeviceChargeItemBtoList())) {
            for (CreateExamDeviceBto.ExamDeviceChargeItemBto item :
                    createExamDeviceBto.getExamDeviceChargeItemBtoList()) {
                ExamDeviceChargeItemBO subBo = new ExamDeviceChargeItemBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemId")) {
                    subBo.setChargeItemId(item.getChargeItemId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemCount")) {
                    subBo.setChargeItemCount(item.getChargeItemCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "filmFeeType")) {
                    subBo.setFilmFeeType(item.getFilmFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "graphicFeeFlag")) {
                    subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "digitalImagingFeeFlag")) {
                    subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enableFlag")) {
                    subBo.setEnableFlag(item.getEnableFlag());
                }
                subBo.setExamDeviceBO(examDeviceBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_device_charge_item")));
                examDeviceBO.getExamDeviceChargeItemBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象ExamDeviceChargeItemBto */
    @AutoGenerated(locked = true)
    private void createExamDeviceChargeItemBto(
            CreateExamDeviceChargeItemBoResult boResult,
            CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                createExamDeviceChargeItemBto.getExamDeviceChargeItemBtoList())) {
            for (CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto item :
                    createExamDeviceChargeItemBto.getExamDeviceChargeItemBtoList()) {
                ExamDeviceChargeItemBO subBo = new ExamDeviceChargeItemBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemId")) {
                    subBo.setChargeItemId(item.getChargeItemId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemCount")) {
                    subBo.setChargeItemCount(item.getChargeItemCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "filmFeeType")) {
                    subBo.setFilmFeeType(item.getFilmFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "graphicFeeFlag")) {
                    subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "digitalImagingFeeFlag")) {
                    subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enableFlag")) {
                    subBo.setEnableFlag(item.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedFlag")) {
                    subBo.setEnhancedFlag(item.getEnhancedFlag());
                }
                subBo.setExamDeviceBO(examDeviceBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_device_charge_item")));
                examDeviceBO.getExamDeviceChargeItemBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamDeviceChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamDeviceChargeItemBtoOnDuplicateUpdate(
            BaseExamDeviceBOService.SaveExamDeviceChargeItemListBoResult boResult,
            SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isEmpty(
                saveExamDeviceChargeItemListBto.getExamDeviceChargeItemBtoList())) {
            saveExamDeviceChargeItemListBto.setExamDeviceChargeItemBtoList(List.of());
        }
        examDeviceBO
                .getExamDeviceChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamDeviceChargeItemListBto
                                            .getExamDeviceChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    examDeviceChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examDeviceChargeItemBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examDeviceChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamDeviceChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveExamDeviceChargeItemListBto.getExamDeviceChargeItemBtoList())) {
            for (SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto item :
                    saveExamDeviceChargeItemListBto.getExamDeviceChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamDeviceChargeItemBO> any =
                        examDeviceBO.getExamDeviceChargeItemBOSet().stream()
                                .filter(
                                        examDeviceChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examDeviceChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamDeviceChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamDeviceChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedFlag")) {
                            bo.setEnhancedFlag(item.getEnhancedFlag());
                        }
                    } else {
                        ExamDeviceChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamDeviceChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedFlag")) {
                            bo.setEnhancedFlag(item.getEnhancedFlag());
                        }
                    }
                } else {
                    ExamDeviceChargeItemBO subBo = new ExamDeviceChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedFlag")) {
                        subBo.setEnhancedFlag(item.getEnhancedFlag());
                    }
                    subBo.setExamDeviceBO(examDeviceBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("exam_device_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examDeviceBO.getExamDeviceChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ExamDeviceDocumentTemplateBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createExamDeviceDocumentTemplateBtoOnDuplicateThrowEx(
            BaseExamDeviceBOService.CreateExamDeviceBoResult boResult,
            CreateExamDeviceBto createExamDeviceBto,
            ExamDeviceBO examDeviceBO) {
        if (examDeviceBO.getExamDeviceDocumentTemplateBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！",
                    examDeviceBO.getExamDeviceDocumentTemplateBO().getId(),
                    "exam_device_document_template");
            throw new IgnoredException(400, "检查设备文书模板已存在");
        } else {
            if (createExamDeviceBto.getExamDeviceDocumentTemplateBto() == null) {
                return;
            }
            ExamDeviceDocumentTemplateBO examDeviceDocumentTemplateBO =
                    examDeviceBO.getOrCreateExamDeviceDocumentTemplateBO();
            CreateExamDeviceBto.ExamDeviceDocumentTemplateBto examDeviceDocumentTemplateBto =
                    createExamDeviceBto.getExamDeviceDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examDeviceDocumentTemplateBO.setOutpatientInformedConsentForm(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examDeviceDocumentTemplateBO.setInpatientInformedConsentForm(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examDeviceDocumentTemplateBO.setOutpatientApplyTemplate(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examDeviceDocumentTemplateBO.setInpatientApplyTemplate(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examDeviceDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examDeviceDocumentTemplateBO.setPreHospitalApplyTemplate(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examDeviceDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examDeviceDocumentTemplateBO.setCreatedBy(
                        createExamDeviceBto.getExamDeviceDocumentTemplateBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "updatedBy")) {
                examDeviceDocumentTemplateBO.setUpdatedBy(
                        createExamDeviceBto.getExamDeviceDocumentTemplateBto().getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examDeviceDocumentTemplateBO.setIodineContrastFlag(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examDeviceDocumentTemplateBO.setGadoliniumContrastFlag(
                        createExamDeviceBto
                                .getExamDeviceDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examDeviceDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_device_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examDeviceDocumentTemplateBto);
            addedBto.setBo(examDeviceDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:ExamDeviceDocumentTemplateBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamDeviceDocumentTemplateBtoOnDuplicateUpdate(
            SaveExamDeviceDocumentTemplateBoResult boResult,
            SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto,
            ExamDeviceBO examDeviceBO) {
        if (examDeviceBO.getExamDeviceDocumentTemplateBO() != null) {
            ExamDeviceDocumentTemplateBO bo =
                    examDeviceBO.getOrCreateExamDeviceDocumentTemplateBO();
            SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto bto =
                    saveExamDeviceDocumentTemplateBto.getExamDeviceDocumentTemplateBto();
            if (bto == null) {
                ExamDeviceDocumentTemplate deletedItem =
                        examDeviceBO
                                .getExamDeviceDocumentTemplateBO()
                                .convertToExamDeviceDocumentTemplate();
                boResult.getDeletedList().add(deletedItem);
                examDeviceBO.setExamDeviceDocumentTemplateBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToExamDeviceDocumentTemplate());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                bo.setOutpatientInformedConsentForm(bto.getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                bo.setInpatientInformedConsentForm(bto.getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                bo.setOutpatientApplyTemplate(bto.getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                bo.setInpatientApplyTemplate(bto.getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                bo.setEmergencyObservationApplyTemplate(bto.getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                bo.setPreHospitalApplyTemplate(bto.getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                bo.setPhysicalExamApplyTemplate(bto.getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdBy")) {
                bo.setCreatedBy(bto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                bo.setIodineContrastFlag(bto.getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                bo.setGadoliniumContrastFlag(bto.getGadoliniumContrastFlag());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (saveExamDeviceDocumentTemplateBto.getExamDeviceDocumentTemplateBto() == null) {
                return;
            }
            ExamDeviceDocumentTemplateBO examDeviceDocumentTemplateBO =
                    examDeviceBO.getOrCreateExamDeviceDocumentTemplateBO();
            SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto
                    examDeviceDocumentTemplateBto =
                            saveExamDeviceDocumentTemplateBto.getExamDeviceDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examDeviceDocumentTemplateBO.setOutpatientInformedConsentForm(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examDeviceDocumentTemplateBO.setInpatientInformedConsentForm(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examDeviceDocumentTemplateBO.setOutpatientApplyTemplate(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examDeviceDocumentTemplateBO.setInpatientApplyTemplate(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examDeviceDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examDeviceDocumentTemplateBO.setPreHospitalApplyTemplate(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examDeviceDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examDeviceDocumentTemplateBO.setCreatedBy(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "updatedBy")) {
                examDeviceDocumentTemplateBO.setUpdatedBy(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examDeviceDocumentTemplateBO.setIodineContrastFlag(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examDeviceDocumentTemplateBO.setGadoliniumContrastFlag(
                        saveExamDeviceDocumentTemplateBto
                                .getExamDeviceDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examDeviceDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_device_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examDeviceDocumentTemplateBto);
            addedBto.setBo(examDeviceDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建检查设备开单限制 */
    @AutoGenerated(locked = true)
    protected CreateExamDeviceOrderLimitBoResult createExamDeviceOrderLimitBase(
            CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto) {
        if (createExamDeviceOrderLimitBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamDeviceOrderLimitBoResult boResult = new CreateExamDeviceOrderLimitBoResult();
        ExamDeviceBO examDeviceBO =
                updateCreateExamDeviceOrderLimitOnMissThrowEx(
                        boResult, createExamDeviceOrderLimitBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamDeviceOrderLimitBto, "__$validPropertySet"),
                    "examDeviceOrderLimitBtoList")) {
                createExamDeviceOrderLimitBto(
                        boResult, createExamDeviceOrderLimitBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 创建对象ExamDeviceOrderLimitBto */
    @AutoGenerated(locked = true)
    private void createExamDeviceOrderLimitBto(
            CreateExamDeviceBoResult boResult,
            CreateExamDeviceBto createExamDeviceBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(createExamDeviceBto.getExamDeviceOrderLimitBtoList())) {
            for (CreateExamDeviceBto.ExamDeviceOrderLimitBto item :
                    createExamDeviceBto.getExamDeviceOrderLimitBtoList()) {
                ExamDeviceOrderLimitBO subBo = new ExamDeviceOrderLimitBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partCountCalculationDay")) {
                    subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partMaxCount")) {
                    subBo.setPartMaxCount(item.getPartMaxCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partValidCount")) {
                    subBo.setPartValidCount(item.getPartValidCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedCount")) {
                    subBo.setEnhancedCount(item.getEnhancedCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedScanningMutexFlag")) {
                    subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enableFlag")) {
                    subBo.setEnableFlag(item.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                subBo.setExamDeviceBO(examDeviceBO);
                subBo.setId(this.idGenerator.allocateId("exam_device_order_limit"));
                examDeviceBO.getExamDeviceOrderLimitBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象ExamDeviceOrderLimitBto */
    @AutoGenerated(locked = true)
    private void createExamDeviceOrderLimitBto(
            CreateExamDeviceOrderLimitBoResult boResult,
            CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                createExamDeviceOrderLimitBto.getExamDeviceOrderLimitBtoList())) {
            for (CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto item :
                    createExamDeviceOrderLimitBto.getExamDeviceOrderLimitBtoList()) {
                ExamDeviceOrderLimitBO subBo = new ExamDeviceOrderLimitBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partCountCalculationDay")) {
                    subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partMaxCount")) {
                    subBo.setPartMaxCount(item.getPartMaxCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partValidCount")) {
                    subBo.setPartValidCount(item.getPartValidCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedCount")) {
                    subBo.setEnhancedCount(item.getEnhancedCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedScanningMutexFlag")) {
                    subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                subBo.setExamDeviceBO(examDeviceBO);
                subBo.setId(item.getId());
                examDeviceBO.getExamDeviceOrderLimitBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamDeviceOrderLimitBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamDeviceOrderLimitBtoOnDuplicateUpdate(
            BaseExamDeviceBOService.SaveExamDeviceOrderLimitListBoResult boResult,
            SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isEmpty(
                saveExamDeviceOrderLimitListBto.getExamDeviceOrderLimitBtoList())) {
            saveExamDeviceOrderLimitListBto.setExamDeviceOrderLimitBtoList(List.of());
        }
        examDeviceBO
                .getExamDeviceOrderLimitBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamDeviceOrderLimitListBto
                                            .getExamDeviceOrderLimitBtoList()
                                            .stream()
                                            .filter(
                                                    examDeviceOrderLimitBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examDeviceOrderLimitBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examDeviceOrderLimitBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamDeviceOrderLimit());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveExamDeviceOrderLimitListBto.getExamDeviceOrderLimitBtoList())) {
            for (SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto item :
                    saveExamDeviceOrderLimitListBto.getExamDeviceOrderLimitBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamDeviceOrderLimitBO> any =
                        examDeviceBO.getExamDeviceOrderLimitBOSet().stream()
                                .filter(
                                        examDeviceOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examDeviceOrderLimitBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamDeviceOrderLimitBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamDeviceOrderLimit());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partCountCalculationDay")) {
                            bo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partMaxCount")) {
                            bo.setPartMaxCount(item.getPartMaxCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partValidCount")) {
                            bo.setPartValidCount(item.getPartValidCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedCount")) {
                            bo.setEnhancedCount(item.getEnhancedCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedScanningMutexFlag")) {
                            bo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        ExamDeviceOrderLimitBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamDeviceOrderLimit());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partCountCalculationDay")) {
                            bo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partMaxCount")) {
                            bo.setPartMaxCount(item.getPartMaxCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partValidCount")) {
                            bo.setPartValidCount(item.getPartValidCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedCount")) {
                            bo.setEnhancedCount(item.getEnhancedCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedScanningMutexFlag")) {
                            bo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    ExamDeviceOrderLimitBO subBo = new ExamDeviceOrderLimitBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partCountCalculationDay")) {
                        subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partMaxCount")) {
                        subBo.setPartMaxCount(item.getPartMaxCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partValidCount")) {
                        subBo.setPartValidCount(item.getPartValidCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedCount")) {
                        subBo.setEnhancedCount(item.getEnhancedCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedScanningMutexFlag")) {
                        subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setExamDeviceBO(examDeviceBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("exam_device_order_limit"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examDeviceBO.getExamDeviceOrderLimitBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO createSaveExamDeviceChargeItemListOnDuplicateUpdate(
            SaveExamDeviceChargeItemListBoResult boResult,
            SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveExamDeviceChargeItemListBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(saveExamDeviceChargeItemListBto.getId());
            if (examDeviceBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examDeviceBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examDeviceBO.convertToExamDevice());
                updatedBto.setBto(saveExamDeviceChargeItemListBto);
                updatedBto.setBo(examDeviceBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examDeviceBO.convertToExamDevice());
                updatedBto.setBto(saveExamDeviceChargeItemListBto);
                updatedBto.setBo(examDeviceBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            examDeviceBO = new ExamDeviceBO();
            if (pkExist) {
                examDeviceBO.setId(saveExamDeviceChargeItemListBto.getId());
            } else {
                examDeviceBO.setId(String.valueOf(this.idGenerator.allocateId("exam_device")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveExamDeviceChargeItemListBto);
            addedBto.setBo(examDeviceBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examDeviceBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO createSaveExamDeviceOrderLimitListOnDuplicateUpdate(
            SaveExamDeviceOrderLimitListBoResult boResult,
            SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveExamDeviceOrderLimitListBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(saveExamDeviceOrderLimitListBto.getId());
            if (examDeviceBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examDeviceBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examDeviceBO.convertToExamDevice());
                updatedBto.setBto(saveExamDeviceOrderLimitListBto);
                updatedBto.setBo(examDeviceBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examDeviceBO.convertToExamDevice());
                updatedBto.setBto(saveExamDeviceOrderLimitListBto);
                updatedBto.setBo(examDeviceBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            examDeviceBO = new ExamDeviceBO();
            if (pkExist) {
                examDeviceBO.setId(saveExamDeviceOrderLimitListBto.getId());
            } else {
                examDeviceBO.setId(String.valueOf(this.idGenerator.allocateId("exam_device")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveExamDeviceOrderLimitListBto);
            addedBto.setBo(examDeviceBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examDeviceBO;
    }

    /** 保存检查设备收费项目列表 */
    @AutoGenerated(locked = true)
    protected SaveExamDeviceChargeItemListBoResult saveExamDeviceChargeItemListBase(
            SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
        if (saveExamDeviceChargeItemListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamDeviceChargeItemListBoResult boResult = new SaveExamDeviceChargeItemListBoResult();
        ExamDeviceBO examDeviceBO =
                createSaveExamDeviceChargeItemListOnDuplicateUpdate(
                        boResult, saveExamDeviceChargeItemListBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamDeviceChargeItemListBto, "__$validPropertySet"),
                    "examDeviceChargeItemBtoList")) {
                createExamDeviceChargeItemBtoOnDuplicateUpdate(
                        boResult, saveExamDeviceChargeItemListBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 保存检查设备文书模板 */
    @AutoGenerated(locked = true)
    protected SaveExamDeviceDocumentTemplateBoResult saveExamDeviceDocumentTemplateBase(
            SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto) {
        if (saveExamDeviceDocumentTemplateBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamDeviceDocumentTemplateBoResult boResult =
                new SaveExamDeviceDocumentTemplateBoResult();
        ExamDeviceBO examDeviceBO =
                updateSaveExamDeviceDocumentTemplateOnMissThrowEx(
                        boResult, saveExamDeviceDocumentTemplateBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamDeviceDocumentTemplateBto, "__$validPropertySet"),
                    "examDeviceDocumentTemplateBto")) {
                createExamDeviceDocumentTemplateBtoOnDuplicateUpdate(
                        boResult, saveExamDeviceDocumentTemplateBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 保存检查设备开单限制 */
    @AutoGenerated(locked = true)
    protected SaveExamDeviceOrderLimitListBoResult saveExamDeviceOrderLimitListBase(
            SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
        if (saveExamDeviceOrderLimitListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamDeviceOrderLimitListBoResult boResult = new SaveExamDeviceOrderLimitListBoResult();
        ExamDeviceBO examDeviceBO =
                createSaveExamDeviceOrderLimitListOnDuplicateUpdate(
                        boResult, saveExamDeviceOrderLimitListBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamDeviceOrderLimitListBto, "__$validPropertySet"),
                    "examDeviceOrderLimitBtoList")) {
                createExamDeviceOrderLimitBtoOnDuplicateUpdate(
                        boResult, saveExamDeviceOrderLimitListBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 更新对象:changeExamDeviceChargeItemEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateChangeExamDeviceChargeItemEnableFlagOnMissThrowEx(
            ChangeExamDeviceChargeItemEnableFlagBoResult boResult,
            ChangeExamDeviceChargeItemEnableFlagBto changeExamDeviceChargeItemEnableFlagBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamDeviceChargeItemEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(changeExamDeviceChargeItemEnableFlagBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(changeExamDeviceChargeItemEnableFlagBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新对象:changeExamDeviceEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateChangeExamDeviceEnableFlagOnMissThrowEx(
            BaseExamDeviceBOService.ChangeExamDeviceEnableFlagBoResult boResult,
            ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamDeviceEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(changeExamDeviceEnableFlagBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(changeExamDeviceEnableFlagBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamDeviceEnableFlagBto, "__$validPropertySet"),
                    "enableFlag")) {
                examDeviceBO.setEnableFlag(changeExamDeviceEnableFlagBto.getEnableFlag());
            }
            return examDeviceBO;
        }
    }

    /** 更新对象:changeExamDeviceOrderLimitEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateChangeExamDeviceOrderLimitEnableFlagOnMissThrowEx(
            ChangeExamDeviceOrderLimitEnableFlagBoResult boResult,
            ChangeExamDeviceOrderLimitEnableFlagBto changeExamDeviceOrderLimitEnableFlagBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamDeviceOrderLimitEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(changeExamDeviceOrderLimitEnableFlagBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(changeExamDeviceOrderLimitEnableFlagBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新对象:createExamDeviceChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateCreateExamDeviceChargeItemOnMissThrowEx(
            BaseExamDeviceBOService.CreateExamDeviceChargeItemBoResult boResult,
            CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createExamDeviceChargeItemBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(createExamDeviceChargeItemBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(createExamDeviceChargeItemBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新对象:createExamDeviceOrderLimit,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateCreateExamDeviceOrderLimitOnMissThrowEx(
            BaseExamDeviceBOService.CreateExamDeviceOrderLimitBoResult boResult,
            CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createExamDeviceOrderLimitBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(createExamDeviceOrderLimitBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(createExamDeviceOrderLimitBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新检查设备 */
    @AutoGenerated(locked = true)
    protected UpdateExamDeviceBoResult updateExamDeviceBase(
            UpdateExamDeviceBto updateExamDeviceBto) {
        if (updateExamDeviceBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamDeviceBoResult boResult = new UpdateExamDeviceBoResult();
        ExamDeviceBO examDeviceBO =
                updateUpdateExamDeviceOnMissThrowEx(boResult, updateExamDeviceBto);
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 更新检查设备收费项目 */
    @AutoGenerated(locked = true)
    protected UpdateExamDeviceChargeItemBoResult updateExamDeviceChargeItemBase(
            UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto) {
        if (updateExamDeviceChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamDeviceChargeItemBoResult boResult = new UpdateExamDeviceChargeItemBoResult();
        ExamDeviceBO examDeviceBO =
                updateUpdateExamDeviceChargeItemOnMissThrowEx(
                        boResult, updateExamDeviceChargeItemBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamDeviceChargeItemBto, "__$validPropertySet"),
                    "examDeviceChargeItemBtoList")) {
                updateExamDeviceChargeItemBtoOnMissThrowEx(
                        boResult, updateExamDeviceChargeItemBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 更新对象:examDeviceChargeItemBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamDeviceChargeItemBtoOnMissThrowEx(
            BaseExamDeviceBOService.ChangeExamDeviceChargeItemEnableFlagBoResult boResult,
            ChangeExamDeviceChargeItemEnableFlagBto changeExamDeviceChargeItemEnableFlagBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                changeExamDeviceChargeItemEnableFlagBto.getExamDeviceChargeItemBtoList())) {
            for (ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto bto :
                    changeExamDeviceChargeItemEnableFlagBto.getExamDeviceChargeItemBtoList()) {
                Optional<ExamDeviceChargeItemBO> any =
                        examDeviceBO.getExamDeviceChargeItemBOSet().stream()
                                .filter(
                                        examDeviceChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examDeviceChargeItemBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamDeviceChargeItemBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamDeviceChargeItem());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:examDeviceChargeItemBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamDeviceChargeItemBtoOnMissThrowEx(
            BaseExamDeviceBOService.UpdateExamDeviceChargeItemBoResult boResult,
            UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                updateExamDeviceChargeItemBto.getExamDeviceChargeItemBtoList())) {
            for (UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto bto :
                    updateExamDeviceChargeItemBto.getExamDeviceChargeItemBtoList()) {
                Optional<ExamDeviceChargeItemBO> any =
                        examDeviceBO.getExamDeviceChargeItemBOSet().stream()
                                .filter(
                                        examDeviceChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examDeviceChargeItemBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamDeviceChargeItemBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamDeviceChargeItem());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "chargeItemId")) {
                        bo.setChargeItemId(bto.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "campusIdList")) {
                        bo.setCampusIdList(bto.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "chargeItemCount")) {
                        bo.setChargeItemCount(bto.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "filmFeeType")) {
                        bo.setFilmFeeType(bto.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        bo.setGraphicFeeFlag(bto.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        bo.setDigitalImagingFeeFlag(bto.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "useScopeList")) {
                        bo.setUseScopeList(bto.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updatedBy")) {
                        bo.setUpdatedBy(bto.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedFlag")) {
                        bo.setEnhancedFlag(bto.getEnhancedFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 修改检查设备开单限制 */
    @AutoGenerated(locked = true)
    protected UpdateExamDeviceOrderLimitBoResult updateExamDeviceOrderLimitBase(
            UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto) {
        if (updateExamDeviceOrderLimitBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamDeviceOrderLimitBoResult boResult = new UpdateExamDeviceOrderLimitBoResult();
        ExamDeviceBO examDeviceBO =
                updateUpdateExamDeviceOrderLimitOnMissThrowEx(
                        boResult, updateExamDeviceOrderLimitBto);
        if (examDeviceBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamDeviceOrderLimitBto, "__$validPropertySet"),
                    "examDeviceOrderLimitBtoList")) {
                updateExamDeviceOrderLimitBtoOnMissThrowEx(
                        boResult, updateExamDeviceOrderLimitBto, examDeviceBO);
            }
        }
        boResult.setRootBo(examDeviceBO);
        return boResult;
    }

    /** 更新对象:examDeviceOrderLimitBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamDeviceOrderLimitBtoOnMissThrowEx(
            BaseExamDeviceBOService.ChangeExamDeviceOrderLimitEnableFlagBoResult boResult,
            ChangeExamDeviceOrderLimitEnableFlagBto changeExamDeviceOrderLimitEnableFlagBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                changeExamDeviceOrderLimitEnableFlagBto.getExamDeviceOrderLimitBtoList())) {
            for (ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto bto :
                    changeExamDeviceOrderLimitEnableFlagBto.getExamDeviceOrderLimitBtoList()) {
                Optional<ExamDeviceOrderLimitBO> any =
                        examDeviceBO.getExamDeviceOrderLimitBOSet().stream()
                                .filter(
                                        examDeviceOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examDeviceOrderLimitBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamDeviceOrderLimitBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamDeviceOrderLimit());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:examDeviceOrderLimitBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamDeviceOrderLimitBtoOnMissThrowEx(
            BaseExamDeviceBOService.UpdateExamDeviceOrderLimitBoResult boResult,
            UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto,
            ExamDeviceBO examDeviceBO) {
        if (CollectionUtil.isNotEmpty(
                updateExamDeviceOrderLimitBto.getExamDeviceOrderLimitBtoList())) {
            for (UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto bto :
                    updateExamDeviceOrderLimitBto.getExamDeviceOrderLimitBtoList()) {
                Optional<ExamDeviceOrderLimitBO> any =
                        examDeviceBO.getExamDeviceOrderLimitBOSet().stream()
                                .filter(
                                        examDeviceOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examDeviceOrderLimitBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamDeviceOrderLimitBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamDeviceOrderLimit());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "campusIdList")) {
                        bo.setCampusIdList(bto.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "useScopeList")) {
                        bo.setUseScopeList(bto.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partCountCalculationDay")) {
                        bo.setPartCountCalculationDay(bto.getPartCountCalculationDay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partMaxCount")) {
                        bo.setPartMaxCount(bto.getPartMaxCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partValidCount")) {
                        bo.setPartValidCount(bto.getPartValidCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedCount")) {
                        bo.setEnhancedCount(bto.getEnhancedCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedScanningMutexFlag")) {
                        bo.setEnhancedScanningMutexFlag(bto.getEnhancedScanningMutexFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updatedBy")) {
                        bo.setUpdatedBy(bto.getUpdatedBy());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:saveExamDeviceDocumentTemplate,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateSaveExamDeviceDocumentTemplateOnMissThrowEx(
            BaseExamDeviceBOService.SaveExamDeviceDocumentTemplateBoResult boResult,
            SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamDeviceDocumentTemplateBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(saveExamDeviceDocumentTemplateBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(saveExamDeviceDocumentTemplateBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新对象:updateExamDeviceChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateUpdateExamDeviceChargeItemOnMissThrowEx(
            UpdateExamDeviceChargeItemBoResult boResult,
            UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamDeviceChargeItemBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(updateExamDeviceChargeItemBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(updateExamDeviceChargeItemBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    /** 更新对象:updateExamDevice,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateUpdateExamDeviceOnMissThrowEx(
            BaseExamDeviceBOService.UpdateExamDeviceBoResult boResult,
            UpdateExamDeviceBto updateExamDeviceBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamDeviceBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(updateExamDeviceBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(updateExamDeviceBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "examTypeId")) {
                examDeviceBO.setExamTypeId(updateExamDeviceBto.getExamTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "sortNumber")) {
                examDeviceBO.setSortNumber(updateExamDeviceBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "deviceId")) {
                examDeviceBO.setDeviceId(updateExamDeviceBto.getDeviceId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "deviceName")) {
                examDeviceBO.setDeviceName(updateExamDeviceBto.getDeviceName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "inputCode")) {
                examDeviceBO.setInputCode(updateExamDeviceBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "campusIdList")) {
                examDeviceBO.setCampusIdList(updateExamDeviceBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "billingMode")) {
                examDeviceBO.setBillingMode(updateExamDeviceBto.getBillingMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamDeviceBto, "__$validPropertySet"),
                    "updatedBy")) {
                examDeviceBO.setUpdatedBy(updateExamDeviceBto.getUpdatedBy());
            }
            return examDeviceBO;
        }
    }

    /** 更新对象:updateExamDeviceOrderLimit,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamDeviceBO updateUpdateExamDeviceOrderLimitOnMissThrowEx(
            UpdateExamDeviceOrderLimitBoResult boResult,
            UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto) {
        ExamDeviceBO examDeviceBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamDeviceOrderLimitBto.getId() == null);
        if (!allNull && !found) {
            examDeviceBO = ExamDeviceBO.getById(updateExamDeviceOrderLimitBto.getId());
            found = true;
        }
        if (examDeviceBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examDeviceBO.convertToExamDevice());
            updatedBto.setBto(updateExamDeviceOrderLimitBto);
            updatedBto.setBo(examDeviceBO);
            boResult.getUpdatedList().add(updatedBto);
            return examDeviceBO;
        }
    }

    public static class CreateExamDeviceBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamDeviceBto, ExamDeviceBO> getCreatedBto(
                CreateExamDeviceBto createExamDeviceBto) {
            return this.getAddedResult(createExamDeviceBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplateBO>
                getCreatedBto(
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return this.getAddedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamDeviceBto.ExamDeviceChargeItemBto, ExamDeviceChargeItemBO>
                getCreatedBto(CreateExamDeviceBto.ExamDeviceChargeItemBto examDeviceChargeItemBto) {
            return this.getAddedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamDeviceBto.ExamDeviceOrderLimitBto, ExamDeviceOrderLimitBO>
                getCreatedBto(CreateExamDeviceBto.ExamDeviceOrderLimitBto examDeviceOrderLimitBto) {
            return this.getAddedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public ExamDeviceDocumentTemplate getDeleted_ExamDeviceDocumentTemplate() {
            return (ExamDeviceDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamDeviceDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public ExamDeviceChargeItem getDeleted_ExamDeviceChargeItem() {
            return (ExamDeviceChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamDeviceOrderLimit getDeleted_ExamDeviceOrderLimit() {
            return (ExamDeviceOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamDeviceBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                CreateExamDeviceBto createExamDeviceBto) {
            return super.getUpdatedResult(createExamDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplate,
                        ExamDeviceDocumentTemplateBO>
                getUpdatedBto(
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return super.getUpdatedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamDeviceBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItem,
                        ExamDeviceChargeItemBO>
                getUpdatedBto(CreateExamDeviceBto.ExamDeviceChargeItemBto examDeviceChargeItemBto) {
            return super.getUpdatedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamDeviceBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimit,
                        ExamDeviceOrderLimitBO>
                getUpdatedBto(CreateExamDeviceBto.ExamDeviceOrderLimitBto examDeviceOrderLimitBto) {
            return super.getUpdatedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamDeviceBto, ExamDeviceBO> getUnmodifiedBto(
                CreateExamDeviceBto createExamDeviceBto) {
            return super.getUnmodifiedResult(createExamDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplateBO>
                getUnmodifiedBto(
                        CreateExamDeviceBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return super.getUnmodifiedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamDeviceBto.ExamDeviceChargeItemBto, ExamDeviceChargeItemBO>
                getUnmodifiedBto(
                        CreateExamDeviceBto.ExamDeviceChargeItemBto examDeviceChargeItemBto) {
            return super.getUnmodifiedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamDeviceBto.ExamDeviceOrderLimitBto, ExamDeviceOrderLimitBO>
                getUnmodifiedBto(
                        CreateExamDeviceBto.ExamDeviceOrderLimitBto examDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(examDeviceOrderLimitBto);
        }
    }

    public static class UpdateExamDeviceBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamDeviceBto, ExamDeviceBO> getCreatedBto(
                UpdateExamDeviceBto updateExamDeviceBto) {
            return this.getAddedResult(updateExamDeviceBto);
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamDeviceBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                UpdateExamDeviceBto updateExamDeviceBto) {
            return super.getUpdatedResult(updateExamDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamDeviceBto, ExamDeviceBO> getUnmodifiedBto(
                UpdateExamDeviceBto updateExamDeviceBto) {
            return super.getUnmodifiedResult(updateExamDeviceBto);
        }
    }

    public static class ChangeExamDeviceEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamDeviceEnableFlagBto, ExamDeviceBO> getCreatedBto(
                ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
            return this.getAddedResult(changeExamDeviceEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangeExamDeviceEnableFlagBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
            return super.getUpdatedResult(changeExamDeviceEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamDeviceEnableFlagBto, ExamDeviceBO> getUnmodifiedBto(
                ChangeExamDeviceEnableFlagBto changeExamDeviceEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamDeviceEnableFlagBto);
        }
    }

    public static class ChangeExamDeviceOrderLimitEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamDeviceOrderLimitEnableFlagBto, ExamDeviceBO> getCreatedBto(
                ChangeExamDeviceOrderLimitEnableFlagBto changeExamDeviceOrderLimitEnableFlagBto) {
            return this.getAddedResult(changeExamDeviceOrderLimitEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getCreatedBto(
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return this.getAddedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public ExamDeviceOrderLimit getDeleted_ExamDeviceOrderLimit() {
            return (ExamDeviceOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangeExamDeviceOrderLimitEnableFlagBto, ExamDevice, ExamDeviceBO>
                getUpdatedBto(
                        ChangeExamDeviceOrderLimitEnableFlagBto
                                changeExamDeviceOrderLimitEnableFlagBto) {
            return super.getUpdatedResult(changeExamDeviceOrderLimitEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimit,
                        ExamDeviceOrderLimitBO>
                getUpdatedBto(
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUpdatedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamDeviceOrderLimitEnableFlagBto, ExamDeviceBO>
                getUnmodifiedBto(
                        ChangeExamDeviceOrderLimitEnableFlagBto
                                changeExamDeviceOrderLimitEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamDeviceOrderLimitEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getUnmodifiedBto(
                        ChangeExamDeviceOrderLimitEnableFlagBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(examDeviceOrderLimitBto);
        }
    }

    public static class ChangeExamDeviceChargeItemEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getCreatedBto(
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return this.getAddedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamDeviceChargeItemEnableFlagBto, ExamDeviceBO> getCreatedBto(
                ChangeExamDeviceChargeItemEnableFlagBto changeExamDeviceChargeItemEnableFlagBto) {
            return this.getAddedResult(changeExamDeviceChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceChargeItem getDeleted_ExamDeviceChargeItem() {
            return (ExamDeviceChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItem,
                        ExamDeviceChargeItemBO>
                getUpdatedBto(
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUpdatedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangeExamDeviceChargeItemEnableFlagBto, ExamDevice, ExamDeviceBO>
                getUpdatedBto(
                        ChangeExamDeviceChargeItemEnableFlagBto
                                changeExamDeviceChargeItemEnableFlagBto) {
            return super.getUpdatedResult(changeExamDeviceChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getUnmodifiedBto(
                        ChangeExamDeviceChargeItemEnableFlagBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUnmodifiedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamDeviceChargeItemEnableFlagBto, ExamDeviceBO>
                getUnmodifiedBto(
                        ChangeExamDeviceChargeItemEnableFlagBto
                                changeExamDeviceChargeItemEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamDeviceChargeItemEnableFlagBto);
        }
    }

    public static class SaveExamDeviceDocumentTemplateBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplateBO>
                getCreatedBto(
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return this.getAddedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamDeviceDocumentTemplateBto, ExamDeviceBO> getCreatedBto(
                SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto) {
            return this.getAddedResult(saveExamDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceDocumentTemplate getDeleted_ExamDeviceDocumentTemplate() {
            return (ExamDeviceDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamDeviceDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplate,
                        ExamDeviceDocumentTemplateBO>
                getUpdatedBto(
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return super.getUpdatedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamDeviceDocumentTemplateBto, ExamDevice, ExamDeviceBO>
                getUpdatedBto(SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto) {
            return super.getUpdatedResult(saveExamDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto,
                        ExamDeviceDocumentTemplateBO>
                getUnmodifiedBto(
                        SaveExamDeviceDocumentTemplateBto.ExamDeviceDocumentTemplateBto
                                examDeviceDocumentTemplateBto) {
            return super.getUnmodifiedResult(examDeviceDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamDeviceDocumentTemplateBto, ExamDeviceBO> getUnmodifiedBto(
                SaveExamDeviceDocumentTemplateBto saveExamDeviceDocumentTemplateBto) {
            return super.getUnmodifiedResult(saveExamDeviceDocumentTemplateBto);
        }
    }

    public static class CreateExamDeviceChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getCreatedBto(
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return this.getAddedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamDeviceChargeItemBto, ExamDeviceBO> getCreatedBto(
                CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto) {
            return this.getAddedResult(createExamDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceChargeItem getDeleted_ExamDeviceChargeItem() {
            return (ExamDeviceChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItem,
                        ExamDeviceChargeItemBO>
                getUpdatedBto(
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUpdatedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamDeviceChargeItemBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto) {
            return super.getUpdatedResult(createExamDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getUnmodifiedBto(
                        CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUnmodifiedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamDeviceChargeItemBto, ExamDeviceBO> getUnmodifiedBto(
                CreateExamDeviceChargeItemBto createExamDeviceChargeItemBto) {
            return super.getUnmodifiedResult(createExamDeviceChargeItemBto);
        }
    }

    public static class UpdateExamDeviceChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getCreatedBto(
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return this.getAddedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamDeviceChargeItemBto, ExamDeviceBO> getCreatedBto(
                UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto) {
            return this.getAddedResult(updateExamDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceChargeItem getDeleted_ExamDeviceChargeItem() {
            return (ExamDeviceChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItem,
                        ExamDeviceChargeItemBO>
                getUpdatedBto(
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUpdatedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamDeviceChargeItemBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto) {
            return super.getUpdatedResult(updateExamDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getUnmodifiedBto(
                        UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUnmodifiedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamDeviceChargeItemBto, ExamDeviceBO> getUnmodifiedBto(
                UpdateExamDeviceChargeItemBto updateExamDeviceChargeItemBto) {
            return super.getUnmodifiedResult(updateExamDeviceChargeItemBto);
        }
    }

    public static class CreateExamDeviceOrderLimitBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getCreatedBto(
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return this.getAddedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamDeviceOrderLimitBto, ExamDeviceBO> getCreatedBto(
                CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto) {
            return this.getAddedResult(createExamDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceOrderLimit getDeleted_ExamDeviceOrderLimit() {
            return (ExamDeviceOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimit,
                        ExamDeviceOrderLimitBO>
                getUpdatedBto(
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUpdatedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamDeviceOrderLimitBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto) {
            return super.getUpdatedResult(createExamDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getUnmodifiedBto(
                        CreateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamDeviceOrderLimitBto, ExamDeviceBO> getUnmodifiedBto(
                CreateExamDeviceOrderLimitBto createExamDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(createExamDeviceOrderLimitBto);
        }
    }

    public static class UpdateExamDeviceOrderLimitBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getCreatedBto(
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return this.getAddedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamDeviceOrderLimitBto, ExamDeviceBO> getCreatedBto(
                UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto) {
            return this.getAddedResult(updateExamDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceOrderLimit getDeleted_ExamDeviceOrderLimit() {
            return (ExamDeviceOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimit,
                        ExamDeviceOrderLimitBO>
                getUpdatedBto(
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUpdatedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamDeviceOrderLimitBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto) {
            return super.getUpdatedResult(updateExamDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getUnmodifiedBto(
                        UpdateExamDeviceOrderLimitBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamDeviceOrderLimitBto, ExamDeviceBO> getUnmodifiedBto(
                UpdateExamDeviceOrderLimitBto updateExamDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(updateExamDeviceOrderLimitBto);
        }
    }

    public static class SaveExamDeviceChargeItemListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getCreatedBto(
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return this.getAddedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamDeviceChargeItemListBto, ExamDeviceBO> getCreatedBto(
                SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
            return this.getAddedResult(saveExamDeviceChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceChargeItem getDeleted_ExamDeviceChargeItem() {
            return (ExamDeviceChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItem,
                        ExamDeviceChargeItemBO>
                getUpdatedBto(
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUpdatedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamDeviceChargeItemListBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
            return super.getUpdatedResult(saveExamDeviceChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto,
                        ExamDeviceChargeItemBO>
                getUnmodifiedBto(
                        SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto
                                examDeviceChargeItemBto) {
            return super.getUnmodifiedResult(examDeviceChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamDeviceChargeItemListBto, ExamDeviceBO> getUnmodifiedBto(
                SaveExamDeviceChargeItemListBto saveExamDeviceChargeItemListBto) {
            return super.getUnmodifiedResult(saveExamDeviceChargeItemListBto);
        }
    }

    public static class SaveExamDeviceOrderLimitListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamDeviceBO getRootBo() {
            return (ExamDeviceBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getCreatedBto(
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return this.getAddedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamDeviceOrderLimitListBto, ExamDeviceBO> getCreatedBto(
                SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
            return this.getAddedResult(saveExamDeviceOrderLimitListBto);
        }

        @AutoGenerated(locked = true)
        public ExamDeviceOrderLimit getDeleted_ExamDeviceOrderLimit() {
            return (ExamDeviceOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDeviceOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamDevice getDeleted_ExamDevice() {
            return (ExamDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimit,
                        ExamDeviceOrderLimitBO>
                getUpdatedBto(
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUpdatedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamDeviceOrderLimitListBto, ExamDevice, ExamDeviceBO> getUpdatedBto(
                SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
            return super.getUpdatedResult(saveExamDeviceOrderLimitListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto,
                        ExamDeviceOrderLimitBO>
                getUnmodifiedBto(
                        SaveExamDeviceOrderLimitListBto.ExamDeviceOrderLimitBto
                                examDeviceOrderLimitBto) {
            return super.getUnmodifiedResult(examDeviceOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamDeviceOrderLimitListBto, ExamDeviceBO> getUnmodifiedBto(
                SaveExamDeviceOrderLimitListBto saveExamDeviceOrderLimitListBto) {
            return super.getUnmodifiedResult(saveExamDeviceOrderLimitListBto);
        }
    }
}
