package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.ExamTypeDictionaryBO;
import com.pulse.dictionary_business.persist.dos.ExamTypeBodyDictionary;
import com.pulse.dictionary_business.persist.dos.ExamTypeChargeItem;
import com.pulse.dictionary_business.persist.dos.ExamTypeDictionary;
import com.pulse.dictionary_business.persist.dos.ExamTypeDocumentTemplate;
import com.pulse.dictionary_business.persist.dos.ExamTypeMethodDictionary;
import com.pulse.dictionary_business.persist.dos.ExamTypeOrderLimit;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.ChangeExamTypeChargeItemEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.ChangeExamTypeDictionaryEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.ChangeExamTypeOrderLimitEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.CreateExamTypeChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.CreateExamTypeDictionaryBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.CreateExamTypeOrderLimitBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.SaveExamTypeChargeItemListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.SaveExamTypeDocumentTemplateBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.SaveExamTypeOrderLimitListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.UpdateExamTypeChargeItemBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.UpdateExamTypeDictionaryBoResult;
import com.pulse.dictionary_business.service.base.BaseExamTypeDictionaryBOService.UpdateExamTypeOrderLimitBoResult;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeChargeItemEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeDictionaryEnableFlagBto;
import com.pulse.dictionary_business.service.bto.ChangeExamTypeOrderLimitEnableFlagBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeDictionaryBto;
import com.pulse.dictionary_business.service.bto.CreateExamTypeOrderLimitBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeChargeItemListBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeDocumentTemplateBto;
import com.pulse.dictionary_business.service.bto.SaveExamTypeOrderLimitListBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeChargeItemBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeDictionaryBto;
import com.pulse.dictionary_business.service.bto.UpdateExamTypeOrderLimitBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "d079923d-071c-3881-9cbb-10bcb29b0ecf")
public class BaseExamTypeDictionaryBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 修改检查类型收费项目启用标识 */
    @AutoGenerated(locked = true)
    protected ChangeExamTypeChargeItemEnableFlagBoResult changeExamTypeChargeItemEnableFlagBase(
            ChangeExamTypeChargeItemEnableFlagBto changeExamTypeChargeItemEnableFlagBto) {
        if (changeExamTypeChargeItemEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamTypeChargeItemEnableFlagBoResult boResult =
                new ChangeExamTypeChargeItemEnableFlagBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateChangeExamTypeChargeItemEnableFlagOnMissThrowEx(
                        boResult, changeExamTypeChargeItemEnableFlagBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamTypeChargeItemEnableFlagBto, "__$validPropertySet"),
                    "examTypeChargeItemBtoList")) {
                updateExamTypeChargeItemBtoOnMissThrowEx(
                        boResult, changeExamTypeChargeItemEnableFlagBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 修改检查类型字典启用标识 */
    @AutoGenerated(locked = true)
    protected ChangeExamTypeDictionaryEnableFlagBoResult changeExamTypeDictionaryEnableFlagBase(
            ChangeExamTypeDictionaryEnableFlagBto changeExamTypeDictionaryEnableFlagBto) {
        if (changeExamTypeDictionaryEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamTypeDictionaryEnableFlagBoResult boResult =
                new ChangeExamTypeDictionaryEnableFlagBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateChangeExamTypeDictionaryEnableFlagOnMissThrowEx(
                        boResult, changeExamTypeDictionaryEnableFlagBto);
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 修改检查类型开单限制停用标识 */
    @AutoGenerated(locked = true)
    protected ChangeExamTypeOrderLimitEnableFlagBoResult changeExamTypeOrderLimitEnableFlagBase(
            ChangeExamTypeOrderLimitEnableFlagBto changeExamTypeOrderLimitEnableFlagBto) {
        if (changeExamTypeOrderLimitEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeExamTypeOrderLimitEnableFlagBoResult boResult =
                new ChangeExamTypeOrderLimitEnableFlagBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateChangeExamTypeOrderLimitEnableFlagOnMissThrowEx(
                        boResult, changeExamTypeOrderLimitEnableFlagBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamTypeOrderLimitEnableFlagBto, "__$validPropertySet"),
                    "examTypeOrderLimitBtoList")) {
                updateExamTypeOrderLimitBtoOnMissThrowEx(
                        boResult, changeExamTypeOrderLimitEnableFlagBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO createCreateExamTypeDictionaryOnDuplicateThrowEx(
            CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createExamTypeDictionaryBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(createExamTypeDictionaryBto.getId());
            if (examTypeDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examTypeDictionaryBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        examTypeDictionaryBO.getId(),
                        "exam_type_dictionary");
                throw new IgnoredException(400, "检查类型字典已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "exam_type_dictionary",
                        examTypeDictionaryBO.getId(),
                        "exam_type_dictionary");
                throw new IgnoredException(400, "检查类型字典已存在");
            }
        } else {
            examTypeDictionaryBO = new ExamTypeDictionaryBO();
            if (pkExist) {
                examTypeDictionaryBO.setId(createExamTypeDictionaryBto.getId());
            } else {
                examTypeDictionaryBO.setId(
                        String.valueOf(this.idGenerator.allocateId("exam_type_dictionary")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "sortNumber")) {
                examTypeDictionaryBO.setSortNumber(createExamTypeDictionaryBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeCode")) {
                examTypeDictionaryBO.setExamTypeCode(createExamTypeDictionaryBto.getExamTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeName")) {
                examTypeDictionaryBO.setExamTypeName(createExamTypeDictionaryBto.getExamTypeName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "inputCode")) {
                examTypeDictionaryBO.setInputCode(createExamTypeDictionaryBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "standardExamTypeCode")) {
                examTypeDictionaryBO.setStandardExamTypeCode(
                        createExamTypeDictionaryBto.getStandardExamTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "standardExamTypeName")) {
                examTypeDictionaryBO.setStandardExamTypeName(
                        createExamTypeDictionaryBto.getStandardExamTypeName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "enableFlag")) {
                examTypeDictionaryBO.setEnableFlag(createExamTypeDictionaryBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "campusIdList")) {
                examTypeDictionaryBO.setCampusIdList(createExamTypeDictionaryBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "useScopeList")) {
                examTypeDictionaryBO.setUseScopeList(createExamTypeDictionaryBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "billingMode")) {
                examTypeDictionaryBO.setBillingMode(createExamTypeDictionaryBto.getBillingMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "everyVisitMaxPartCount")) {
                examTypeDictionaryBO.setEveryVisitMaxPartCount(
                        createExamTypeDictionaryBto.getEveryVisitMaxPartCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "everyDayMaxPartCount")) {
                examTypeDictionaryBO.setEveryDayMaxPartCount(
                        createExamTypeDictionaryBto.getEveryDayMaxPartCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "createdBy")) {
                examTypeDictionaryBO.setCreatedBy(createExamTypeDictionaryBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "updatedBy")) {
                examTypeDictionaryBO.setUpdatedBy(createExamTypeDictionaryBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "partSplitFlag")) {
                examTypeDictionaryBO.setPartSplitFlag(
                        createExamTypeDictionaryBto.getPartSplitFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "parentId")) {
                examTypeDictionaryBO.setParentId(createExamTypeDictionaryBto.getParentId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createExamTypeDictionaryBto);
            addedBto.setBo(examTypeDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examTypeDictionaryBO;
    }

    /** 创建对象ExamTypeBodyDictionaryBto */
    @AutoGenerated(locked = true)
    private void createExamTypeBodyDictionaryBto(
            CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createExamTypeDictionaryBto.getExamTypeBodyDictionaryBtoList())) {
            for (CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto item :
                    createExamTypeDictionaryBto.getExamTypeBodyDictionaryBtoList()) {
                ExamTypeBodyDictionaryBO subBo = new ExamTypeBodyDictionaryBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examBodyCode")) {
                    subBo.setExamBodyCode(item.getExamBodyCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examBodyName")) {
                    subBo.setExamBodyName(item.getExamBodyName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "inputCode")) {
                    subBo.setInputCode(item.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                subBo.setId(
                        String.valueOf(this.idGenerator.allocateId("exam_type_body_dictionary")));
                examTypeDictionaryBO.getExamTypeBodyDictionaryBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建检查类型收费项目（追加新增） */
    @AutoGenerated(locked = true)
    protected CreateExamTypeChargeItemBoResult createExamTypeChargeItemBase(
            CreateExamTypeChargeItemBto createExamTypeChargeItemBto) {
        if (createExamTypeChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamTypeChargeItemBoResult boResult = new CreateExamTypeChargeItemBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateCreateExamTypeChargeItemOnMissThrowEx(boResult, createExamTypeChargeItemBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeChargeItemBto, "__$validPropertySet"),
                    "examTypeChargeItemBtoList")) {
                createExamTypeChargeItemBtoOnDuplicateThrowEx(
                        boResult, createExamTypeChargeItemBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 创建对象ExamTypeChargeItemBto */
    @AutoGenerated(locked = true)
    private void createExamTypeChargeItemBto(
            CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(createExamTypeDictionaryBto.getExamTypeChargeItemBtoList())) {
            for (CreateExamTypeDictionaryBto.ExamTypeChargeItemBto item :
                    createExamTypeDictionaryBto.getExamTypeChargeItemBtoList()) {
                ExamTypeChargeItemBO subBo = new ExamTypeChargeItemBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemId")) {
                    subBo.setChargeItemId(item.getChargeItemId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargeItemCount")) {
                    subBo.setChargeItemCount(item.getChargeItemCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "filmFeeType")) {
                    subBo.setFilmFeeType(item.getFilmFeeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "graphicFeeFlag")) {
                    subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "digitalImagingFeeFlag")) {
                    subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enableFlag")) {
                    subBo.setEnableFlag(item.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "chargePartNumber")) {
                    subBo.setChargePartNumber(item.getChargePartNumber());
                }
                subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_type_charge_item")));
                examTypeDictionaryBO.getExamTypeChargeItemBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamTypeChargeItemBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createExamTypeChargeItemBtoOnDuplicateThrowEx(
            CreateExamTypeChargeItemBoResult boResult,
            CreateExamTypeChargeItemBto createExamTypeChargeItemBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(createExamTypeChargeItemBto.getExamTypeChargeItemBtoList())) {
            for (CreateExamTypeChargeItemBto.ExamTypeChargeItemBto item :
                    createExamTypeChargeItemBto.getExamTypeChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamTypeChargeItemBO> any =
                        examTypeDictionaryBO.getExamTypeChargeItemBOSet().stream()
                                .filter(
                                        examTypeChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examTypeChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "exam_type_charge_item");
                        throw new IgnoredException(400, "检查类型收费项目已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "exam_type_charge_item",
                                any.get().getId());
                        throw new IgnoredException(400, "检查类型收费项目已存在");
                    }
                } else {
                    ExamTypeChargeItemBO subBo = new ExamTypeChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedFlag")) {
                        subBo.setEnhancedFlag(item.getEnhancedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargePartNumber")) {
                        subBo.setChargePartNumber(item.getChargePartNumber());
                    }
                    subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("exam_type_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examTypeDictionaryBO.getExamTypeChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ExamTypeChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamTypeChargeItemBtoOnDuplicateUpdate(
            BaseExamTypeDictionaryBOService.SaveExamTypeChargeItemListBoResult boResult,
            SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isEmpty(saveExamTypeChargeItemListBto.getExamTypeChargeItemBtoList())) {
            saveExamTypeChargeItemListBto.setExamTypeChargeItemBtoList(List.of());
        }
        examTypeDictionaryBO
                .getExamTypeChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamTypeChargeItemListBto
                                            .getExamTypeChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    examTypeChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examTypeChargeItemBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examTypeChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamTypeChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveExamTypeChargeItemListBto.getExamTypeChargeItemBtoList())) {
            for (SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto item :
                    saveExamTypeChargeItemListBto.getExamTypeChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamTypeChargeItemBO> any =
                        examTypeDictionaryBO.getExamTypeChargeItemBOSet().stream()
                                .filter(
                                        examTypeChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examTypeChargeItemBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamTypeChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamTypeChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedFlag")) {
                            bo.setEnhancedFlag(item.getEnhancedFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargePartNumber")) {
                            bo.setChargePartNumber(item.getChargePartNumber());
                        }
                    } else {
                        ExamTypeChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamTypeChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemCount")) {
                            bo.setChargeItemCount(item.getChargeItemCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "filmFeeType")) {
                            bo.setFilmFeeType(item.getFilmFeeType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graphicFeeFlag")) {
                            bo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "digitalImagingFeeFlag")) {
                            bo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedFlag")) {
                            bo.setEnhancedFlag(item.getEnhancedFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargePartNumber")) {
                            bo.setChargePartNumber(item.getChargePartNumber());
                        }
                    }
                } else {
                    ExamTypeChargeItemBO subBo = new ExamTypeChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemCount")) {
                        subBo.setChargeItemCount(item.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "filmFeeType")) {
                        subBo.setFilmFeeType(item.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        subBo.setGraphicFeeFlag(item.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        subBo.setDigitalImagingFeeFlag(item.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedFlag")) {
                        subBo.setEnhancedFlag(item.getEnhancedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargePartNumber")) {
                        subBo.setChargePartNumber(item.getChargePartNumber());
                    }
                    subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("exam_type_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examTypeDictionaryBO.getExamTypeChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建检查类型字典 */
    @AutoGenerated(locked = true)
    protected CreateExamTypeDictionaryBoResult createExamTypeDictionaryBase(
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {
        if (createExamTypeDictionaryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamTypeDictionaryBoResult boResult = new CreateExamTypeDictionaryBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                createCreateExamTypeDictionaryOnDuplicateThrowEx(
                        boResult, createExamTypeDictionaryBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeDocumentTemplateBto")) {
                createExamTypeDocumentTemplateBtoOnDuplicateThrowEx(
                        boResult, createExamTypeDictionaryBto, examTypeDictionaryBO);
            }
        }
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeMethodDictionaryBtoList")) {
                createExamTypeMethodDictionaryBto(
                        boResult, createExamTypeDictionaryBto, examTypeDictionaryBO);
            }
        }
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeOrderLimitBtoList")) {
                createExamTypeOrderLimitBtoOnDuplicateThrowEx(
                        boResult, createExamTypeDictionaryBto, examTypeDictionaryBO);
            }
        }
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeBodyDictionaryBtoList")) {
                createExamTypeBodyDictionaryBto(
                        boResult, createExamTypeDictionaryBto, examTypeDictionaryBO);
            }
        }
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeChargeItemBtoList")) {
                createExamTypeChargeItemBto(
                        boResult, createExamTypeDictionaryBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 创建对象:ExamTypeDocumentTemplateBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createExamTypeDocumentTemplateBtoOnDuplicateThrowEx(
            BaseExamTypeDictionaryBOService.CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (examTypeDictionaryBO.getExamTypeDocumentTemplateBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！",
                    examTypeDictionaryBO.getExamTypeDocumentTemplateBO().getId(),
                    "exam_type_document_template");
            throw new IgnoredException(400, "检查类型文书模板已存在");
        } else {
            if (createExamTypeDictionaryBto.getExamTypeDocumentTemplateBto() == null) {
                return;
            }
            ExamTypeDocumentTemplateBO examTypeDocumentTemplateBO =
                    examTypeDictionaryBO.getOrCreateExamTypeDocumentTemplateBO();
            CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto examTypeDocumentTemplateBto =
                    createExamTypeDictionaryBto.getExamTypeDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examTypeDocumentTemplateBO.setOutpatientInformedConsentForm(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examTypeDocumentTemplateBO.setInpatientInformedConsentForm(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examTypeDocumentTemplateBO.setOutpatientApplyTemplate(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examTypeDocumentTemplateBO.setInpatientApplyTemplate(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examTypeDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examTypeDocumentTemplateBO.setPreHospitalApplyTemplate(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examTypeDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examTypeDocumentTemplateBO.setCreatedBy(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "updatedBy")) {
                examTypeDocumentTemplateBO.setUpdatedBy(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examTypeDocumentTemplateBO.setIodineContrastFlag(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examTypeDocumentTemplateBO.setGadoliniumContrastFlag(
                        createExamTypeDictionaryBto
                                .getExamTypeDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examTypeDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_type_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examTypeDocumentTemplateBto);
            addedBto.setBo(examTypeDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:ExamTypeDocumentTemplateBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamTypeDocumentTemplateBtoOnDuplicateUpdate(
            SaveExamTypeDocumentTemplateBoResult boResult,
            SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (examTypeDictionaryBO.getExamTypeDocumentTemplateBO() != null) {
            ExamTypeDocumentTemplateBO bo =
                    examTypeDictionaryBO.getOrCreateExamTypeDocumentTemplateBO();
            SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto bto =
                    saveExamTypeDocumentTemplateBto.getExamTypeDocumentTemplateBto();
            if (bto == null) {
                ExamTypeDocumentTemplate deletedItem =
                        examTypeDictionaryBO
                                .getExamTypeDocumentTemplateBO()
                                .convertToExamTypeDocumentTemplate();
                boResult.getDeletedList().add(deletedItem);
                examTypeDictionaryBO.setExamTypeDocumentTemplateBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToExamTypeDocumentTemplate());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                bo.setOutpatientInformedConsentForm(bto.getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                bo.setInpatientInformedConsentForm(bto.getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                bo.setOutpatientApplyTemplate(bto.getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                bo.setInpatientApplyTemplate(bto.getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                bo.setEmergencyObservationApplyTemplate(bto.getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                bo.setPreHospitalApplyTemplate(bto.getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                bo.setPhysicalExamApplyTemplate(bto.getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdBy")) {
                bo.setCreatedBy(bto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                bo.setIodineContrastFlag(bto.getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                bo.setGadoliniumContrastFlag(bto.getGadoliniumContrastFlag());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (saveExamTypeDocumentTemplateBto.getExamTypeDocumentTemplateBto() == null) {
                return;
            }
            ExamTypeDocumentTemplateBO examTypeDocumentTemplateBO =
                    examTypeDictionaryBO.getOrCreateExamTypeDocumentTemplateBO();
            SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto
                    examTypeDocumentTemplateBto =
                            saveExamTypeDocumentTemplateBto.getExamTypeDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examTypeDocumentTemplateBO.setOutpatientInformedConsentForm(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examTypeDocumentTemplateBO.setInpatientInformedConsentForm(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examTypeDocumentTemplateBO.setOutpatientApplyTemplate(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examTypeDocumentTemplateBO.setInpatientApplyTemplate(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examTypeDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examTypeDocumentTemplateBO.setPreHospitalApplyTemplate(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examTypeDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examTypeDocumentTemplateBO.setCreatedBy(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "updatedBy")) {
                examTypeDocumentTemplateBO.setUpdatedBy(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examTypeDocumentTemplateBO.setIodineContrastFlag(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examTypeDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examTypeDocumentTemplateBO.setGadoliniumContrastFlag(
                        saveExamTypeDocumentTemplateBto
                                .getExamTypeDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examTypeDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_type_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examTypeDocumentTemplateBto);
            addedBto.setBo(examTypeDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象ExamTypeMethodDictionaryBto */
    @AutoGenerated(locked = true)
    private void createExamTypeMethodDictionaryBto(
            CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                createExamTypeDictionaryBto.getExamTypeMethodDictionaryBtoList())) {
            for (CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto item :
                    createExamTypeDictionaryBto.getExamTypeMethodDictionaryBtoList()) {
                ExamTypeMethodDictionaryBO subBo = new ExamTypeMethodDictionaryBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examMethodCode")) {
                    subBo.setExamMethodCode(item.getExamMethodCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examMethodName")) {
                    subBo.setExamMethodName(item.getExamMethodName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "inputCode")) {
                    subBo.setInputCode(item.getInputCode());
                }
                subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                subBo.setId(
                        String.valueOf(this.idGenerator.allocateId("exam_type_method_dictionary")));
                examTypeDictionaryBO.getExamTypeMethodDictionaryBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建检查类型开单限制 */
    @AutoGenerated(locked = true)
    protected CreateExamTypeOrderLimitBoResult createExamTypeOrderLimitBase(
            CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto) {
        if (createExamTypeOrderLimitBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamTypeOrderLimitBoResult boResult = new CreateExamTypeOrderLimitBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateCreateExamTypeOrderLimitOnMissThrowEx(boResult, createExamTypeOrderLimitBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createExamTypeOrderLimitBto, "__$validPropertySet"),
                    "examTypeOrderLimitBtoList")) {
                createExamTypeOrderLimitBto(
                        boResult, createExamTypeOrderLimitBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 创建对象ExamTypeOrderLimitBto */
    @AutoGenerated(locked = true)
    private void createExamTypeOrderLimitBto(
            CreateExamTypeOrderLimitBoResult boResult,
            CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(createExamTypeOrderLimitBto.getExamTypeOrderLimitBtoList())) {
            for (CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto item :
                    createExamTypeOrderLimitBto.getExamTypeOrderLimitBtoList()) {
                ExamTypeOrderLimitBO subBo = new ExamTypeOrderLimitBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partCountCalculationDay")) {
                    subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partMaxCount")) {
                    subBo.setPartMaxCount(item.getPartMaxCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "partValidCount")) {
                    subBo.setPartValidCount(item.getPartValidCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedCount")) {
                    subBo.setEnhancedCount(item.getEnhancedCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enableFlag")) {
                    subBo.setEnableFlag(item.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "enhancedScanningMutexFlag")) {
                    subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "billingNode")) {
                    subBo.setBillingNode(item.getBillingNode());
                }
                subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                subBo.setId(item.getId());
                examTypeDictionaryBO.getExamTypeOrderLimitBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamTypeOrderLimitBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createExamTypeOrderLimitBtoOnDuplicateThrowEx(
            CreateExamTypeDictionaryBoResult boResult,
            CreateExamTypeDictionaryBto createExamTypeDictionaryBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(createExamTypeDictionaryBto.getExamTypeOrderLimitBtoList())) {
            for (CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto item :
                    createExamTypeDictionaryBto.getExamTypeOrderLimitBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamTypeOrderLimitBO> any =
                        examTypeDictionaryBO.getExamTypeOrderLimitBOSet().stream()
                                .filter(
                                        examTypeOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examTypeOrderLimitBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "exam_type_order_limit");
                        throw new IgnoredException(400, "检查类型开单限制已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "exam_type_order_limit",
                                any.get().getId());
                        throw new IgnoredException(400, "检查类型开单限制已存在");
                    }
                } else {
                    ExamTypeOrderLimitBO subBo = new ExamTypeOrderLimitBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partCountCalculationDay")) {
                        subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partMaxCount")) {
                        subBo.setPartMaxCount(item.getPartMaxCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partValidCount")) {
                        subBo.setPartValidCount(item.getPartValidCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedCount")) {
                        subBo.setEnhancedCount(item.getEnhancedCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedScanningMutexFlag")) {
                        subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "billingNode")) {
                        subBo.setBillingNode(item.getBillingNode());
                    }
                    subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("exam_type_order_limit")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examTypeDictionaryBO.getExamTypeOrderLimitBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ExamTypeOrderLimitBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamTypeOrderLimitBtoOnDuplicateUpdate(
            BaseExamTypeDictionaryBOService.SaveExamTypeOrderLimitListBoResult boResult,
            SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isEmpty(saveExamTypeOrderLimitListBto.getExamTypeOrderLimitBtoList())) {
            saveExamTypeOrderLimitListBto.setExamTypeOrderLimitBtoList(List.of());
        }
        examTypeDictionaryBO
                .getExamTypeOrderLimitBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamTypeOrderLimitListBto
                                            .getExamTypeOrderLimitBtoList()
                                            .stream()
                                            .filter(
                                                    examTypeOrderLimitBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examTypeOrderLimitBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examTypeOrderLimitBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamTypeOrderLimit());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveExamTypeOrderLimitListBto.getExamTypeOrderLimitBtoList())) {
            for (SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto item :
                    saveExamTypeOrderLimitListBto.getExamTypeOrderLimitBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamTypeOrderLimitBO> any =
                        examTypeDictionaryBO.getExamTypeOrderLimitBOSet().stream()
                                .filter(
                                        examTypeOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examTypeOrderLimitBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamTypeOrderLimitBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamTypeOrderLimit());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partCountCalculationDay")) {
                            bo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partMaxCount")) {
                            bo.setPartMaxCount(item.getPartMaxCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partValidCount")) {
                            bo.setPartValidCount(item.getPartValidCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedCount")) {
                            bo.setEnhancedCount(item.getEnhancedCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedScanningMutexFlag")) {
                            bo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "billingNode")) {
                            bo.setBillingNode(item.getBillingNode());
                        }
                    } else {
                        ExamTypeOrderLimitBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamTypeOrderLimit());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partCountCalculationDay")) {
                            bo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partMaxCount")) {
                            bo.setPartMaxCount(item.getPartMaxCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "partValidCount")) {
                            bo.setPartValidCount(item.getPartValidCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedCount")) {
                            bo.setEnhancedCount(item.getEnhancedCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enhancedScanningMutexFlag")) {
                            bo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "billingNode")) {
                            bo.setBillingNode(item.getBillingNode());
                        }
                    }
                } else {
                    ExamTypeOrderLimitBO subBo = new ExamTypeOrderLimitBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partCountCalculationDay")) {
                        subBo.setPartCountCalculationDay(item.getPartCountCalculationDay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partMaxCount")) {
                        subBo.setPartMaxCount(item.getPartMaxCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "partValidCount")) {
                        subBo.setPartValidCount(item.getPartValidCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedCount")) {
                        subBo.setEnhancedCount(item.getEnhancedCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enhancedScanningMutexFlag")) {
                        subBo.setEnhancedScanningMutexFlag(item.getEnhancedScanningMutexFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "billingNode")) {
                        subBo.setBillingNode(item.getBillingNode());
                    }
                    subBo.setExamTypeDictionaryBO(examTypeDictionaryBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("exam_type_order_limit")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examTypeDictionaryBO.getExamTypeOrderLimitBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO createSaveExamTypeChargeItemListOnDuplicateUpdate(
            SaveExamTypeChargeItemListBoResult boResult,
            SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveExamTypeChargeItemListBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(saveExamTypeChargeItemListBto.getId());
            if (examTypeDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examTypeDictionaryBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
                updatedBto.setBto(saveExamTypeChargeItemListBto);
                updatedBto.setBo(examTypeDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
                updatedBto.setBto(saveExamTypeChargeItemListBto);
                updatedBto.setBo(examTypeDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            examTypeDictionaryBO = new ExamTypeDictionaryBO();
            if (pkExist) {
                examTypeDictionaryBO.setId(saveExamTypeChargeItemListBto.getId());
            } else {
                examTypeDictionaryBO.setId(
                        String.valueOf(this.idGenerator.allocateId("exam_type_dictionary")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveExamTypeChargeItemListBto);
            addedBto.setBo(examTypeDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examTypeDictionaryBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO createSaveExamTypeOrderLimitListOnDuplicateUpdate(
            SaveExamTypeOrderLimitListBoResult boResult,
            SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveExamTypeOrderLimitListBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(saveExamTypeOrderLimitListBto.getId());
            if (examTypeDictionaryBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examTypeDictionaryBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
                updatedBto.setBto(saveExamTypeOrderLimitListBto);
                updatedBto.setBo(examTypeDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
                updatedBto.setBto(saveExamTypeOrderLimitListBto);
                updatedBto.setBo(examTypeDictionaryBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            examTypeDictionaryBO = new ExamTypeDictionaryBO();
            if (pkExist) {
                examTypeDictionaryBO.setId(saveExamTypeOrderLimitListBto.getId());
            } else {
                examTypeDictionaryBO.setId(
                        String.valueOf(this.idGenerator.allocateId("exam_type_dictionary")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveExamTypeOrderLimitListBto);
            addedBto.setBo(examTypeDictionaryBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examTypeDictionaryBO;
    }

    /** 保存检查类型收费项目列表 */
    @AutoGenerated(locked = true)
    protected SaveExamTypeChargeItemListBoResult saveExamTypeChargeItemListBase(
            SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
        if (saveExamTypeChargeItemListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamTypeChargeItemListBoResult boResult = new SaveExamTypeChargeItemListBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                createSaveExamTypeChargeItemListOnDuplicateUpdate(
                        boResult, saveExamTypeChargeItemListBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamTypeChargeItemListBto, "__$validPropertySet"),
                    "examTypeChargeItemBtoList")) {
                createExamTypeChargeItemBtoOnDuplicateUpdate(
                        boResult, saveExamTypeChargeItemListBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 保存检查类型文书模板 */
    @AutoGenerated(locked = true)
    protected SaveExamTypeDocumentTemplateBoResult saveExamTypeDocumentTemplateBase(
            SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto) {
        if (saveExamTypeDocumentTemplateBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamTypeDocumentTemplateBoResult boResult = new SaveExamTypeDocumentTemplateBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateSaveExamTypeDocumentTemplateOnMissThrowEx(
                        boResult, saveExamTypeDocumentTemplateBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamTypeDocumentTemplateBto, "__$validPropertySet"),
                    "examTypeDocumentTemplateBto")) {
                createExamTypeDocumentTemplateBtoOnDuplicateUpdate(
                        boResult, saveExamTypeDocumentTemplateBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 保存检查类型开单限制 */
    @AutoGenerated(locked = true)
    protected SaveExamTypeOrderLimitListBoResult saveExamTypeOrderLimitListBase(
            SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
        if (saveExamTypeOrderLimitListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamTypeOrderLimitListBoResult boResult = new SaveExamTypeOrderLimitListBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                createSaveExamTypeOrderLimitListOnDuplicateUpdate(
                        boResult, saveExamTypeOrderLimitListBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamTypeOrderLimitListBto, "__$validPropertySet"),
                    "examTypeOrderLimitBtoList")) {
                createExamTypeOrderLimitBtoOnDuplicateUpdate(
                        boResult, saveExamTypeOrderLimitListBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 更新对象:changeExamTypeChargeItemEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateChangeExamTypeChargeItemEnableFlagOnMissThrowEx(
            ChangeExamTypeChargeItemEnableFlagBoResult boResult,
            ChangeExamTypeChargeItemEnableFlagBto changeExamTypeChargeItemEnableFlagBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamTypeChargeItemEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(changeExamTypeChargeItemEnableFlagBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(changeExamTypeChargeItemEnableFlagBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:changeExamTypeDictionaryEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateChangeExamTypeDictionaryEnableFlagOnMissThrowEx(
            BaseExamTypeDictionaryBOService.ChangeExamTypeDictionaryEnableFlagBoResult boResult,
            ChangeExamTypeDictionaryEnableFlagBto changeExamTypeDictionaryEnableFlagBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamTypeDictionaryEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(changeExamTypeDictionaryEnableFlagBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(changeExamTypeDictionaryEnableFlagBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeExamTypeDictionaryEnableFlagBto, "__$validPropertySet"),
                    "enableFlag")) {
                examTypeDictionaryBO.setEnableFlag(
                        changeExamTypeDictionaryEnableFlagBto.getEnableFlag());
            }
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:changeExamTypeOrderLimitEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateChangeExamTypeOrderLimitEnableFlagOnMissThrowEx(
            ChangeExamTypeOrderLimitEnableFlagBoResult boResult,
            ChangeExamTypeOrderLimitEnableFlagBto changeExamTypeOrderLimitEnableFlagBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeExamTypeOrderLimitEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(changeExamTypeOrderLimitEnableFlagBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(changeExamTypeOrderLimitEnableFlagBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:createExamTypeChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateCreateExamTypeChargeItemOnMissThrowEx(
            BaseExamTypeDictionaryBOService.CreateExamTypeChargeItemBoResult boResult,
            CreateExamTypeChargeItemBto createExamTypeChargeItemBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createExamTypeChargeItemBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(createExamTypeChargeItemBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(createExamTypeChargeItemBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:createExamTypeOrderLimit,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateCreateExamTypeOrderLimitOnMissThrowEx(
            BaseExamTypeDictionaryBOService.CreateExamTypeOrderLimitBoResult boResult,
            CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createExamTypeOrderLimitBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(createExamTypeOrderLimitBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(createExamTypeOrderLimitBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新检查类型收费项目 */
    @AutoGenerated(locked = true)
    protected UpdateExamTypeChargeItemBoResult updateExamTypeChargeItemBase(
            UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto) {
        if (updateExamTypeChargeItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamTypeChargeItemBoResult boResult = new UpdateExamTypeChargeItemBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateUpdateExamTypeChargeItemOnMissThrowEx(boResult, updateExamTypeChargeItemBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeChargeItemBto, "__$validPropertySet"),
                    "examTypeChargeItemBtoList")) {
                updateExamTypeChargeItemBtoOnMissThrowEx(
                        boResult, updateExamTypeChargeItemBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 更新对象:examTypeChargeItemBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamTypeChargeItemBtoOnMissThrowEx(
            BaseExamTypeDictionaryBOService.ChangeExamTypeChargeItemEnableFlagBoResult boResult,
            ChangeExamTypeChargeItemEnableFlagBto changeExamTypeChargeItemEnableFlagBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                changeExamTypeChargeItemEnableFlagBto.getExamTypeChargeItemBtoList())) {
            for (ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto bto :
                    changeExamTypeChargeItemEnableFlagBto.getExamTypeChargeItemBtoList()) {
                Optional<ExamTypeChargeItemBO> any =
                        examTypeDictionaryBO.getExamTypeChargeItemBOSet().stream()
                                .filter(
                                        examTypeChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examTypeChargeItemBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamTypeChargeItemBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamTypeChargeItem());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:examTypeChargeItemBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamTypeChargeItemBtoOnMissThrowEx(
            BaseExamTypeDictionaryBOService.UpdateExamTypeChargeItemBoResult boResult,
            UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(updateExamTypeChargeItemBto.getExamTypeChargeItemBtoList())) {
            for (UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto bto :
                    updateExamTypeChargeItemBto.getExamTypeChargeItemBtoList()) {
                Optional<ExamTypeChargeItemBO> any =
                        examTypeDictionaryBO.getExamTypeChargeItemBOSet().stream()
                                .filter(
                                        examTypeChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examTypeChargeItemBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamTypeChargeItemBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamTypeChargeItem());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "chargeItemId")) {
                        bo.setChargeItemId(bto.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "campusIdList")) {
                        bo.setCampusIdList(bto.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "chargeItemCount")) {
                        bo.setChargeItemCount(bto.getChargeItemCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "filmFeeType")) {
                        bo.setFilmFeeType(bto.getFilmFeeType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "graphicFeeFlag")) {
                        bo.setGraphicFeeFlag(bto.getGraphicFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "digitalImagingFeeFlag")) {
                        bo.setDigitalImagingFeeFlag(bto.getDigitalImagingFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "useScopeList")) {
                        bo.setUseScopeList(bto.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updatedBy")) {
                        bo.setUpdatedBy(bto.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedFlag")) {
                        bo.setEnhancedFlag(bto.getEnhancedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "chargePartNumber")) {
                        bo.setChargePartNumber(bto.getChargePartNumber());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新检查类型信息 */
    @AutoGenerated(locked = true)
    protected UpdateExamTypeDictionaryBoResult updateExamTypeDictionaryBase(
            UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
        if (updateExamTypeDictionaryBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamTypeDictionaryBoResult boResult = new UpdateExamTypeDictionaryBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateUpdateExamTypeDictionaryOnMissThrowEx(boResult, updateExamTypeDictionaryBto);
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 更新检查类型开单限制 */
    @AutoGenerated(locked = true)
    protected UpdateExamTypeOrderLimitBoResult updateExamTypeOrderLimitBase(
            UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto) {
        if (updateExamTypeOrderLimitBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamTypeOrderLimitBoResult boResult = new UpdateExamTypeOrderLimitBoResult();
        ExamTypeDictionaryBO examTypeDictionaryBO =
                updateUpdateExamTypeOrderLimitOnMissThrowEx(boResult, updateExamTypeOrderLimitBto);
        if (examTypeDictionaryBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeOrderLimitBto, "__$validPropertySet"),
                    "examTypeOrderLimitBtoList")) {
                updateExamTypeOrderLimitBtoOnMissThrowEx(
                        boResult, updateExamTypeOrderLimitBto, examTypeDictionaryBO);
            }
        }
        boResult.setRootBo(examTypeDictionaryBO);
        return boResult;
    }

    /** 更新对象:examTypeOrderLimitBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamTypeOrderLimitBtoOnMissThrowEx(
            BaseExamTypeDictionaryBOService.ChangeExamTypeOrderLimitEnableFlagBoResult boResult,
            ChangeExamTypeOrderLimitEnableFlagBto changeExamTypeOrderLimitEnableFlagBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(
                changeExamTypeOrderLimitEnableFlagBto.getExamTypeOrderLimitBtoList())) {
            for (ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto bto :
                    changeExamTypeOrderLimitEnableFlagBto.getExamTypeOrderLimitBtoList()) {
                Optional<ExamTypeOrderLimitBO> any =
                        examTypeDictionaryBO.getExamTypeOrderLimitBOSet().stream()
                                .filter(
                                        examTypeOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examTypeOrderLimitBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamTypeOrderLimitBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamTypeOrderLimit());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:examTypeOrderLimitBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateExamTypeOrderLimitBtoOnMissThrowEx(
            BaseExamTypeDictionaryBOService.UpdateExamTypeOrderLimitBoResult boResult,
            UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto,
            ExamTypeDictionaryBO examTypeDictionaryBO) {
        if (CollectionUtil.isNotEmpty(updateExamTypeOrderLimitBto.getExamTypeOrderLimitBtoList())) {
            for (UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto bto :
                    updateExamTypeOrderLimitBto.getExamTypeOrderLimitBtoList()) {
                Optional<ExamTypeOrderLimitBO> any =
                        examTypeDictionaryBO.getExamTypeOrderLimitBOSet().stream()
                                .filter(
                                        examTypeOrderLimitBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                examTypeOrderLimitBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ExamTypeOrderLimitBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToExamTypeOrderLimit());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "campusIdList")) {
                        bo.setCampusIdList(bto.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "useScopeList")) {
                        bo.setUseScopeList(bto.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partCountCalculationDay")) {
                        bo.setPartCountCalculationDay(bto.getPartCountCalculationDay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partMaxCount")) {
                        bo.setPartMaxCount(bto.getPartMaxCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "partValidCount")) {
                        bo.setPartValidCount(bto.getPartValidCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedCount")) {
                        bo.setEnhancedCount(bto.getEnhancedCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enhancedScanningMutexFlag")) {
                        bo.setEnhancedScanningMutexFlag(bto.getEnhancedScanningMutexFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updatedBy")) {
                        bo.setUpdatedBy(bto.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "billingNode")) {
                        bo.setBillingNode(bto.getBillingNode());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:saveExamTypeDocumentTemplate,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateSaveExamTypeDocumentTemplateOnMissThrowEx(
            BaseExamTypeDictionaryBOService.SaveExamTypeDocumentTemplateBoResult boResult,
            SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamTypeDocumentTemplateBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(saveExamTypeDocumentTemplateBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(saveExamTypeDocumentTemplateBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:updateExamTypeChargeItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateUpdateExamTypeChargeItemOnMissThrowEx(
            UpdateExamTypeChargeItemBoResult boResult,
            UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamTypeChargeItemBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(updateExamTypeChargeItemBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(updateExamTypeChargeItemBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:updateExamTypeDictionary,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateUpdateExamTypeDictionaryOnMissThrowEx(
            BaseExamTypeDictionaryBOService.UpdateExamTypeDictionaryBoResult boResult,
            UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamTypeDictionaryBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(updateExamTypeDictionaryBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(updateExamTypeDictionaryBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "sortNumber")) {
                examTypeDictionaryBO.setSortNumber(updateExamTypeDictionaryBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeCode")) {
                examTypeDictionaryBO.setExamTypeCode(updateExamTypeDictionaryBto.getExamTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "examTypeName")) {
                examTypeDictionaryBO.setExamTypeName(updateExamTypeDictionaryBto.getExamTypeName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "inputCode")) {
                examTypeDictionaryBO.setInputCode(updateExamTypeDictionaryBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "standardExamTypeCode")) {
                examTypeDictionaryBO.setStandardExamTypeCode(
                        updateExamTypeDictionaryBto.getStandardExamTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "standardExamTypeName")) {
                examTypeDictionaryBO.setStandardExamTypeName(
                        updateExamTypeDictionaryBto.getStandardExamTypeName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "campusIdList")) {
                examTypeDictionaryBO.setCampusIdList(updateExamTypeDictionaryBto.getCampusIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "useScopeList")) {
                examTypeDictionaryBO.setUseScopeList(updateExamTypeDictionaryBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "billingMode")) {
                examTypeDictionaryBO.setBillingMode(updateExamTypeDictionaryBto.getBillingMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "everyDayMaxPartCount")) {
                examTypeDictionaryBO.setEveryDayMaxPartCount(
                        updateExamTypeDictionaryBto.getEveryDayMaxPartCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "everyVisitMaxPartCount")) {
                examTypeDictionaryBO.setEveryVisitMaxPartCount(
                        updateExamTypeDictionaryBto.getEveryVisitMaxPartCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "updatedBy")) {
                examTypeDictionaryBO.setUpdatedBy(updateExamTypeDictionaryBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "partSplitFlag")) {
                examTypeDictionaryBO.setPartSplitFlag(
                        updateExamTypeDictionaryBto.getPartSplitFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateExamTypeDictionaryBto, "__$validPropertySet"),
                    "parentId")) {
                examTypeDictionaryBO.setParentId(updateExamTypeDictionaryBto.getParentId());
            }
            return examTypeDictionaryBO;
        }
    }

    /** 更新对象:updateExamTypeOrderLimit,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamTypeDictionaryBO updateUpdateExamTypeOrderLimitOnMissThrowEx(
            UpdateExamTypeOrderLimitBoResult boResult,
            UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto) {
        ExamTypeDictionaryBO examTypeDictionaryBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamTypeOrderLimitBto.getId() == null);
        if (!allNull && !found) {
            examTypeDictionaryBO =
                    ExamTypeDictionaryBO.getById(updateExamTypeOrderLimitBto.getId());
            found = true;
        }
        if (examTypeDictionaryBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examTypeDictionaryBO.convertToExamTypeDictionary());
            updatedBto.setBto(updateExamTypeOrderLimitBto);
            updatedBto.setBo(examTypeDictionaryBO);
            boResult.getUpdatedList().add(updatedBto);
            return examTypeDictionaryBO;
        }
    }

    public static class CreateExamTypeDictionaryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeDictionaryBto, ExamTypeDictionaryBO> getCreatedBto(
                CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {
            return this.getAddedResult(createExamTypeDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplateBO>
                getCreatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return this.getAddedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto,
                        ExamTypeMethodDictionaryBO>
                getCreatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto
                                examTypeMethodDictionaryBto) {
            return this.getAddedResult(examTypeMethodDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getCreatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return this.getAddedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto,
                        ExamTypeBodyDictionaryBO>
                getCreatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto
                                examTypeBodyDictionaryBto) {
            return this.getAddedResult(examTypeBodyDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeDictionaryBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getCreatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return this.getAddedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDocumentTemplate getDeleted_ExamTypeDocumentTemplate() {
            return (ExamTypeDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamTypeDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeMethodDictionary getDeleted_ExamTypeMethodDictionary() {
            return (ExamTypeMethodDictionary)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamTypeMethodDictionary.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeOrderLimit getDeleted_ExamTypeOrderLimit() {
            return (ExamTypeOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeBodyDictionary getDeleted_ExamTypeBodyDictionary() {
            return (ExamTypeBodyDictionary)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamTypeBodyDictionary.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeChargeItem getDeleted_ExamTypeChargeItem() {
            return (ExamTypeChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamTypeDictionaryBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {
            return super.getUpdatedResult(createExamTypeDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplate,
                        ExamTypeDocumentTemplateBO>
                getUpdatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return super.getUpdatedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto,
                        ExamTypeMethodDictionary,
                        ExamTypeMethodDictionaryBO>
                getUpdatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto
                                examTypeMethodDictionaryBto) {
            return super.getUpdatedResult(examTypeMethodDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimit,
                        ExamTypeOrderLimitBO>
                getUpdatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUpdatedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto,
                        ExamTypeBodyDictionary,
                        ExamTypeBodyDictionaryBO>
                getUpdatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto
                                examTypeBodyDictionaryBto) {
            return super.getUpdatedResult(examTypeBodyDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeDictionaryBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItem,
                        ExamTypeChargeItemBO>
                getUpdatedBto(
                        CreateExamTypeDictionaryBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUpdatedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamTypeDictionaryBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                CreateExamTypeDictionaryBto createExamTypeDictionaryBto) {
            return super.getUnmodifiedResult(createExamTypeDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplateBO>
                getUnmodifiedBto(
                        CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return super.getUnmodifiedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto,
                        ExamTypeMethodDictionaryBO>
                getUnmodifiedBto(
                        CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto
                                examTypeMethodDictionaryBto) {
            return super.getUnmodifiedResult(examTypeMethodDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getUnmodifiedBto(
                        CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUnmodifiedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto,
                        ExamTypeBodyDictionaryBO>
                getUnmodifiedBto(
                        CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto
                                examTypeBodyDictionaryBto) {
            return super.getUnmodifiedResult(examTypeBodyDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeDictionaryBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getUnmodifiedBto(
                        CreateExamTypeDictionaryBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUnmodifiedResult(examTypeChargeItemBto);
        }
    }

    public static class SaveExamTypeDocumentTemplateBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplateBO>
                getCreatedBto(
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return this.getAddedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamTypeDocumentTemplateBto, ExamTypeDictionaryBO> getCreatedBto(
                SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto) {
            return this.getAddedResult(saveExamTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeDocumentTemplate getDeleted_ExamTypeDocumentTemplate() {
            return (ExamTypeDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamTypeDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplate,
                        ExamTypeDocumentTemplateBO>
                getUpdatedBto(
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return super.getUpdatedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamTypeDocumentTemplateBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto) {
            return super.getUpdatedResult(saveExamTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto,
                        ExamTypeDocumentTemplateBO>
                getUnmodifiedBto(
                        SaveExamTypeDocumentTemplateBto.ExamTypeDocumentTemplateBto
                                examTypeDocumentTemplateBto) {
            return super.getUnmodifiedResult(examTypeDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamTypeDocumentTemplateBto, ExamTypeDictionaryBO>
                getUnmodifiedBto(SaveExamTypeDocumentTemplateBto saveExamTypeDocumentTemplateBto) {
            return super.getUnmodifiedResult(saveExamTypeDocumentTemplateBto);
        }
    }

    public static class ChangeExamTypeOrderLimitEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimitBO>
                getCreatedBto(
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto
                                examTypeOrderLimitBto) {
            return this.getAddedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamTypeOrderLimitEnableFlagBto, ExamTypeDictionaryBO> getCreatedBto(
                ChangeExamTypeOrderLimitEnableFlagBto changeExamTypeOrderLimitEnableFlagBto) {
            return this.getAddedResult(changeExamTypeOrderLimitEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeOrderLimit getDeleted_ExamTypeOrderLimit() {
            return (ExamTypeOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimit,
                        ExamTypeOrderLimitBO>
                getUpdatedBto(
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto
                                examTypeOrderLimitBto) {
            return super.getUpdatedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamTypeOrderLimitEnableFlagBto,
                        ExamTypeDictionary,
                        ExamTypeDictionaryBO>
                getUpdatedBto(
                        ChangeExamTypeOrderLimitEnableFlagBto
                                changeExamTypeOrderLimitEnableFlagBto) {
            return super.getUpdatedResult(changeExamTypeOrderLimitEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimitBO>
                getUnmodifiedBto(
                        ChangeExamTypeOrderLimitEnableFlagBto.ExamTypeOrderLimitBto
                                examTypeOrderLimitBto) {
            return super.getUnmodifiedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamTypeOrderLimitEnableFlagBto, ExamTypeDictionaryBO>
                getUnmodifiedBto(
                        ChangeExamTypeOrderLimitEnableFlagBto
                                changeExamTypeOrderLimitEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamTypeOrderLimitEnableFlagBto);
        }
    }

    public static class CreateExamTypeOrderLimitBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getCreatedBto(
                        CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return this.getAddedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeOrderLimitBto, ExamTypeDictionaryBO> getCreatedBto(
                CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto) {
            return this.getAddedResult(createExamTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeOrderLimit getDeleted_ExamTypeOrderLimit() {
            return (ExamTypeOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimit,
                        ExamTypeOrderLimitBO>
                getUpdatedBto(
                        CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUpdatedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamTypeOrderLimitBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto) {
            return super.getUpdatedResult(createExamTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getUnmodifiedBto(
                        CreateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUnmodifiedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamTypeOrderLimitBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                CreateExamTypeOrderLimitBto createExamTypeOrderLimitBto) {
            return super.getUnmodifiedResult(createExamTypeOrderLimitBto);
        }
    }

    public static class CreateExamTypeChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeChargeItemBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getCreatedBto(
                        CreateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return this.getAddedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamTypeChargeItemBto, ExamTypeDictionaryBO> getCreatedBto(
                CreateExamTypeChargeItemBto createExamTypeChargeItemBto) {
            return this.getAddedResult(createExamTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeChargeItem getDeleted_ExamTypeChargeItem() {
            return (ExamTypeChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamTypeChargeItemBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItem,
                        ExamTypeChargeItemBO>
                getUpdatedBto(
                        CreateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUpdatedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamTypeChargeItemBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(CreateExamTypeChargeItemBto createExamTypeChargeItemBto) {
            return super.getUpdatedResult(createExamTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamTypeChargeItemBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getUnmodifiedBto(
                        CreateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUnmodifiedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamTypeChargeItemBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                CreateExamTypeChargeItemBto createExamTypeChargeItemBto) {
            return super.getUnmodifiedResult(createExamTypeChargeItemBto);
        }
    }

    public static class ChangeExamTypeChargeItemEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItemBO>
                getCreatedBto(
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto
                                examTypeChargeItemBto) {
            return this.getAddedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamTypeChargeItemEnableFlagBto, ExamTypeDictionaryBO> getCreatedBto(
                ChangeExamTypeChargeItemEnableFlagBto changeExamTypeChargeItemEnableFlagBto) {
            return this.getAddedResult(changeExamTypeChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeChargeItem getDeleted_ExamTypeChargeItem() {
            return (ExamTypeChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItem,
                        ExamTypeChargeItemBO>
                getUpdatedBto(
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto
                                examTypeChargeItemBto) {
            return super.getUpdatedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamTypeChargeItemEnableFlagBto,
                        ExamTypeDictionary,
                        ExamTypeDictionaryBO>
                getUpdatedBto(
                        ChangeExamTypeChargeItemEnableFlagBto
                                changeExamTypeChargeItemEnableFlagBto) {
            return super.getUpdatedResult(changeExamTypeChargeItemEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItemBO>
                getUnmodifiedBto(
                        ChangeExamTypeChargeItemEnableFlagBto.ExamTypeChargeItemBto
                                examTypeChargeItemBto) {
            return super.getUnmodifiedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamTypeChargeItemEnableFlagBto, ExamTypeDictionaryBO>
                getUnmodifiedBto(
                        ChangeExamTypeChargeItemEnableFlagBto
                                changeExamTypeChargeItemEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamTypeChargeItemEnableFlagBto);
        }
    }

    public static class UpdateExamTypeDictionaryBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamTypeDictionaryBto, ExamTypeDictionaryBO> getCreatedBto(
                UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
            return this.getAddedResult(updateExamTypeDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamTypeDictionaryBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
            return super.getUpdatedResult(updateExamTypeDictionaryBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamTypeDictionaryBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                UpdateExamTypeDictionaryBto updateExamTypeDictionaryBto) {
            return super.getUnmodifiedResult(updateExamTypeDictionaryBto);
        }
    }

    public static class ChangeExamTypeDictionaryEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeExamTypeDictionaryEnableFlagBto, ExamTypeDictionaryBO> getCreatedBto(
                ChangeExamTypeDictionaryEnableFlagBto changeExamTypeDictionaryEnableFlagBto) {
            return this.getAddedResult(changeExamTypeDictionaryEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeExamTypeDictionaryEnableFlagBto,
                        ExamTypeDictionary,
                        ExamTypeDictionaryBO>
                getUpdatedBto(
                        ChangeExamTypeDictionaryEnableFlagBto
                                changeExamTypeDictionaryEnableFlagBto) {
            return super.getUpdatedResult(changeExamTypeDictionaryEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeExamTypeDictionaryEnableFlagBto, ExamTypeDictionaryBO>
                getUnmodifiedBto(
                        ChangeExamTypeDictionaryEnableFlagBto
                                changeExamTypeDictionaryEnableFlagBto) {
            return super.getUnmodifiedResult(changeExamTypeDictionaryEnableFlagBto);
        }
    }

    public static class UpdateExamTypeOrderLimitBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getCreatedBto(
                        UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return this.getAddedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamTypeOrderLimitBto, ExamTypeDictionaryBO> getCreatedBto(
                UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto) {
            return this.getAddedResult(updateExamTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeOrderLimit getDeleted_ExamTypeOrderLimit() {
            return (ExamTypeOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimit,
                        ExamTypeOrderLimitBO>
                getUpdatedBto(
                        UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUpdatedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamTypeOrderLimitBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto) {
            return super.getUpdatedResult(updateExamTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getUnmodifiedBto(
                        UpdateExamTypeOrderLimitBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUnmodifiedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamTypeOrderLimitBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                UpdateExamTypeOrderLimitBto updateExamTypeOrderLimitBto) {
            return super.getUnmodifiedResult(updateExamTypeOrderLimitBto);
        }
    }

    public static class UpdateExamTypeChargeItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getCreatedBto(
                        UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return this.getAddedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamTypeChargeItemBto, ExamTypeDictionaryBO> getCreatedBto(
                UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto) {
            return this.getAddedResult(updateExamTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeChargeItem getDeleted_ExamTypeChargeItem() {
            return (ExamTypeChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItem,
                        ExamTypeChargeItemBO>
                getUpdatedBto(
                        UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUpdatedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamTypeChargeItemBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto) {
            return super.getUpdatedResult(updateExamTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getUnmodifiedBto(
                        UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUnmodifiedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamTypeChargeItemBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                UpdateExamTypeChargeItemBto updateExamTypeChargeItemBto) {
            return super.getUnmodifiedResult(updateExamTypeChargeItemBto);
        }
    }

    public static class SaveExamTypeChargeItemListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getCreatedBto(
                        SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return this.getAddedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamTypeChargeItemListBto, ExamTypeDictionaryBO> getCreatedBto(
                SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
            return this.getAddedResult(saveExamTypeChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeChargeItem getDeleted_ExamTypeChargeItem() {
            return (ExamTypeChargeItem)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto,
                        ExamTypeChargeItem,
                        ExamTypeChargeItemBO>
                getUpdatedBto(
                        SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUpdatedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamTypeChargeItemListBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
            return super.getUpdatedResult(saveExamTypeChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto, ExamTypeChargeItemBO>
                getUnmodifiedBto(
                        SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto examTypeChargeItemBto) {
            return super.getUnmodifiedResult(examTypeChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamTypeChargeItemListBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                SaveExamTypeChargeItemListBto saveExamTypeChargeItemListBto) {
            return super.getUnmodifiedResult(saveExamTypeChargeItemListBto);
        }
    }

    public static class SaveExamTypeOrderLimitListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamTypeDictionaryBO getRootBo() {
            return (ExamTypeDictionaryBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getCreatedBto(
                        SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return this.getAddedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamTypeOrderLimitListBto, ExamTypeDictionaryBO> getCreatedBto(
                SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
            return this.getAddedResult(saveExamTypeOrderLimitListBto);
        }

        @AutoGenerated(locked = true)
        public ExamTypeOrderLimit getDeleted_ExamTypeOrderLimit() {
            return (ExamTypeOrderLimit)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeOrderLimit.class));
        }

        @AutoGenerated(locked = true)
        public ExamTypeDictionary getDeleted_ExamTypeDictionary() {
            return (ExamTypeDictionary)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamTypeDictionary.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto,
                        ExamTypeOrderLimit,
                        ExamTypeOrderLimitBO>
                getUpdatedBto(
                        SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUpdatedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamTypeOrderLimitListBto, ExamTypeDictionary, ExamTypeDictionaryBO>
                getUpdatedBto(SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
            return super.getUpdatedResult(saveExamTypeOrderLimitListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto, ExamTypeOrderLimitBO>
                getUnmodifiedBto(
                        SaveExamTypeOrderLimitListBto.ExamTypeOrderLimitBto examTypeOrderLimitBto) {
            return super.getUnmodifiedResult(examTypeOrderLimitBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamTypeOrderLimitListBto, ExamTypeDictionaryBO> getUnmodifiedBto(
                SaveExamTypeOrderLimitListBto saveExamTypeOrderLimitListBto) {
            return super.getUnmodifiedResult(saveExamTypeOrderLimitListBto);
        }
    }
}
