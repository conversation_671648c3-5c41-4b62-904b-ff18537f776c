package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ClinicItemDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "b2fb92a6-3f51-41ad-a5c0-db63cc991830|BTO|DEFINITION")
public class CreateClinicItemChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "9d7e5995-918a-45b6-bbe4-ac0a6d8b062a")
    private List<CreateClinicItemChargeItemBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3751a9bb-fa4d-4c3e-a195-4ff6272da97f")
    private String clinicItemId;

    @AutoGenerated(locked = true)
    public void setClinicItemChargeItemBtoList(
            List<CreateClinicItemChargeItemBto.ClinicItemChargeItemBto>
                    clinicItemChargeItemBtoList) {
        this.__$validPropertySet.add("clinicItemChargeItemBtoList");
        this.clinicItemChargeItemBtoList = clinicItemChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemId(String clinicItemId) {
        this.__$validPropertySet.add("clinicItemId");
        this.clinicItemId = clinicItemId;
    }

    /**
     * <b>[源自]</b> ClinicItemChargeItem
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemChargeItemBto {
        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "e6b7f83d-1485-456a-9c3b-cdee800a2e11")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "c4aafa79-8377-4e10-b932-279c0fe06c63")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "2349b6a2-f9d6-4a19-8df7-bbe67c111161")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "75d305ab-4573-4e5b-8f21-672b5d6f1dfb")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "ea304142-319d-4797-aaec-c75c8feb9e35")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "95b5f084-5b6e-42c1-8181-6520ce922d4e")
        private Boolean digitalImagingFeeFlag;

        /** 首次计费标志 */
        @AutoGenerated(locked = true, uuid = "8d52bf6d-d4df-4050-8ff7-4ff2389d6861")
        private Boolean firstTimeBillingFlag;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "035b1f62-b689-4fd1-89f1-1b1c19dbdbab")
        private String performDepartmentId;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "d6625a5f-9c4e-4f4c-ad0d-6256085c261b")
        private List<String> useScopeList;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "d946d450-3914-41da-988f-2e5bbf662912")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "19af7b8e-ea73-4d07-b044-9738a65e22ec")
        private String createdBy;

        /** 诊疗项目id */
        @AutoGenerated(locked = true, uuid = "20aba126-6282-4c1e-aa29-a2cb10a53b9b")
        private String clinicItemId;

        /** 允许修改计数标志 */
        @AutoGenerated(locked = true, uuid = "78f959ed-c1e2-420b-827a-daad23ceb5ec")
        private Boolean allowModifyCountFlag;

        /** 诊疗项目计费类型 */
        @AutoGenerated(locked = true, uuid = "94df285c-0252-417f-8fee-9eda45cd1a53")
        private String clinicItemBillingType;

        /** 诊疗医保代码 */
        @AutoGenerated(locked = true, uuid = "9c725349-c1da-4060-96db-497eeb3aea84")
        private String clinicInsuranceCode;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setFirstTimeBillingFlag(Boolean firstTimeBillingFlag) {
            this.__$validPropertySet.add("firstTimeBillingFlag");
            this.firstTimeBillingFlag = firstTimeBillingFlag;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemId(String clinicItemId) {
            this.__$validPropertySet.add("clinicItemId");
            this.clinicItemId = clinicItemId;
        }

        @AutoGenerated(locked = true)
        public void setAllowModifyCountFlag(Boolean allowModifyCountFlag) {
            this.__$validPropertySet.add("allowModifyCountFlag");
            this.allowModifyCountFlag = allowModifyCountFlag;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemBillingType(String clinicItemBillingType) {
            this.__$validPropertySet.add("clinicItemBillingType");
            this.clinicItemBillingType = clinicItemBillingType;
        }

        @AutoGenerated(locked = true)
        public void setClinicInsuranceCode(String clinicInsuranceCode) {
            this.__$validPropertySet.add("clinicInsuranceCode");
            this.clinicInsuranceCode = clinicInsuranceCode;
        }
    }
}
