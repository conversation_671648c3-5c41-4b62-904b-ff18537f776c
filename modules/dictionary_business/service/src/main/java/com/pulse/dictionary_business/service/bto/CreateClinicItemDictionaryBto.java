package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ClinicItemDictionary
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "ecbec004-5a2f-428f-955e-4f992c9efb08|BTO|DEFINITION")
public class CreateClinicItemDictionaryBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 最大年龄限制 */
    @AutoGenerated(locked = true, uuid = "7c63d9e8-5e5d-4ec5-8b54-9a17c64e6e12")
    private Long ageMaxLimit;

    /** 最小年龄限制 */
    @AutoGenerated(locked = true, uuid = "50c7eb06-2aa3-437e-98ee-2849c80c82a9")
    private Long ageMinLimit;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "7897f8d6-bc3c-4a32-8655-b199ebc199d4")
    private String alias;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "5315d281-7ce0-46ba-86a2-82d4ff0e1ac4")
    private Date auditDate;

    /** 审核标记 */
    @AutoGenerated(locked = true, uuid = "93dce41f-d830-4a15-8de4-fbae76e4ea61")
    private Boolean auditFlag;

    /** 审核操作员 */
    @AutoGenerated(locked = true, uuid = "540d3a89-b64e-42d4-83c3-164c2fef22bc")
    private String auditOperatorId;

    /** 计价标识 */
    @AutoGenerated(locked = true, uuid = "96af3669-8469-4d1b-8a0a-106bb40915fc")
    private String billingAttribute;

    /** 计费间隔 时间间隔分钟 */
    @AutoGenerated(locked = true, uuid = "80d67415-3c2e-443e-8d0e-37f93796966b")
    private Long billingInterval;

    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8b78f944-7689-4437-aa0b-23a87abfd435")
    private List<String> campusIdList;

    /** 卡片打印类型 字典：卡片打印类型 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d2b1469f-0022-46bf-8e81-666e8fcee357")
    private List<String> cardPrintTypeList;

    /** 诊疗项目目录 */
    @AutoGenerated(locked = true, uuid = "baf8785f-3700-4a62-90cd-99dbaec38727")
    private String clinicItemCatalogId;

    @Valid
    @AutoGenerated(locked = true, uuid = "1c4e9688-0e57-4c34-8a4e-68ee235104bb")
    private List<CreateClinicItemDictionaryBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "1b576c91-1c08-4eab-9fe2-0be3ef58ae01")
    private List<CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto>
            clinicItemGuideDescriptionBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a16a68f7-c18d-4efc-bb41-2c06dbad7950")
    private String clinicItemId;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "f4bddf42-b38f-4648-8af7-07a7549e5ed8")
    private String clinicItemName;

    @Valid
    @AutoGenerated(locked = true, uuid = "204e2df2-edac-4799-807e-e5dd6d2dea35")
    private List<CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto>
            clinicItemPerformDepartmentBtoList;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "5a514508-19e7-432f-a1f7-71ec2aa5cfa9")
    private String createdBy;

    /** 延迟天数 */
    @AutoGenerated(locked = true, uuid = "809696b5-3120-4848-a696-00debd2194e0")
    private Long delayDays;

    /** 项目说明 */
    @AutoGenerated(locked = true, uuid = "9bcdb5d3-92af-420d-ac92-6fd6993019b4")
    private String description;

    /** 双签名标志 */
    @AutoGenerated(locked = true, uuid = "ddef5f8b-c521-42a7-b6f9-8150d8c078d8")
    private Boolean doubleSignatureFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "f33d5078-6f9c-40a1-82eb-d0ba9f1ff172")
    private Boolean enableFlag;

    /** 排斥时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1a8ffa98-8cfb-4557-a0aa-eff9f53fc709")
    private TimeEo exclusionTime;

    /** 排斥类型 */
    @AutoGenerated(locked = true, uuid = "de7704d7-1442-4e7e-b227-678d4dd51314")
    private String exclusionType;

    /** 频次 */
    @AutoGenerated(locked = true, uuid = "9138884f-6a49-40b7-a950-9d64f77bce87")
    private String frequency;

    /** 默认频率不允许修改标志 */
    @AutoGenerated(locked = true, uuid = "68d7c49b-d53a-4a9f-828a-13717dc33bbf")
    private Boolean frequencyNotAllowedModifyFlag;

    /** 执行科室是否默认包含本科室 */
    @AutoGenerated(locked = true, uuid = "b1339c33-7526-4dc9-9e98-871303c738bc")
    private Boolean includeCurrentDepartmentFlag;

    /** 住院默认执行科室类型 */
    @AutoGenerated(locked = true, uuid = "df64d3de-6399-4c27-81ca-420b2a11a97b")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c83aa38e-e583-4635-9ab8-3c4b74ff379d")
    private InputCodeEo inputCode;

    /** 机构代码 */
    @AutoGenerated(locked = true, uuid = "95047bb3-daf8-42e0-a650-2b847e953918")
    private String institutionId;

    /** 项目规格 */
    @AutoGenerated(locked = true, uuid = "95c11059-eb45-4bd9-b14e-38a958cdc05b")
    private String itemSpecification;

    /** 医嘱项目类型 */
    @AutoGenerated(locked = true, uuid = "3e1f60c7-47e4-46d3-b517-864d17524825")
    private String itemType;

    /** 限制性别 */
    @AutoGenerated(locked = true, uuid = "8b04b9a2-8359-4814-b3c3-1db355855388")
    private String limitGender;

    /** 限制病房ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bd1eca8a-a6e1-408e-8eec-515929ec2e2d")
    private List<String> limitWardIdList;

    /** 是否只允许选择配置的执行科室 */
    @AutoGenerated(locked = true, uuid = "cb6004b8-41df-4ffd-8fe7-8745d6dc27c0")
    private Boolean onlySelectSettingDepartmentFlag;

    /** 对应手术icd9编码 */
    @AutoGenerated(locked = true, uuid = "32f506cb-43fd-46a8-957f-314cff373eb8")
    private String operationCode;

    /** 医嘱频次计费类型 */
    @AutoGenerated(locked = true, uuid = "411d6bed-34db-42aa-8a82-da84d0ab966f")
    private String orderFrequencyBillingType;

    /** 门诊默认执行科室ID */
    @AutoGenerated(locked = true, uuid = "b7ea9948-a736-4577-a85e-f4ac2ebd7b38")
    private String outpDefaultPerformDepartmentId;

    /** PDA执行标志 */
    @AutoGenerated(locked = true, uuid = "de1d6500-bf12-46dd-8c86-b4981dfbd038")
    private Boolean pdaPerformFlag;

    /** 打印标志 */
    @AutoGenerated(locked = true, uuid = "3736e4bf-c8bd-42ca-9593-ccbc20cc0249")
    private Boolean printFlag;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "ea161ed1-a844-404c-be67-209a4ea12830")
    private String remark;

    /** 医保抢救标志 */
    @AutoGenerated(locked = true, uuid = "5318c1a6-0245-475e-85e8-37d87ed113bf")
    private Boolean rescueFlag;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "2c143d83-b0ee-4489-af3c-819ac6fbab23")
    private Long sortNumber;

    /** 是否特需 */
    @AutoGenerated(locked = true, uuid = "7233126d-7968-4d3e-807b-eeae0f9e189c")
    private Boolean specialNeedFlag;

    /** 标准代码 */
    @AutoGenerated(locked = true, uuid = "88552117-d199-4279-83b4-d0a2e5152cff")
    private String standardCode;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "abb60e3d-ba62-4d59-ba02-0e0554b42e17")
    private String unit;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "1f03f06a-43ee-44c9-a284-68de0c548ed7")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "943b381a-178a-4782-a30f-79289954d695")
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public void setAgeMaxLimit(Long ageMaxLimit) {
        this.__$validPropertySet.add("ageMaxLimit");
        this.ageMaxLimit = ageMaxLimit;
    }

    @AutoGenerated(locked = true)
    public void setAgeMinLimit(Long ageMinLimit) {
        this.__$validPropertySet.add("ageMinLimit");
        this.ageMinLimit = ageMinLimit;
    }

    @AutoGenerated(locked = true)
    public void setAlias(String alias) {
        this.__$validPropertySet.add("alias");
        this.alias = alias;
    }

    @AutoGenerated(locked = true)
    public void setAuditDate(Date auditDate) {
        this.__$validPropertySet.add("auditDate");
        this.auditDate = auditDate;
    }

    @AutoGenerated(locked = true)
    public void setAuditFlag(Boolean auditFlag) {
        this.__$validPropertySet.add("auditFlag");
        this.auditFlag = auditFlag;
    }

    @AutoGenerated(locked = true)
    public void setAuditOperatorId(String auditOperatorId) {
        this.__$validPropertySet.add("auditOperatorId");
        this.auditOperatorId = auditOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setBillingAttribute(String billingAttribute) {
        this.__$validPropertySet.add("billingAttribute");
        this.billingAttribute = billingAttribute;
    }

    @AutoGenerated(locked = true)
    public void setBillingInterval(Long billingInterval) {
        this.__$validPropertySet.add("billingInterval");
        this.billingInterval = billingInterval;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setCardPrintType(List<String> cardPrintType) {
        this.__$validPropertySet.add("cardPrintTypeList");
        this.cardPrintTypeList = cardPrintType;
    }

    @AutoGenerated(locked = true)
    public void setCardPrintTypeList(List<String> cardPrintTypeList) {
        this.__$validPropertySet.add("cardPrintTypeList");
        this.cardPrintTypeList = cardPrintTypeList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemCatalogId(String clinicItemCatalogId) {
        this.__$validPropertySet.add("clinicItemCatalogId");
        this.clinicItemCatalogId = clinicItemCatalogId;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemChargeItemBtoList(
            List<CreateClinicItemDictionaryBto.ClinicItemChargeItemBto>
                    clinicItemChargeItemBtoList) {
        this.__$validPropertySet.add("clinicItemChargeItemBtoList");
        this.clinicItemChargeItemBtoList = clinicItemChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemGuideDescriptionBtoList(
            List<CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto>
                    clinicItemGuideDescriptionBtoList) {
        this.__$validPropertySet.add("clinicItemGuideDescriptionBtoList");
        this.clinicItemGuideDescriptionBtoList = clinicItemGuideDescriptionBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemId(String clinicItemId) {
        this.__$validPropertySet.add("clinicItemId");
        this.clinicItemId = clinicItemId;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemName(String clinicItemName) {
        this.__$validPropertySet.add("clinicItemName");
        this.clinicItemName = clinicItemName;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemPerformDepartmentBtoList(
            List<CreateClinicItemDictionaryBto.ClinicItemPerformDepartmentBto>
                    clinicItemPerformDepartmentBtoList) {
        this.__$validPropertySet.add("clinicItemPerformDepartmentBtoList");
        this.clinicItemPerformDepartmentBtoList = clinicItemPerformDepartmentBtoList;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDelayDays(Long delayDays) {
        this.__$validPropertySet.add("delayDays");
        this.delayDays = delayDays;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setDoubleSignatureFlag(Boolean doubleSignatureFlag) {
        this.__$validPropertySet.add("doubleSignatureFlag");
        this.doubleSignatureFlag = doubleSignatureFlag;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setExclusionTime(TimeEo exclusionTime) {
        this.__$validPropertySet.add("exclusionTime");
        this.exclusionTime = exclusionTime;
    }

    @AutoGenerated(locked = true)
    public void setExclusionType(String exclusionType) {
        this.__$validPropertySet.add("exclusionType");
        this.exclusionType = exclusionType;
    }

    @AutoGenerated(locked = true)
    public void setFrequency(String frequency) {
        this.__$validPropertySet.add("frequency");
        this.frequency = frequency;
    }

    @AutoGenerated(locked = true)
    public void setFrequencyNotAllowedModifyFlag(Boolean frequencyNotAllowedModifyFlag) {
        this.__$validPropertySet.add("frequencyNotAllowedModifyFlag");
        this.frequencyNotAllowedModifyFlag = frequencyNotAllowedModifyFlag;
    }

    @AutoGenerated(locked = true)
    public void setIncludeCurrentDepartmentFlag(Boolean includeCurrentDepartmentFlag) {
        this.__$validPropertySet.add("includeCurrentDepartmentFlag");
        this.includeCurrentDepartmentFlag = includeCurrentDepartmentFlag;
    }

    @AutoGenerated(locked = true)
    public void setInpDefaultPerformDepartmentType(
            DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType) {
        this.__$validPropertySet.add("inpDefaultPerformDepartmentType");
        this.inpDefaultPerformDepartmentType = inpDefaultPerformDepartmentType;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setInstitutionId(String institutionId) {
        this.__$validPropertySet.add("institutionId");
        this.institutionId = institutionId;
    }

    @AutoGenerated(locked = true)
    public void setItemSpecification(String itemSpecification) {
        this.__$validPropertySet.add("itemSpecification");
        this.itemSpecification = itemSpecification;
    }

    @AutoGenerated(locked = true)
    public void setItemType(String itemType) {
        this.__$validPropertySet.add("itemType");
        this.itemType = itemType;
    }

    @AutoGenerated(locked = true)
    public void setLimitGender(String limitGender) {
        this.__$validPropertySet.add("limitGender");
        this.limitGender = limitGender;
    }

    @AutoGenerated(locked = true)
    public void setLimitWardIdList(List<String> limitWardIdList) {
        this.__$validPropertySet.add("limitWardIdList");
        this.limitWardIdList = limitWardIdList;
    }

    @AutoGenerated(locked = true)
    public void setOnlySelectSettingDepartmentFlag(Boolean onlySelectSettingDepartmentFlag) {
        this.__$validPropertySet.add("onlySelectSettingDepartmentFlag");
        this.onlySelectSettingDepartmentFlag = onlySelectSettingDepartmentFlag;
    }

    @AutoGenerated(locked = true)
    public void setOperationCode(String operationCode) {
        this.__$validPropertySet.add("operationCode");
        this.operationCode = operationCode;
    }

    @AutoGenerated(locked = true)
    public void setOrderFrequencyBillingType(String orderFrequencyBillingType) {
        this.__$validPropertySet.add("orderFrequencyBillingType");
        this.orderFrequencyBillingType = orderFrequencyBillingType;
    }

    @AutoGenerated(locked = true)
    public void setOutpDefaultPerformDepartmentId(String outpDefaultPerformDepartmentId) {
        this.__$validPropertySet.add("outpDefaultPerformDepartmentId");
        this.outpDefaultPerformDepartmentId = outpDefaultPerformDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setPdaPerformFlag(Boolean pdaPerformFlag) {
        this.__$validPropertySet.add("pdaPerformFlag");
        this.pdaPerformFlag = pdaPerformFlag;
    }

    @AutoGenerated(locked = true)
    public void setPrintFlag(Boolean printFlag) {
        this.__$validPropertySet.add("printFlag");
        this.printFlag = printFlag;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setRescueFlag(Boolean rescueFlag) {
        this.__$validPropertySet.add("rescueFlag");
        this.rescueFlag = rescueFlag;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setSpecialNeedFlag(Boolean specialNeedFlag) {
        this.__$validPropertySet.add("specialNeedFlag");
        this.specialNeedFlag = specialNeedFlag;
    }

    @AutoGenerated(locked = true)
    public void setStandardCode(String standardCode) {
        this.__$validPropertySet.add("standardCode");
        this.standardCode = standardCode;
    }

    @AutoGenerated(locked = true)
    public void setUnit(String unit) {
        this.__$validPropertySet.add("unit");
        this.unit = unit;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    @AutoGenerated(locked = true)
    public void setUseScopeList(List<String> useScopeList) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScopeList;
    }

    /**
     * <b>[源自]</b> ClinicItemChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "e7cee674-86de-4cfe-afa8-1b00044bd66f")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "9b4e96b3-4158-480d-b3b2-5a34acc82edb")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "eef6fcaa-766d-4d92-8607-5f059907db8e")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "b21797dc-2552-4d9b-9c34-eb76df62750f")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "86a98377-251c-40ef-93c7-2c2ced925475")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "6eb0e4da-766b-4c06-94ea-4eb96e53516d")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "0be0a6a3-0170-4cfe-af02-12510612fd50")
        private Boolean digitalImagingFeeFlag;

        /** 首次计费标志 */
        @AutoGenerated(locked = true, uuid = "d06974e2-ac18-4d59-af0c-4b9ac5f4f70d")
        private Boolean firstTimeBillingFlag;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "d8d5541a-8eb7-4179-877d-d8d6b5b51b36")
        private String performDepartmentId;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "b383b16b-5183-4206-8d68-b13751d4be59")
        private List<String> useScopeList;

        /** 允许修改计数标志 */
        @AutoGenerated(locked = true, uuid = "b9ac2b3e-e84a-4944-8b61-ff4ae30cd811")
        private Boolean allowModifyCountFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "ed95bce8-8997-4369-b223-1aadb4d46c85")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "dc02782d-3bb5-4a02-a964-ec03ae319d48")
        private String createdBy;

        /** 诊疗项目计费类型 */
        @AutoGenerated(locked = true, uuid = "e37d5e58-1e46-4afe-9f14-948e2c0e752d")
        private String clinicItemBillingType;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setFirstTimeBillingFlag(Boolean firstTimeBillingFlag) {
            this.__$validPropertySet.add("firstTimeBillingFlag");
            this.firstTimeBillingFlag = firstTimeBillingFlag;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setAllowModifyCountFlag(Boolean allowModifyCountFlag) {
            this.__$validPropertySet.add("allowModifyCountFlag");
            this.allowModifyCountFlag = allowModifyCountFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemBillingType(String clinicItemBillingType) {
            this.__$validPropertySet.add("clinicItemBillingType");
            this.clinicItemBillingType = clinicItemBillingType;
        }
    }

    /**
     * <b>[源自]</b> ClinicItemPerformDepartment
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemPerformDepartmentBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "d65410ba-cc20-4c52-bddf-626bee8812cf")
        private Long id;

        /** 使用组织类型 */
        @AutoGenerated(locked = true, uuid = "6ca306b9-9a61-4bd8-be15-36fb68e092d9")
        private String useOrganizationType;

        /** 组织ID */
        @AutoGenerated(locked = true, uuid = "2f21dfde-fb2f-4bed-93eb-11d988011397")
        private String organizationId;

        /** 开单科室id */
        @AutoGenerated(locked = true, uuid = "cccdd530-0218-4243-873a-5d32cbfb2a7c")
        private String orderDepartmentId;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "d97cb1b7-755f-4d68-9884-85f39ca2280d")
        private String performDepartmentId;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "ccb6415d-e40a-47e3-bc6c-2c65c1a995a7")
        private List<String> useScopeList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(Long id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setUseOrganizationType(String useOrganizationType) {
            this.__$validPropertySet.add("useOrganizationType");
            this.useOrganizationType = useOrganizationType;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setOrderDepartmentId(String orderDepartmentId) {
            this.__$validPropertySet.add("orderDepartmentId");
            this.orderDepartmentId = orderDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }
    }

    /**
     * <b>[源自]</b> ClinicItemGuideDescription
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemGuideDescriptionBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "84df766a-e477-4bab-b80b-4c28dd5f4761")
        private String id;

        /** 项目类型 */
        @AutoGenerated(locked = true, uuid = "c1e78e5f-d674-4eb6-8e71-b1d989b4f85e")
        private String itemType;

        /** 导医使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "47e0bc4d-6064-4ed6-a8e7-ba2f2ec83ca5")
        private List<String> guideUseScopeList;

        /** 使用组织类型 全院、院区、科室 */
        @AutoGenerated(locked = true, uuid = "74a674a2-1c2d-478a-adf5-30ccb9548fad")
        private String organizationType;

        /** 组织ID 可能是科室、可能是院区 */
        @AutoGenerated(locked = true, uuid = "215b4cc7-8959-47ee-8c40-b05d752ea0b7")
        private String organizationId;

        /** 执行科室D */
        @AutoGenerated(locked = true, uuid = "3555119e-d81e-45be-b730-c0e6abe7a581")
        private String performDepartmentId;

        /** 注意事项 */
        @AutoGenerated(locked = true, uuid = "fe1f5ca4-d2f0-4827-b9c7-e5321ad5b894")
        private String notice;

        /** 检查说明 */
        @AutoGenerated(locked = true, uuid = "64166bae-4fb1-4582-b312-075d5aa94e01")
        private String examInstruction;

        /** 导医说明 */
        @AutoGenerated(locked = true, uuid = "8260451e-ddc6-4e74-8122-607aed9792b5")
        private String guideInfo;

        /** 导航地址 */
        @AutoGenerated(locked = true, uuid = "417eb5a4-f1e5-4a96-af64-ac85592da2a0")
        private String navigationAddress;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "99a30253-7534-41ed-9df1-e713b33e742f")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "1bdf4330-a3d3-4c7b-811a-75a79566452d")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "744a1946-a151-4018-842c-6e35de05b98c")
        private String createdBy;

        /** 院区id */
        @AutoGenerated(locked = true, uuid = "bfbdfe49-210c-440a-a07f-c7c7ef65a201")
        private String campusId;

        /** 开单科室id */
        @AutoGenerated(locked = true, uuid = "289e5561-9c72-42c9-a24e-0808a4415930")
        private String orderDepartmentId;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setItemType(String itemType) {
            this.__$validPropertySet.add("itemType");
            this.itemType = itemType;
        }

        @AutoGenerated(locked = true)
        public void setGuideUseScope(List<String> guideUseScope) {
            this.__$validPropertySet.add("guideUseScopeList");
            this.guideUseScopeList = guideUseScope;
        }

        @AutoGenerated(locked = true)
        public void setGuideUseScopeList(List<String> guideUseScopeList) {
            this.__$validPropertySet.add("guideUseScopeList");
            this.guideUseScopeList = guideUseScopeList;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationType(String organizationType) {
            this.__$validPropertySet.add("organizationType");
            this.organizationType = organizationType;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setNotice(String notice) {
            this.__$validPropertySet.add("notice");
            this.notice = notice;
        }

        @AutoGenerated(locked = true)
        public void setExamInstruction(String examInstruction) {
            this.__$validPropertySet.add("examInstruction");
            this.examInstruction = examInstruction;
        }

        @AutoGenerated(locked = true)
        public void setGuideInfo(String guideInfo) {
            this.__$validPropertySet.add("guideInfo");
            this.guideInfo = guideInfo;
        }

        @AutoGenerated(locked = true)
        public void setNavigationAddress(String navigationAddress) {
            this.__$validPropertySet.add("navigationAddress");
            this.navigationAddress = navigationAddress;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setCampusId(String campusId) {
            this.__$validPropertySet.add("campusId");
            this.campusId = campusId;
        }

        @AutoGenerated(locked = true)
        public void setOrderDepartmentId(String orderDepartmentId) {
            this.__$validPropertySet.add("orderDepartmentId");
            this.orderDepartmentId = orderDepartmentId;
        }
    }
}
