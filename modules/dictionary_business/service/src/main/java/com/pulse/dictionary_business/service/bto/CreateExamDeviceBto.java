package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.ExamTypeBillingModeEnum;
import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.pulse.dictionary_business.service.bto.CreateExamDeviceBto.ExamDeviceDocumentTemplateBto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamDevice
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "9729af19-0422-4667-a838-70cfa79946b7|BTO|DEFINITION")
public class CreateExamDeviceBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 计费模式 */
    @AutoGenerated(locked = true, uuid = "8074d9c6-bb1c-46bb-b14d-120ad845e442")
    private ExamTypeBillingModeEnum billingMode;

    /** 院区id */
    @Valid
    @AutoGenerated(locked = true, uuid = "8541f70d-c3f7-4d74-a726-b1e7fd7c6254")
    private List<String> campusIdList;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "5c4a37cd-75a0-47ea-97d8-56d12827125d")
    private String createdBy;

    /** 设备ID */
    @AutoGenerated(locked = true, uuid = "e8793b81-f8c4-4e70-ae70-daf74654e8fa")
    private String deviceId;

    /** 设备名称 */
    @AutoGenerated(locked = true, uuid = "97849749-b902-42a9-837d-1b904dc96f8f")
    private String deviceName;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "3d1680e7-163b-47b0-b970-d0a79c355c4a")
    private Boolean enableFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "2651d1fa-ecfe-4429-9cbd-73e704e9dc3c")
    private List<CreateExamDeviceBto.ExamDeviceChargeItemBto> examDeviceChargeItemBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "485be286-b432-493d-8c7f-5f8d44840bba")
    private ExamDeviceDocumentTemplateBto examDeviceDocumentTemplateBto;

    @Valid
    @AutoGenerated(locked = true, uuid = "33b7fd84-c624-482f-8231-2ad10971423b")
    private List<CreateExamDeviceBto.ExamDeviceOrderLimitBto> examDeviceOrderLimitBtoList;

    /** 检查类型id */
    @AutoGenerated(locked = true, uuid = "1b0c2978-531f-4d0e-a882-3d1bb06fac7b")
    private String examTypeId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4464c7bf-238b-43b2-8233-820c4a6b643d")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d99d4089-2939-4553-8ea5-f901d090b963")
    private InputCodeEo inputCode;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "ab5a0950-77ca-4025-8324-1bc45e316260")
    private Long sortNumber;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "7f8ce35f-bee4-4cf9-aebc-ce65f1e6e8a0")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setBillingMode(ExamTypeBillingModeEnum billingMode) {
        this.__$validPropertySet.add("billingMode");
        this.billingMode = billingMode;
    }

    @AutoGenerated(locked = true)
    public void setCampusId(List<String> campusId) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusId;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDeviceId(String deviceId) {
        this.__$validPropertySet.add("deviceId");
        this.deviceId = deviceId;
    }

    @AutoGenerated(locked = true)
    public void setDeviceName(String deviceName) {
        this.__$validPropertySet.add("deviceName");
        this.deviceName = deviceName;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setExamDeviceChargeItemBtoList(
            List<CreateExamDeviceBto.ExamDeviceChargeItemBto> examDeviceChargeItemBtoList) {
        this.__$validPropertySet.add("examDeviceChargeItemBtoList");
        this.examDeviceChargeItemBtoList = examDeviceChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamDeviceDocumentTemplateBto(
            CreateExamDeviceBto.ExamDeviceDocumentTemplateBto examDeviceDocumentTemplateBto) {
        this.__$validPropertySet.add("examDeviceDocumentTemplateBto");
        this.examDeviceDocumentTemplateBto = examDeviceDocumentTemplateBto;
    }

    @AutoGenerated(locked = true)
    public void setExamDeviceOrderLimitBtoList(
            List<CreateExamDeviceBto.ExamDeviceOrderLimitBto> examDeviceOrderLimitBtoList) {
        this.__$validPropertySet.add("examDeviceOrderLimitBtoList");
        this.examDeviceOrderLimitBtoList = examDeviceOrderLimitBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeId(String examTypeId) {
        this.__$validPropertySet.add("examTypeId");
        this.examTypeId = examTypeId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    /**
     * <b>[源自]</b> ExamDeviceDocumentTemplate
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceDocumentTemplateBto {
        /** 门诊知情同意书 */
        @AutoGenerated(locked = true, uuid = "7c749c63-ea98-4d18-9def-ad5f9de8d5c0")
        private String outpatientInformedConsentForm;

        /** 住院知情同意书 */
        @AutoGenerated(locked = true, uuid = "cb71cbb3-**************-7534d05afe88")
        private String inpatientInformedConsentForm;

        /** 门诊申请单模板 */
        @AutoGenerated(locked = true, uuid = "b00909d0-7f8d-4105-89f8-8c4fa59cf797")
        private String outpatientApplyTemplate;

        /** 住院申请单模板 */
        @AutoGenerated(locked = true, uuid = "ae76e2e6-ffc1-49dc-b9d9-edaca4fa65a3")
        private String inpatientApplyTemplate;

        /** 留观申请单模版 */
        @AutoGenerated(locked = true, uuid = "86b77a59-8c99-4c90-9a67-54435566cd37")
        private String emergencyObservationApplyTemplate;

        /** 院前申请单模板 */
        @AutoGenerated(locked = true, uuid = "bbcc4f97-33b8-456b-a0c9-22de7859bfc3")
        private String preHospitalApplyTemplate;

        /** 体检申请单模板 */
        @AutoGenerated(locked = true, uuid = "97c9b5d7-f0ee-470b-9ba4-d898b31a3f67")
        private String physicalExamApplyTemplate;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "ea59414e-7c89-49fd-99a8-110b8557b8c8")
        private String createdBy;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "072f1aa5-e122-4b8d-ad3c-6f39f162a14a")
        private String updatedBy;

        /** 碘对比标志 */
        @AutoGenerated(locked = true, uuid = "4a04b819-6e50-4568-a570-218fa97a5a94")
        private Boolean iodineContrastFlag;

        /** 钆对比标志 */
        @AutoGenerated(locked = true, uuid = "86fd2193-63d4-4257-88ed-cac2cdd65dfd")
        private Boolean gadoliniumContrastFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setOutpatientInformedConsentForm(String outpatientInformedConsentForm) {
            this.__$validPropertySet.add("outpatientInformedConsentForm");
            this.outpatientInformedConsentForm = outpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setInpatientInformedConsentForm(String inpatientInformedConsentForm) {
            this.__$validPropertySet.add("inpatientInformedConsentForm");
            this.inpatientInformedConsentForm = inpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setOutpatientApplyTemplate(String outpatientApplyTemplate) {
            this.__$validPropertySet.add("outpatientApplyTemplate");
            this.outpatientApplyTemplate = outpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setInpatientApplyTemplate(String inpatientApplyTemplate) {
            this.__$validPropertySet.add("inpatientApplyTemplate");
            this.inpatientApplyTemplate = inpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setEmergencyObservationApplyTemplate(String emergencyObservationApplyTemplate) {
            this.__$validPropertySet.add("emergencyObservationApplyTemplate");
            this.emergencyObservationApplyTemplate = emergencyObservationApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPreHospitalApplyTemplate(String preHospitalApplyTemplate) {
            this.__$validPropertySet.add("preHospitalApplyTemplate");
            this.preHospitalApplyTemplate = preHospitalApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPhysicalExamApplyTemplate(String physicalExamApplyTemplate) {
            this.__$validPropertySet.add("physicalExamApplyTemplate");
            this.physicalExamApplyTemplate = physicalExamApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setIodineContrastFlag(Boolean iodineContrastFlag) {
            this.__$validPropertySet.add("iodineContrastFlag");
            this.iodineContrastFlag = iodineContrastFlag;
        }

        @AutoGenerated(locked = true)
        public void setGadoliniumContrastFlag(Boolean gadoliniumContrastFlag) {
            this.__$validPropertySet.add("gadoliniumContrastFlag");
            this.gadoliniumContrastFlag = gadoliniumContrastFlag;
        }
    }

    /**
     * <b>[源自]</b> ExamDeviceChargeItem
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceChargeItemBto {
        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "d81e71da-c1dc-4909-b322-e293a3c87bf7")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "bf88d182-b155-4fde-946d-c384efeabe97")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "ee891751-3371-4ecc-89eb-4b2b9ea18982")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "3a991402-051d-4a94-a202-ec7f651b4a1a")
        private FilmFeeTypeEnum filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "524afdb8-9f55-429f-9e1f-bb5d53b6d082")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "83d6cfcb-493d-4f6c-b49a-1b2550cf70fb")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "f49d0753-ae4e-4e91-8da0-bc91b810a5f9")
        private List<String> useScopeList;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "c8c79913-0f74-4b76-914a-af8db4d922b2")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "81521919-880e-43bd-9134-8469647f5a24")
        private String createdBy;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "d68ad0a3-ff53-4d01-95b6-2d5e8b7dd6e1")
        private Boolean enableFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(FilmFeeTypeEnum filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }
    }

    /**
     * <b>[源自]</b> ExamDeviceOrderLimit
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceOrderLimitBto {
        /** 院区id */
        @Valid
        @AutoGenerated(locked = true, uuid = "6de56c32-67e6-4d1b-9102-0b22880a73db")
        private List<String> campusIdList;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "05ab4043-66bc-4bb7-bb03-010198d0fd57")
        private List<String> useScopeList;

        /** 部位累加天数 */
        @AutoGenerated(locked = true, uuid = "8bb828ac-104d-41fc-b187-b42d37b1bd58")
        private Long partCountCalculationDay;

        /** 部位最大数量 */
        @AutoGenerated(locked = true, uuid = "f73f57c0-fa85-4eba-919a-a220d07fc2d4")
        private Long partMaxCount;

        /** 有效部位数量 */
        @AutoGenerated(locked = true, uuid = "1506bd17-b3b4-4895-b1a9-eb14ab157f9b")
        private Long partValidCount;

        /** 增强数量 */
        @AutoGenerated(locked = true, uuid = "5b8af278-6d9b-4d18-94e6-c86859cf9aad")
        private Long enhancedCount;

        /** 增强扫描互斥标志 */
        @AutoGenerated(locked = true, uuid = "37e34989-cfe1-4b57-8333-6921a327fe23")
        private Boolean enhancedScanningMutexFlag;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "a6dd5683-cf2d-4dbf-bb9b-1d9a3f826c0c")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "e31f00b3-8e13-42ed-a66a-dddaa5f4879d")
        private Long updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "c0ab65f5-2132-4fc1-a6f6-cf7633ffb30f")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setPartCountCalculationDay(Long partCountCalculationDay) {
            this.__$validPropertySet.add("partCountCalculationDay");
            this.partCountCalculationDay = partCountCalculationDay;
        }

        @AutoGenerated(locked = true)
        public void setPartMaxCount(Long partMaxCount) {
            this.__$validPropertySet.add("partMaxCount");
            this.partMaxCount = partMaxCount;
        }

        @AutoGenerated(locked = true)
        public void setPartValidCount(Long partValidCount) {
            this.__$validPropertySet.add("partValidCount");
            this.partValidCount = partValidCount;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedCount(Long enhancedCount) {
            this.__$validPropertySet.add("enhancedCount");
            this.enhancedCount = enhancedCount;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedScanningMutexFlag(Boolean enhancedScanningMutexFlag) {
            this.__$validPropertySet.add("enhancedScanningMutexFlag");
            this.enhancedScanningMutexFlag = enhancedScanningMutexFlag;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(Long updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }
}
