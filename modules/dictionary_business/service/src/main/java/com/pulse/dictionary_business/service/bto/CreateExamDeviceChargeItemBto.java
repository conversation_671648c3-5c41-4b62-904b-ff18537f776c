package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamDevice
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "be9e7c9c-51a0-4905-842e-9ba464ccf780|BTO|DEFINITION")
public class CreateExamDeviceChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "7c41c853-f7ff-4075-b9db-a4afb113d8f1")
    private List<CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto> examDeviceChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3129ed28-b534-4de0-8fce-2a495c77174b")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamDeviceChargeItemBtoList(
            List<CreateExamDeviceChargeItemBto.ExamDeviceChargeItemBto>
                    examDeviceChargeItemBtoList) {
        this.__$validPropertySet.add("examDeviceChargeItemBtoList");
        this.examDeviceChargeItemBtoList = examDeviceChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamDeviceChargeItem
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceChargeItemBto {
        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "9db3274e-1efb-4612-97c7-a19b586fe4dc")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "504fc668-64de-4e2b-a86c-8be19ae71056")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "ceed1263-2031-4c90-8ebf-b7dcf7356bfe")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "ad27aa10-1685-4011-a98c-900ae8ceb2a3")
        private FilmFeeTypeEnum filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "bc03880e-ae0a-4a8a-9ffd-9e092d70f360")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "8b537413-9052-4d46-9483-ff4f8adeb552")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "05000680-0925-49f0-8991-f22b65c46dbb")
        private List<String> useScopeList;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "990b31e5-fd4b-4ec4-bb30-bad1aab0fe2d")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "04f8c482-8abc-42ad-b0f0-380f570334fa")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "83456062-f170-42e1-b5a6-cd8d687bf5a8")
        private String createdBy;

        /** 检查设备id */
        @AutoGenerated(locked = true, uuid = "552cb26d-79a7-4181-a3d0-ce762bd5dacc")
        private String examDeviceId;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "91359726-2bba-455d-9c6c-b32ec94394c8")
        private Boolean enhancedFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(FilmFeeTypeEnum filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setExamDeviceId(String examDeviceId) {
            this.__$validPropertySet.add("examDeviceId");
            this.examDeviceId = examDeviceId;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }
    }
}
