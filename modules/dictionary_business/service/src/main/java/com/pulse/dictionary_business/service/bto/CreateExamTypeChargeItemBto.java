package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamTypeDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "6deb4a36-eb7b-43be-a785-0ac4fc195d7b|BTO|DEFINITION")
public class CreateExamTypeChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "4d0d6dea-2de5-405c-9056-48d64906f510")
    private List<CreateExamTypeChargeItemBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "fbedf3ae-a464-4cd0-9ef0-14b80b6b83f1")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamTypeChargeItemBtoList(
            List<CreateExamTypeChargeItemBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList) {
        this.__$validPropertySet.add("examTypeChargeItemBtoList");
        this.examTypeChargeItemBtoList = examTypeChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamTypeChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "c466e85d-317e-4d72-b144-c540ab33f516")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "895f395e-f5cd-4d39-8596-6d4c66de2494")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "534869bd-6d0f-4c0e-b2d6-6156c343c8cc")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "e1184204-da70-4721-aa23-76459c44b6bb")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "c6410e0b-fcde-4acb-9449-8ba69e759b8a")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "e6d6d839-80ea-4097-ae7c-a1b247fb6bf6")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "db23c521-44c8-4b9c-88b2-c771e6447d1a")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "52f2f4b9-1d38-4e77-9af8-82014847d1d8")
        private List<String> useScopeList;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "7ce5683f-ddd9-4aa7-8b7b-6b6255f37ec5")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "b1502498-6635-4aa9-be0d-fa6350835522")
        private String createdBy;

        /** 检查类型id */
        @AutoGenerated(locked = true, uuid = "ef5cc7ed-c039-4012-86e7-19915950e3cb")
        private String examTypeId;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "e83ccd5f-4c46-4afd-8616-4f62db74b884")
        private Boolean enableFlag;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "a4a353e0-b22b-45ec-ab6c-2090b36a31a2")
        private Boolean enhancedFlag;

        /** 收费部位数量 */
        @AutoGenerated(locked = true, uuid = "a08478fc-5de9-4197-a2e6-02bf416b9483")
        private Long chargePartNumber;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setExamTypeId(String examTypeId) {
            this.__$validPropertySet.add("examTypeId");
            this.examTypeId = examTypeId;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }

        @AutoGenerated(locked = true)
        public void setChargePartNumber(Long chargePartNumber) {
            this.__$validPropertySet.add("chargePartNumber");
            this.chargePartNumber = chargePartNumber;
        }
    }
}
