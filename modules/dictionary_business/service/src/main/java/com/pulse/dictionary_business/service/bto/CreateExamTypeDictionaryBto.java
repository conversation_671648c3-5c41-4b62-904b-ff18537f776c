package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.service.bto.CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamTypeDictionary
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "dd7d1ff5-89b1-45a9-8406-7435addcd49e|BTO|DEFINITION")
public class CreateExamTypeDictionaryBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 计费模式 按检查项目计费 按检查类型计费 按检查类型+设备类型计费 */
    @AutoGenerated(locked = true, uuid = "61924a44-9bab-4a7b-b4fc-1fd4ebd54f8f")
    private String billingMode;

    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "98b94f0b-e3bc-4cfc-ae71-4be0f3193c04")
    private List<String> campusIdList;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "9700ebb5-86aa-4917-a176-adef14bc7549")
    private String createdBy;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "8cf77632-ff58-4594-8252-e28373c361dc")
    private Boolean enableFlag;

    /** 当日申请最大部位数 */
    @AutoGenerated(locked = true, uuid = "8fedf6ad-f08b-4879-a57f-2c63f3e54f50")
    private Long everyDayMaxPartCount;

    /** 当日一次就诊申请最大部位数 */
    @AutoGenerated(locked = true, uuid = "06f3aa22-80fe-4db8-98ec-56a57d814523")
    private Long everyVisitMaxPartCount;

    @Valid
    @AutoGenerated(locked = true, uuid = "08101357-7908-43a9-b4e0-09d51c196e8e")
    private List<CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto>
            examTypeBodyDictionaryBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "276464f9-e140-4d38-bfab-2da161eaa3a5")
    private List<CreateExamTypeDictionaryBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList;

    /** 检查类型编码 */
    @AutoGenerated(locked = true, uuid = "2f9da02a-d517-4d49-a5a0-282bd8da563b")
    private String examTypeCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "0800a955-0b3a-4f51-995c-7e0a0684c599")
    private ExamTypeDocumentTemplateBto examTypeDocumentTemplateBto;

    @Valid
    @AutoGenerated(locked = true, uuid = "d86e0289-81f2-4967-a850-459a436a9f7e")
    private List<CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto>
            examTypeMethodDictionaryBtoList;

    /** 检查类型名称 */
    @AutoGenerated(locked = true, uuid = "f17908b2-0994-439a-acfb-81ea3ab83679")
    private String examTypeName;

    @Valid
    @AutoGenerated(locked = true, uuid = "432e39b7-8e1c-4bff-9d12-30604089e048")
    private List<CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto> examTypeOrderLimitBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "331aa54b-c5d5-435a-93d8-6794158bb706")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "72926e29-612b-42ef-b7b0-0d5b9f312f07")
    private InputCodeEo inputCode;

    /** 父ID */
    @AutoGenerated(locked = true, uuid = "599491b6-3a01-446d-8d29-d92098a96b41")
    private String parentId;

    /** 部位分割标志 */
    @AutoGenerated(locked = true, uuid = "62933f5f-1147-4445-8246-5db7578bc565")
    private Boolean partSplitFlag;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "9aaf3ae5-66fa-4dd0-97fa-2bdc3c9a4a17")
    private Long sortNumber;

    /** 标准检查分类编码 */
    @AutoGenerated(locked = true, uuid = "5aa6a32d-3c40-4a96-aa31-d1d947294af3")
    private String standardExamTypeCode;

    /** 标准检查分类名称 */
    @AutoGenerated(locked = true, uuid = "717156ff-86f6-4bfd-828e-7f48a871638c")
    private String standardExamTypeName;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "1cce380a-79d6-4be4-b6d6-0981b9e968eb")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "*************-4128-ae17-488a37eded0e")
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public void setBillingMode(String billingMode) {
        this.__$validPropertySet.add("billingMode");
        this.billingMode = billingMode;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setEveryDayMaxPartCount(Long everyDayMaxPartCount) {
        this.__$validPropertySet.add("everyDayMaxPartCount");
        this.everyDayMaxPartCount = everyDayMaxPartCount;
    }

    @AutoGenerated(locked = true)
    public void setEveryVisitMaxPartCount(Long everyVisitMaxPartCount) {
        this.__$validPropertySet.add("everyVisitMaxPartCount");
        this.everyVisitMaxPartCount = everyVisitMaxPartCount;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeBodyDictionaryBtoList(
            List<CreateExamTypeDictionaryBto.ExamTypeBodyDictionaryBto>
                    examTypeBodyDictionaryBtoList) {
        this.__$validPropertySet.add("examTypeBodyDictionaryBtoList");
        this.examTypeBodyDictionaryBtoList = examTypeBodyDictionaryBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeChargeItemBtoList(
            List<CreateExamTypeDictionaryBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList) {
        this.__$validPropertySet.add("examTypeChargeItemBtoList");
        this.examTypeChargeItemBtoList = examTypeChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeCode(String examTypeCode) {
        this.__$validPropertySet.add("examTypeCode");
        this.examTypeCode = examTypeCode;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeDocumentTemplateBto(
            CreateExamTypeDictionaryBto.ExamTypeDocumentTemplateBto examTypeDocumentTemplateBto) {
        this.__$validPropertySet.add("examTypeDocumentTemplateBto");
        this.examTypeDocumentTemplateBto = examTypeDocumentTemplateBto;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeMethodDictionaryBtoList(
            List<CreateExamTypeDictionaryBto.ExamTypeMethodDictionaryBto>
                    examTypeMethodDictionaryBtoList) {
        this.__$validPropertySet.add("examTypeMethodDictionaryBtoList");
        this.examTypeMethodDictionaryBtoList = examTypeMethodDictionaryBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeName(String examTypeName) {
        this.__$validPropertySet.add("examTypeName");
        this.examTypeName = examTypeName;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeOrderLimitBtoList(
            List<CreateExamTypeDictionaryBto.ExamTypeOrderLimitBto> examTypeOrderLimitBtoList) {
        this.__$validPropertySet.add("examTypeOrderLimitBtoList");
        this.examTypeOrderLimitBtoList = examTypeOrderLimitBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setParentId(String parentId) {
        this.__$validPropertySet.add("parentId");
        this.parentId = parentId;
    }

    @AutoGenerated(locked = true)
    public void setPartSplitFlag(Boolean partSplitFlag) {
        this.__$validPropertySet.add("partSplitFlag");
        this.partSplitFlag = partSplitFlag;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setStandardExamTypeCode(String standardExamTypeCode) {
        this.__$validPropertySet.add("standardExamTypeCode");
        this.standardExamTypeCode = standardExamTypeCode;
    }

    @AutoGenerated(locked = true)
    public void setStandardExamTypeName(String standardExamTypeName) {
        this.__$validPropertySet.add("standardExamTypeName");
        this.standardExamTypeName = standardExamTypeName;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    @AutoGenerated(locked = true)
    public void setUseScope(List<String> useScope) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScope;
    }

    @AutoGenerated(locked = true)
    public void setUseScopeList(List<String> useScopeList) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScopeList;
    }

    /**
     * <b>[源自]</b> ExamTypeDocumentTemplate
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeDocumentTemplateBto {
        /** 门诊知情同意书 */
        @AutoGenerated(locked = true, uuid = "9b5de7e1-8abc-494b-ae0b-51f831f59324")
        private String outpatientInformedConsentForm;

        /** 住院知情同意书 */
        @AutoGenerated(locked = true, uuid = "cc5d5c8f-bd69-40e2-8195-8ae0825980a1")
        private String inpatientInformedConsentForm;

        /** 门诊申请单模板 */
        @AutoGenerated(locked = true, uuid = "7ecb1d2e-a991-43fd-ab66-ff6739a19bba")
        private String outpatientApplyTemplate;

        /** 住院申请单模板 */
        @AutoGenerated(locked = true, uuid = "d0100567-5a17-41ea-8305-a02e57763fb0")
        private String inpatientApplyTemplate;

        /** 留观申请单模版 */
        @AutoGenerated(locked = true, uuid = "b8b52609-09f4-4548-9782-ab03628e2c44")
        private String emergencyObservationApplyTemplate;

        /** 院前申请单模板 */
        @AutoGenerated(locked = true, uuid = "3202cb82-3a01-4955-a2d2-6a599b876a82")
        private String preHospitalApplyTemplate;

        /** 体检申请单模板 */
        @AutoGenerated(locked = true, uuid = "8a20dd8a-221b-44d0-b08c-576cd79542c2")
        private String physicalExamApplyTemplate;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "eb368c07-e55e-4619-b48a-528482b0d408")
        private String createdBy;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "55d43c21-0fe0-4365-9a99-38df4957d2db")
        private String updatedBy;

        /** 碘对比标志 */
        @AutoGenerated(locked = true, uuid = "0075ec29-f76c-43ce-8b7c-6ba0363c0441")
        private Boolean iodineContrastFlag;

        /** 钆对比标志 */
        @AutoGenerated(locked = true, uuid = "1c9bed32-e411-4f63-bc03-983562aa9c43")
        private Boolean gadoliniumContrastFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setOutpatientInformedConsentForm(String outpatientInformedConsentForm) {
            this.__$validPropertySet.add("outpatientInformedConsentForm");
            this.outpatientInformedConsentForm = outpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setInpatientInformedConsentForm(String inpatientInformedConsentForm) {
            this.__$validPropertySet.add("inpatientInformedConsentForm");
            this.inpatientInformedConsentForm = inpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setOutpatientApplyTemplate(String outpatientApplyTemplate) {
            this.__$validPropertySet.add("outpatientApplyTemplate");
            this.outpatientApplyTemplate = outpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setInpatientApplyTemplate(String inpatientApplyTemplate) {
            this.__$validPropertySet.add("inpatientApplyTemplate");
            this.inpatientApplyTemplate = inpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setEmergencyObservationApplyTemplate(String emergencyObservationApplyTemplate) {
            this.__$validPropertySet.add("emergencyObservationApplyTemplate");
            this.emergencyObservationApplyTemplate = emergencyObservationApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPreHospitalApplyTemplate(String preHospitalApplyTemplate) {
            this.__$validPropertySet.add("preHospitalApplyTemplate");
            this.preHospitalApplyTemplate = preHospitalApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPhysicalExamApplyTemplate(String physicalExamApplyTemplate) {
            this.__$validPropertySet.add("physicalExamApplyTemplate");
            this.physicalExamApplyTemplate = physicalExamApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setIodineContrastFlag(Boolean iodineContrastFlag) {
            this.__$validPropertySet.add("iodineContrastFlag");
            this.iodineContrastFlag = iodineContrastFlag;
        }

        @AutoGenerated(locked = true)
        public void setGadoliniumContrastFlag(Boolean gadoliniumContrastFlag) {
            this.__$validPropertySet.add("gadoliniumContrastFlag");
            this.gadoliniumContrastFlag = gadoliniumContrastFlag;
        }
    }

    /**
     * <b>[源自]</b> ExamTypeMethodDictionary
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeMethodDictionaryBto {
        /** 排序编号 */
        @AutoGenerated(locked = true, uuid = "43c7b0db-5cc4-42b8-a71d-ff32e3ca119a")
        private Long sortNumber;

        /** 检查方法代码 */
        @AutoGenerated(locked = true, uuid = "b38b6083-4f34-4018-be1c-50e14b0754d0")
        private String examMethodCode;

        /** 检查方法名称 */
        @AutoGenerated(locked = true, uuid = "f630e467-80e1-4d96-99ad-642e3a75c797")
        private String examMethodName;

        /** 输入代码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "f258d883-6dcd-4f53-8934-05b224515258")
        private InputCodeEo inputCode;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setSortNumber(Long sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setExamMethodCode(String examMethodCode) {
            this.__$validPropertySet.add("examMethodCode");
            this.examMethodCode = examMethodCode;
        }

        @AutoGenerated(locked = true)
        public void setExamMethodName(String examMethodName) {
            this.__$validPropertySet.add("examMethodName");
            this.examMethodName = examMethodName;
        }

        @AutoGenerated(locked = true)
        public void setInputCode(InputCodeEo inputCode) {
            this.__$validPropertySet.add("inputCode");
            this.inputCode = inputCode;
        }
    }

    /**
     * <b>[源自]</b> ExamTypeOrderLimit
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeOrderLimitBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "5f98fa47-f34e-4f6c-94d3-c15e88c302b3")
        private String id;

        /** 院区id */
        @Valid
        @AutoGenerated(locked = true, uuid = "4b70c4e2-836e-437e-924e-993a0c18bea8")
        private List<String> campusIdList;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "9bcca2b9-78b7-487d-88af-4d6b57ce6734")
        private List<String> useScopeList;

        /** 部位累加天数 */
        @AutoGenerated(locked = true, uuid = "84c160bc-d9c0-4865-80e8-273ddcc8d3ed")
        private Long partCountCalculationDay;

        /** 部位最大数量 */
        @AutoGenerated(locked = true, uuid = "ec3b868b-cd92-48d2-9a19-23cbd02e647e")
        private Long partMaxCount;

        /** 有效部位数量 */
        @AutoGenerated(locked = true, uuid = "03fa5b6e-c3bd-4e8d-947e-9f22f7bfdb13")
        private Long partValidCount;

        /** 增强数量 */
        @AutoGenerated(locked = true, uuid = "2cc8b5a3-a6d8-4efb-8d31-a908eba08c6b")
        private Long enhancedCount;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "bec8a345-980e-432b-9abb-899c3a0844b6")
        private Boolean enableFlag;

        /** 增强扫描互斥标志 */
        @AutoGenerated(locked = true, uuid = "504b70ec-0cd6-4564-a4b1-7b7eeb27a5af")
        private Boolean enhancedScanningMutexFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "dd93b1c8-ff60-40f2-ab90-3c16acf34803")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "3bd7f61b-6314-47d3-a848-326c769695ef")
        private String createdBy;

        /** 计费节点 */
        @AutoGenerated(locked = true, uuid = "*************-413f-801f-e1813d2988de")
        private String billingNode;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setPartCountCalculationDay(Long partCountCalculationDay) {
            this.__$validPropertySet.add("partCountCalculationDay");
            this.partCountCalculationDay = partCountCalculationDay;
        }

        @AutoGenerated(locked = true)
        public void setPartMaxCount(Long partMaxCount) {
            this.__$validPropertySet.add("partMaxCount");
            this.partMaxCount = partMaxCount;
        }

        @AutoGenerated(locked = true)
        public void setPartValidCount(Long partValidCount) {
            this.__$validPropertySet.add("partValidCount");
            this.partValidCount = partValidCount;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedCount(Long enhancedCount) {
            this.__$validPropertySet.add("enhancedCount");
            this.enhancedCount = enhancedCount;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedScanningMutexFlag(Boolean enhancedScanningMutexFlag) {
            this.__$validPropertySet.add("enhancedScanningMutexFlag");
            this.enhancedScanningMutexFlag = enhancedScanningMutexFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setBillingNode(String billingNode) {
            this.__$validPropertySet.add("billingNode");
            this.billingNode = billingNode;
        }
    }

    /**
     * <b>[源自]</b> ExamTypeBodyDictionary
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeBodyDictionaryBto {
        /** 排序编号 */
        @AutoGenerated(locked = true, uuid = "344f4d0b-0c51-448f-ade5-5c2a850956e2")
        private Long sortNumber;

        /** 检查部位代码 */
        @AutoGenerated(locked = true, uuid = "87284583-d9c4-4808-b0a7-76a5fa5a9780")
        private String examBodyCode;

        /** 检查部位名称 */
        @AutoGenerated(locked = true, uuid = "4bf7906e-9419-48b2-8c0c-1581c713c5f2")
        private String examBodyName;

        /** 输入代码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "acd47b14-b548-42d2-ae7d-6e370ce147db")
        private InputCodeEo inputCode;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "55210c02-84d5-421f-a459-8bbd232b5d57")
        private String createdBy;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "0c787a20-c4f9-4ad4-bdf5-ddf5385120a1")
        private String updatedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setSortNumber(Long sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setExamBodyCode(String examBodyCode) {
            this.__$validPropertySet.add("examBodyCode");
            this.examBodyCode = examBodyCode;
        }

        @AutoGenerated(locked = true)
        public void setExamBodyName(String examBodyName) {
            this.__$validPropertySet.add("examBodyName");
            this.examBodyName = examBodyName;
        }

        @AutoGenerated(locked = true)
        public void setInputCode(InputCodeEo inputCode) {
            this.__$validPropertySet.add("inputCode");
            this.inputCode = inputCode;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }
    }

    /**
     * <b>[源自]</b> ExamTypeChargeItem
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeChargeItemBto {
        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "d77da3c3-918d-474a-a395-fa3b0ffc2ed6")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "7cbcf2e0-1fd1-4a24-8428-e60230e69746")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "1c87142c-6c3d-452d-94c9-dc1c3a4f5b20")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "b5808364-d2f3-4edb-821a-bd26a5c6bbf7")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "4f4daaf3-0243-41bd-8ee3-d962c3c3a648")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "be01397e-2acd-4043-b3a7-fa04c71e132f")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "320e8178-336f-4462-9589-8f78889d5ca3")
        private List<String> useScopeList;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "f129f60b-e6a5-45b1-aadd-4f607e884f3f")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "44be3a0d-2c59-49a2-b7e7-************")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "d7051f44-da29-48bd-81cb-74070976f64a")
        private String createdBy;

        /** 收费部位数量 */
        @AutoGenerated(locked = true, uuid = "1add4243-3fee-4492-9bbc-e63630bc8469")
        private Long chargePartNumber;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setChargePartNumber(Long chargePartNumber) {
            this.__$validPropertySet.add("chargePartNumber");
            this.chargePartNumber = chargePartNumber;
        }
    }
}
