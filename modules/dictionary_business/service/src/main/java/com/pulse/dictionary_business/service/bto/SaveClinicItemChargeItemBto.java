package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ClinicItemDictionary
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "f0a7eed8-2bf4-4167-84ef-f7bb533aee6f|BTO|DEFINITION")
public class SaveClinicItemChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "dbd50aee-4f89-41cd-9712-235e7c7cf425")
    private List<SaveClinicItemChargeItemBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8930da9a-8aa3-489f-a216-57bc39112ddd")
    private String clinicItemId;

    @AutoGenerated(locked = true)
    public void setClinicItemChargeItemBtoList(
            List<SaveClinicItemChargeItemBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList) {
        this.__$validPropertySet.add("clinicItemChargeItemBtoList");
        this.clinicItemChargeItemBtoList = clinicItemChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemId(String clinicItemId) {
        this.__$validPropertySet.add("clinicItemId");
        this.clinicItemId = clinicItemId;
    }

    /**
     * <b>[源自]</b> ClinicItemChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "411d5745-cf0c-41a1-8ef5-927ba29f4090")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "969eea01-5c71-4a3d-afd5-c7b6021a98d6")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "44788537-611b-435e-bf43-0085aa6604f8")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "3b534d59-c8c2-4649-a6ea-e2ff6359e34b")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "5df10ebf-195f-4b06-87a3-8c244517502e")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "43378ef7-dd39-48ce-8569-82ef5891ffdd")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "5df06650-bfbc-486e-9e46-bf24ec676c1b")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "8d12fb74-a868-4296-a2eb-d20ce9601f0e")
        private List<String> useScopeList;

        /** 首次计费标志 */
        @AutoGenerated(locked = true, uuid = "1f737267-c2a6-4993-9754-cce1dfa6d90c")
        private Boolean firstTimeBillingFlag;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "09a2c71c-12f5-41e0-b79e-8272c2f5d5ee")
        private String performDepartmentId;

        /** 允许修改计数标志 */
        @AutoGenerated(locked = true, uuid = "dc2facab-ff13-486a-8edf-7156f1a28179")
        private Boolean allowModifyCountFlag;

        /** 诊疗项目计费类型 */
        @AutoGenerated(locked = true, uuid = "284faeb1-c027-40ef-8451-876b42d61c39")
        private String clinicItemBillingType;

        /** 诊疗医保代码 */
        @AutoGenerated(locked = true, uuid = "81b311c2-b3c0-4548-b7f4-498f6ddfc5a4")
        private String clinicInsuranceCode;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setFirstTimeBillingFlag(Boolean firstTimeBillingFlag) {
            this.__$validPropertySet.add("firstTimeBillingFlag");
            this.firstTimeBillingFlag = firstTimeBillingFlag;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setAllowModifyCountFlag(Boolean allowModifyCountFlag) {
            this.__$validPropertySet.add("allowModifyCountFlag");
            this.allowModifyCountFlag = allowModifyCountFlag;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemBillingType(String clinicItemBillingType) {
            this.__$validPropertySet.add("clinicItemBillingType");
            this.clinicItemBillingType = clinicItemBillingType;
        }

        @AutoGenerated(locked = true)
        public void setClinicInsuranceCode(String clinicInsuranceCode) {
            this.__$validPropertySet.add("clinicInsuranceCode");
            this.clinicInsuranceCode = clinicInsuranceCode;
        }
    }
}
