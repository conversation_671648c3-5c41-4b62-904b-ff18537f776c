package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamDevice
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "904e287b-be2a-4cc9-88d6-04a9883e277f|BTO|DEFINITION")
public class SaveExamDeviceChargeItemListBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "d52b567c-b517-465d-b983-a9de48988f5d")
    private List<SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto>
            examDeviceChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "86499b37-430d-4376-ac3a-50cb7c7169dd")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamDeviceChargeItemBtoList(
            List<SaveExamDeviceChargeItemListBto.ExamDeviceChargeItemBto>
                    examDeviceChargeItemBtoList) {
        this.__$validPropertySet.add("examDeviceChargeItemBtoList");
        this.examDeviceChargeItemBtoList = examDeviceChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamDeviceChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "bad1e06f-4038-4afc-9135-7411bd58fb62")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "35ef7f69-13ab-429b-a87f-70236d1bdb06")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "db1afb50-68f5-4483-a91f-b77e4505b72f")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "73d536d0-6fea-46f6-98b4-dca77fb7053b")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "e78c4cdb-96fc-4df6-b29b-ab2fae440196")
        private FilmFeeTypeEnum filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "b0f88e7b-3e83-4265-96c7-3972b1a86e7e")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "b21e9042-1d21-4770-a42b-6a9724293ac6")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "e97bc35d-eacf-4f2e-965e-af9a73714b56")
        private List<String> useScopeList;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "5f3cb6bc-49cf-4d2a-ab48-01dd445f275b")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "3eaef012-7e49-48c2-b618-19d4fa9ffc29")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "844d45c3-5abe-48cc-8c57-25c24531897c")
        private String createdBy;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "5671e2d1-8c85-421d-a095-3f97c6f5d0a6")
        private Boolean enhancedFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(FilmFeeTypeEnum filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }
    }
}
