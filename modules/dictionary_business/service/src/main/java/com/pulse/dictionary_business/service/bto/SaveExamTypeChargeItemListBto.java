package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamTypeDictionary
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "c11eb555-7484-42d5-b671-b11aa64e176e|BTO|DEFINITION")
public class SaveExamTypeChargeItemListBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "31361807-5b55-4592-a49f-914d29e0a676")
    private List<SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "24039408-864d-4621-a40a-660b836f4892")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamTypeChargeItemBtoList(
            List<SaveExamTypeChargeItemListBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList) {
        this.__$validPropertySet.add("examTypeChargeItemBtoList");
        this.examTypeChargeItemBtoList = examTypeChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamTypeChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "c0a69af3-b4f3-4c8f-ba3d-0c2ad97e2631")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "d12e4aa2-14a5-418a-b37e-40330e11c5e1")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "c6ad51aa-2c9d-45ce-989a-9bb6d6bd3d0b")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "1152651a-9921-462b-a13e-b237df9474af")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "df0dfc23-fb3c-490d-9f5d-c819e811ffac")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "f8a13835-f49a-4abb-ad77-b8550c6aa2de")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "6a5f017a-3eb4-40b1-a71a-8d33142b4ece")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "13bcbf72-0801-4875-b4d4-f9f03d5ed097")
        private List<String> useScopeList;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "ecddc160-47fd-49b1-a82a-0376069c522f")
        private Boolean enableFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "1a57e774-3ba1-404e-963c-e64501cdd3e2")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "309139aa-3fcf-44d6-a9c6-69a74cf76119")
        private String createdBy;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "0786a819-9389-4388-a0ba-ba76c3bc96cf")
        private Boolean enhancedFlag;

        /** 收费部位数量 */
        @AutoGenerated(locked = true, uuid = "1a42cd4f-6e2a-410b-a224-712dbc185fdc")
        private Long chargePartNumber;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setEnableFlag(Boolean enableFlag) {
            this.__$validPropertySet.add("enableFlag");
            this.enableFlag = enableFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }

        @AutoGenerated(locked = true)
        public void setChargePartNumber(Long chargePartNumber) {
            this.__$validPropertySet.add("chargePartNumber");
            this.chargePartNumber = chargePartNumber;
        }
    }
}
