package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ClinicItemDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "b660cb89-f358-4d3a-ad04-8062207ed3df|BTO|DEFINITION")
public class UpdateClinicItemDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 最大年龄限制 */
    @AutoGenerated(locked = true, uuid = "2cc97853-8d45-4a41-aa56-d40b2d5cab1d")
    private Long ageMaxLimit;

    /** 最小年龄限制 */
    @AutoGenerated(locked = true, uuid = "6e7f5cbf-38f0-4b27-ae0f-53aebf43890f")
    private Long ageMinLimit;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "3f7e5d10-67c3-40a7-a979-f805a02f6e92")
    private String alias;

    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "9898215c-eff0-47e7-98ab-50c2e0b53ec1")
    private Date auditDate;

    /** 审核标记 */
    @AutoGenerated(locked = true, uuid = "594e1432-5912-43ba-974f-fc4493525acb")
    private Boolean auditFlag;

    /** 审核操作员 */
    @AutoGenerated(locked = true, uuid = "4d08c660-cb62-4554-b21f-c3846900c669")
    private String auditOperatorId;

    /** 计价标识 */
    @AutoGenerated(locked = true, uuid = "0df25161-ac71-4c7c-8a45-1665438e0b81")
    private String billingAttribute;

    /** 计费间隔 时间间隔分钟 */
    @AutoGenerated(locked = true, uuid = "c83a583f-6bec-4aa7-bf4d-c598d58cb525")
    private Long billingInterval;

    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "9807765f-a3a4-4aeb-8086-4605b8ea9aec")
    private List<String> campusIdList;

    /** 卡片打印类型 字典：卡片打印类型 */
    @Valid
    @AutoGenerated(locked = true, uuid = "dd6b9faf-4053-4aa2-9c9c-56fa868b5666")
    private List<String> cardPrintTypeList;

    /** 诊疗项目目录 */
    @AutoGenerated(locked = true, uuid = "b7d6e08d-134f-46bd-9cb4-32c91b13c6be")
    private String clinicItemCatalogId;

    @Valid
    @AutoGenerated(locked = true, uuid = "65fff0f3-876f-43cb-b5de-f6c6b8591fe6")
    private List<UpdateClinicItemDetailBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "aa663236-32fe-4149-888d-059a7b90544b")
    private String clinicItemId;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "5556df8a-0a2d-477f-99e1-4fc7192660db")
    private String clinicItemName;

    @Valid
    @AutoGenerated(locked = true, uuid = "8a5f67a4-d526-4b70-abd0-751008ebad9b")
    private List<UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto>
            clinicItemPerformDepartmentBtoList;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "64dc5297-9b22-4c1a-8093-e8a5ccfd4741")
    private String createdBy;

    /** 延迟天数 */
    @AutoGenerated(locked = true, uuid = "7ad67432-c252-4de8-b368-a4a629590ef4")
    private Long delayDays;

    /** 项目说明 */
    @AutoGenerated(locked = true, uuid = "8361fc00-a67f-46da-9e4d-78345e8a532e")
    private String description;

    /** 禁用原因 */
    @AutoGenerated(locked = true, uuid = "2f338c91-177c-4a08-a253-f551c201b091")
    private String disabledReason;

    /** 双签名标志 */
    @AutoGenerated(locked = true, uuid = "9091cb3b-fc93-40cd-8081-39621c759e93")
    private Boolean doubleSignatureFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "17d62d08-e369-4091-8519-b8a67d3d54c2")
    private Boolean enableFlag;

    /** 排斥时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1244eeb8-ee3d-4f8f-bc67-ed7509769aa4")
    private TimeEo exclusionTime;

    /** 排斥类型 */
    @AutoGenerated(locked = true, uuid = "4fb42091-ae94-48ef-8741-153953a44be2")
    private String exclusionType;

    /** 频次 */
    @AutoGenerated(locked = true, uuid = "fcb07f81-2f1e-4fd5-96da-7ba8cc257455")
    private String frequency;

    /** 默认频率不允许修改标志 */
    @AutoGenerated(locked = true, uuid = "551d438b-94e8-4a69-afbf-71f64e5212ec")
    private Boolean frequencyNotAllowedModifyFlag;

    /** 执行科室是否默认包含本科室 */
    @AutoGenerated(locked = true, uuid = "db679a21-ff35-4aaa-b468-72559e28b194")
    private Boolean includeCurrentDepartmentFlag;

    /** 住院默认执行科室类型 */
    @AutoGenerated(locked = true, uuid = "1c972ab8-b3fc-4431-b146-f77bc4a681ba")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "81128db6-7f7a-4acc-a501-f7e56d09a093")
    private InputCodeEo inputCode;

    /** 机构代码 */
    @AutoGenerated(locked = true, uuid = "41da1a78-342b-4029-9aa8-f9c00ae848fd")
    private String institutionId;

    /** 项目规格 */
    @AutoGenerated(locked = true, uuid = "a19705b5-0bee-4794-b16b-5b50974f7c97")
    private String itemSpecification;

    /** 医嘱项目类型 */
    @AutoGenerated(locked = true, uuid = "289e0ce6-e271-42fa-abc9-3391d353c189")
    private String itemType;

    /** 限制性别 */
    @AutoGenerated(locked = true, uuid = "d272679b-0775-47a3-adf4-39cef000e1c8")
    private String limitGender;

    /** 限制病房ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b67c05b0-32a2-4203-b36a-276a8ee2f1fa")
    private List<String> limitWardIdList;

    /** 是否只允许选择配置的执行科室 */
    @AutoGenerated(locked = true, uuid = "a9873dec-0137-4836-a6d7-c5373e47f09b")
    private Boolean onlySelectSettingDepartmentFlag;

    /** 对应手术icd9编码 */
    @AutoGenerated(locked = true, uuid = "2586ebdf-831f-4a3a-8452-a2936c41363d")
    private String operationCode;

    /** 医嘱频次计费类型 */
    @AutoGenerated(locked = true, uuid = "7d716f38-cb0f-4d7d-8a93-e28590b42d0b")
    private String orderFrequencyBillingType;

    /** 门诊默认执行科室ID */
    @AutoGenerated(locked = true, uuid = "b2e61d2b-9c11-4a76-be7e-b1b8f7c3f9b1")
    private String outpDefaultPerformDepartmentId;

    /** PDA执行标志 */
    @AutoGenerated(locked = true, uuid = "82314f3d-a95f-482b-bb6b-a410d6acc3f6")
    private Boolean pdaPerformFlag;

    /** 打印标志 */
    @AutoGenerated(locked = true, uuid = "52a02259-c072-4942-aea5-b89f8300b6a1")
    private Boolean printFlag;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "52ccfc29-60cd-4037-b7ef-29101c6a9d48")
    private String remark;

    /** 医保抢救标志 */
    @AutoGenerated(locked = true, uuid = "19668dfa-05ba-4cf5-bac7-0d60bfda9006")
    private Boolean rescueFlag;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "08256c49-5a99-4172-b00a-6cf2359c190e")
    private Long sortNumber;

    /** 是否特需 */
    @AutoGenerated(locked = true, uuid = "dd491290-cf2c-484f-9e50-6695c28dd482")
    private Boolean specialNeedFlag;

    /** 标准代码 */
    @AutoGenerated(locked = true, uuid = "d4a08f31-1028-4bc3-b650-5204cd753484")
    private String standardCode;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "03f00dbd-4c67-43c0-a56c-4c3e784f7db0")
    private String unit;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "6f940466-0a6a-4358-ab4d-b0aeb3c591e6")
    private String updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "719f0241-4a4d-4ddf-bb92-2c16ca5de582")
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public void setAgeMaxLimit(Long ageMaxLimit) {
        this.__$validPropertySet.add("ageMaxLimit");
        this.ageMaxLimit = ageMaxLimit;
    }

    @AutoGenerated(locked = true)
    public void setAgeMinLimit(Long ageMinLimit) {
        this.__$validPropertySet.add("ageMinLimit");
        this.ageMinLimit = ageMinLimit;
    }

    @AutoGenerated(locked = true)
    public void setAlias(String alias) {
        this.__$validPropertySet.add("alias");
        this.alias = alias;
    }

    @AutoGenerated(locked = true)
    public void setAuditDate(Date auditDate) {
        this.__$validPropertySet.add("auditDate");
        this.auditDate = auditDate;
    }

    @AutoGenerated(locked = true)
    public void setAuditFlag(Boolean auditFlag) {
        this.__$validPropertySet.add("auditFlag");
        this.auditFlag = auditFlag;
    }

    @AutoGenerated(locked = true)
    public void setAuditOperatorId(String auditOperatorId) {
        this.__$validPropertySet.add("auditOperatorId");
        this.auditOperatorId = auditOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setBillingAttribute(String billingAttribute) {
        this.__$validPropertySet.add("billingAttribute");
        this.billingAttribute = billingAttribute;
    }

    @AutoGenerated(locked = true)
    public void setBillingInterval(Long billingInterval) {
        this.__$validPropertySet.add("billingInterval");
        this.billingInterval = billingInterval;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setCardPrintType(List<String> cardPrintType) {
        this.__$validPropertySet.add("cardPrintTypeList");
        this.cardPrintTypeList = cardPrintType;
    }

    @AutoGenerated(locked = true)
    public void setCardPrintTypeList(List<String> cardPrintTypeList) {
        this.__$validPropertySet.add("cardPrintTypeList");
        this.cardPrintTypeList = cardPrintTypeList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemCatalogId(String clinicItemCatalogId) {
        this.__$validPropertySet.add("clinicItemCatalogId");
        this.clinicItemCatalogId = clinicItemCatalogId;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemChargeItemBtoList(
            List<UpdateClinicItemDetailBto.ClinicItemChargeItemBto> clinicItemChargeItemBtoList) {
        this.__$validPropertySet.add("clinicItemChargeItemBtoList");
        this.clinicItemChargeItemBtoList = clinicItemChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemId(String clinicItemId) {
        this.__$validPropertySet.add("clinicItemId");
        this.clinicItemId = clinicItemId;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemName(String clinicItemName) {
        this.__$validPropertySet.add("clinicItemName");
        this.clinicItemName = clinicItemName;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemPerformDepartmentBtoList(
            List<UpdateClinicItemDetailBto.ClinicItemPerformDepartmentBto>
                    clinicItemPerformDepartmentBtoList) {
        this.__$validPropertySet.add("clinicItemPerformDepartmentBtoList");
        this.clinicItemPerformDepartmentBtoList = clinicItemPerformDepartmentBtoList;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDelayDays(Long delayDays) {
        this.__$validPropertySet.add("delayDays");
        this.delayDays = delayDays;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setDisabledReason(String disabledReason) {
        this.__$validPropertySet.add("disabledReason");
        this.disabledReason = disabledReason;
    }

    @AutoGenerated(locked = true)
    public void setDoubleSignatureFlag(Boolean doubleSignatureFlag) {
        this.__$validPropertySet.add("doubleSignatureFlag");
        this.doubleSignatureFlag = doubleSignatureFlag;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setExclusionTime(TimeEo exclusionTime) {
        this.__$validPropertySet.add("exclusionTime");
        this.exclusionTime = exclusionTime;
    }

    @AutoGenerated(locked = true)
    public void setExclusionType(String exclusionType) {
        this.__$validPropertySet.add("exclusionType");
        this.exclusionType = exclusionType;
    }

    @AutoGenerated(locked = true)
    public void setFrequency(String frequency) {
        this.__$validPropertySet.add("frequency");
        this.frequency = frequency;
    }

    @AutoGenerated(locked = true)
    public void setFrequencyNotAllowedModifyFlag(Boolean frequencyNotAllowedModifyFlag) {
        this.__$validPropertySet.add("frequencyNotAllowedModifyFlag");
        this.frequencyNotAllowedModifyFlag = frequencyNotAllowedModifyFlag;
    }

    @AutoGenerated(locked = true)
    public void setIncludeCurrentDepartmentFlag(Boolean includeCurrentDepartmentFlag) {
        this.__$validPropertySet.add("includeCurrentDepartmentFlag");
        this.includeCurrentDepartmentFlag = includeCurrentDepartmentFlag;
    }

    @AutoGenerated(locked = true)
    public void setInpDefaultPerformDepartmentType(
            DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType) {
        this.__$validPropertySet.add("inpDefaultPerformDepartmentType");
        this.inpDefaultPerformDepartmentType = inpDefaultPerformDepartmentType;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setInstitutionId(String institutionId) {
        this.__$validPropertySet.add("institutionId");
        this.institutionId = institutionId;
    }

    @AutoGenerated(locked = true)
    public void setItemSpecification(String itemSpecification) {
        this.__$validPropertySet.add("itemSpecification");
        this.itemSpecification = itemSpecification;
    }

    @AutoGenerated(locked = true)
    public void setItemType(String itemType) {
        this.__$validPropertySet.add("itemType");
        this.itemType = itemType;
    }

    @AutoGenerated(locked = true)
    public void setLimitGender(String limitGender) {
        this.__$validPropertySet.add("limitGender");
        this.limitGender = limitGender;
    }

    @AutoGenerated(locked = true)
    public void setLimitWardIdList(List<String> limitWardIdList) {
        this.__$validPropertySet.add("limitWardIdList");
        this.limitWardIdList = limitWardIdList;
    }

    @AutoGenerated(locked = true)
    public void setOnlySelectSettingDepartmentFlag(Boolean onlySelectSettingDepartmentFlag) {
        this.__$validPropertySet.add("onlySelectSettingDepartmentFlag");
        this.onlySelectSettingDepartmentFlag = onlySelectSettingDepartmentFlag;
    }

    @AutoGenerated(locked = true)
    public void setOperationCode(String operationCode) {
        this.__$validPropertySet.add("operationCode");
        this.operationCode = operationCode;
    }

    @AutoGenerated(locked = true)
    public void setOrderFrequencyBillingType(String orderFrequencyBillingType) {
        this.__$validPropertySet.add("orderFrequencyBillingType");
        this.orderFrequencyBillingType = orderFrequencyBillingType;
    }

    @AutoGenerated(locked = true)
    public void setOutpDefaultPerformDepartmentId(String outpDefaultPerformDepartmentId) {
        this.__$validPropertySet.add("outpDefaultPerformDepartmentId");
        this.outpDefaultPerformDepartmentId = outpDefaultPerformDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setPdaPerformFlag(Boolean pdaPerformFlag) {
        this.__$validPropertySet.add("pdaPerformFlag");
        this.pdaPerformFlag = pdaPerformFlag;
    }

    @AutoGenerated(locked = true)
    public void setPrintFlag(Boolean printFlag) {
        this.__$validPropertySet.add("printFlag");
        this.printFlag = printFlag;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setRescueFlag(Boolean rescueFlag) {
        this.__$validPropertySet.add("rescueFlag");
        this.rescueFlag = rescueFlag;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setSpecialNeedFlag(Boolean specialNeedFlag) {
        this.__$validPropertySet.add("specialNeedFlag");
        this.specialNeedFlag = specialNeedFlag;
    }

    @AutoGenerated(locked = true)
    public void setStandardCode(String standardCode) {
        this.__$validPropertySet.add("standardCode");
        this.standardCode = standardCode;
    }

    @AutoGenerated(locked = true)
    public void setUnit(String unit) {
        this.__$validPropertySet.add("unit");
        this.unit = unit;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    @AutoGenerated(locked = true)
    public void setUseScopeList(List<String> useScopeList) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScopeList;
    }

    /**
     * <b>[源自]</b> ClinicItemChargeItem
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "1ad7446a-a0e1-4b53-9e5f-956a2583c5d8")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "80df2491-2724-4b0f-ad9b-8b4449794bda")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "1a506753-425e-4ea5-af16-5cce8f8e8615")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "8837df33-81dc-4591-80de-81d734585b0e")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "ab8b1ac2-6952-4f72-b9d5-1daeedaebda0")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "97ddccb8-6ddc-486f-96a8-1e454f20d77e")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "58778a4d-024d-4c45-91f8-2c353c8446d2")
        private Boolean digitalImagingFeeFlag;

        /** 首次计费标志 */
        @AutoGenerated(locked = true, uuid = "7d4df9e0-766a-4744-905e-028035c8a784")
        private Boolean firstTimeBillingFlag;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "8626a1d6-d176-4ca5-a6c6-34f1ed070e23")
        private String performDepartmentId;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "fa9bf970-a430-435e-a750-74e18d32e720")
        private List<String> useScopeList;

        /** 允许修改计数标志 */
        @AutoGenerated(locked = true, uuid = "d61de577-231d-4f07-a255-302472b16dd6")
        private Boolean allowModifyCountFlag;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "fc7df4c1-5fb8-46ff-b2b5-5a879dcad803")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "5312fe72-d25b-459d-8cc9-5b81ac2e15c7")
        private String createdBy;

        /** 诊疗项目计费类型 */
        @AutoGenerated(locked = true, uuid = "7dec744b-8f08-4d9c-a595-cb43b0291e7e")
        private String clinicItemBillingType;

        /** 诊疗医保代码 */
        @AutoGenerated(locked = true, uuid = "cdadf35b-4533-4577-beeb-51f1a80e204d")
        private String clinicInsuranceCode;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setFirstTimeBillingFlag(Boolean firstTimeBillingFlag) {
            this.__$validPropertySet.add("firstTimeBillingFlag");
            this.firstTimeBillingFlag = firstTimeBillingFlag;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setAllowModifyCountFlag(Boolean allowModifyCountFlag) {
            this.__$validPropertySet.add("allowModifyCountFlag");
            this.allowModifyCountFlag = allowModifyCountFlag;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setClinicItemBillingType(String clinicItemBillingType) {
            this.__$validPropertySet.add("clinicItemBillingType");
            this.clinicItemBillingType = clinicItemBillingType;
        }

        @AutoGenerated(locked = true)
        public void setClinicInsuranceCode(String clinicInsuranceCode) {
            this.__$validPropertySet.add("clinicInsuranceCode");
            this.clinicInsuranceCode = clinicInsuranceCode;
        }
    }

    /**
     * <b>[源自]</b> ClinicItemPerformDepartment
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicItemPerformDepartmentBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "181d4df1-a563-48d2-bc46-b607356ba81e")
        private Long id;

        /** 使用组织类型 */
        @AutoGenerated(locked = true, uuid = "bbfa32ed-29b4-4f98-8a97-a67e40728e71")
        private String useOrganizationType;

        /** 组织ID */
        @AutoGenerated(locked = true, uuid = "d6e9f8fa-b925-43c1-8080-************")
        private String organizationId;

        /** 开单科室id */
        @AutoGenerated(locked = true, uuid = "7c824bc0-c7a2-4c95-8da8-0cb3e97629ec")
        private String orderDepartmentId;

        /** 执行科室id */
        @AutoGenerated(locked = true, uuid = "657c5c15-528a-4c72-856d-f55b3a8ac807")
        private String performDepartmentId;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "ba099891-c2cf-4b40-adfa-c83163713667")
        private List<String> useScopeList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(Long id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setUseOrganizationType(String useOrganizationType) {
            this.__$validPropertySet.add("useOrganizationType");
            this.useOrganizationType = useOrganizationType;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setOrderDepartmentId(String orderDepartmentId) {
            this.__$validPropertySet.add("orderDepartmentId");
            this.orderDepartmentId = orderDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setPerformDepartmentId(String performDepartmentId) {
            this.__$validPropertySet.add("performDepartmentId");
            this.performDepartmentId = performDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }
    }
}
