package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_business.common.enums.FilmFeeTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamDevice
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "cb09aab2-4031-483a-bb33-296219bc1859|BTO|DEFINITION")
public class UpdateExamDeviceChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "822db94d-e668-4916-a6fc-5fb79a2d1a0a")
    private List<UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto> examDeviceChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9f583ed8-3885-495f-abd4-866b60ff6749")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamDeviceChargeItemBtoList(
            List<UpdateExamDeviceChargeItemBto.ExamDeviceChargeItemBto>
                    examDeviceChargeItemBtoList) {
        this.__$validPropertySet.add("examDeviceChargeItemBtoList");
        this.examDeviceChargeItemBtoList = examDeviceChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamDeviceChargeItem
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamDeviceChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "d3a68a24-b32e-46c0-8849-b6fb02ed7310")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "e06c1593-fc67-4b24-bc80-d2f145e6be9b")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "c54b7c33-e91a-4bea-9ff9-5a35eba19ae1")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "42fbe526-06f2-4818-8946-830fdff72086")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "1500ad07-d851-46b1-b5bb-04697020f33f")
        private FilmFeeTypeEnum filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "e78a4370-aad8-4421-9640-fd0e03573be4")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "77f68af4-f689-4ac1-9851-2eac9f918b6a")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "7e61bb2a-2090-44d3-b78f-597da122356b")
        private List<String> useScopeList;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "f65104e8-eab3-492f-a94c-7e668c93bab6")
        private String updatedBy;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "adcc3e21-7a61-4ceb-b6ea-6239a0a3ef75")
        private Boolean enhancedFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(FilmFeeTypeEnum filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }
    }
}
