package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamTypeDictionary
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "ed15b43c-ee8a-4450-a354-902a6fceeb8e|BTO|DEFINITION")
public class UpdateExamTypeChargeItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "4b92a767-6f90-420a-ad14-33a58426073d")
    private List<UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c17da670-8d02-4462-856a-60cb4c88119e")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamTypeChargeItemBtoList(
            List<UpdateExamTypeChargeItemBto.ExamTypeChargeItemBto> examTypeChargeItemBtoList) {
        this.__$validPropertySet.add("examTypeChargeItemBtoList");
        this.examTypeChargeItemBtoList = examTypeChargeItemBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamTypeChargeItem
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamTypeChargeItemBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "ec287dac-b68f-49e3-93b4-83be315a266e")
        private String id;

        /** 收费项目ID */
        @AutoGenerated(locked = true, uuid = "ea3c7206-14de-4dac-a42a-0ebc33533284")
        private String chargeItemId;

        /** 院区ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "7214053a-0efa-4d8a-9c64-d3eefac6e394")
        private List<String> campusIdList;

        /** 收费项目数量 */
        @AutoGenerated(locked = true, uuid = "c995cf6b-5849-49a6-b09f-db8a73b1973c")
        private Long chargeItemCount;

        /** 胶片费类型 */
        @AutoGenerated(locked = true, uuid = "0ececc7f-898e-4f3c-b3ba-9d255dc78aa8")
        private String filmFeeType;

        /** 图文费用标志 */
        @AutoGenerated(locked = true, uuid = "24322b6d-e73e-4f0c-8e5e-87a8ed9f7a6a")
        private Boolean graphicFeeFlag;

        /** 数字成像费用标志 */
        @AutoGenerated(locked = true, uuid = "06eb39f1-f3ab-4aa0-aac8-a5571e6e9d13")
        private Boolean digitalImagingFeeFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "4974df92-32e9-4fa1-bd2f-bc48c53d9e0a")
        private List<String> useScopeList;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "c5e85b64-e886-4be0-a9e5-8876d3ef8d4b")
        private String updatedBy;

        /** 增强标志 */
        @AutoGenerated(locked = true, uuid = "91c3e76b-0a1c-4e86-99d3-399bf37fcd81")
        private Boolean enhancedFlag;

        /** 收费部位数量 */
        @AutoGenerated(locked = true, uuid = "018dfd48-03ba-473a-a5a2-ecebdab89185")
        private Long chargePartNumber;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemId(String chargeItemId) {
            this.__$validPropertySet.add("chargeItemId");
            this.chargeItemId = chargeItemId;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setChargeItemCount(Long chargeItemCount) {
            this.__$validPropertySet.add("chargeItemCount");
            this.chargeItemCount = chargeItemCount;
        }

        @AutoGenerated(locked = true)
        public void setFilmFeeType(String filmFeeType) {
            this.__$validPropertySet.add("filmFeeType");
            this.filmFeeType = filmFeeType;
        }

        @AutoGenerated(locked = true)
        public void setGraphicFeeFlag(Boolean graphicFeeFlag) {
            this.__$validPropertySet.add("graphicFeeFlag");
            this.graphicFeeFlag = graphicFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setDigitalImagingFeeFlag(Boolean digitalImagingFeeFlag) {
            this.__$validPropertySet.add("digitalImagingFeeFlag");
            this.digitalImagingFeeFlag = digitalImagingFeeFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setEnhancedFlag(Boolean enhancedFlag) {
            this.__$validPropertySet.add("enhancedFlag");
            this.enhancedFlag = enhancedFlag;
        }

        @AutoGenerated(locked = true)
        public void setChargePartNumber(Long chargePartNumber) {
            this.__$validPropertySet.add("chargePartNumber");
            this.chargePartNumber = chargePartNumber;
        }
    }
}
