package com.pulse.rule_engine.entrance.web.controller;

import com.pulse.rule_engine.entrance.web.converter.RuleGroupBaseVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleGroupDetailVoConverter;
import com.pulse.rule_engine.entrance.web.vo.RuleGroupBaseVo;
import com.pulse.rule_engine.entrance.web.vo.RuleGroupDetailVo;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.persist.qto.ListRuleGroupQto;
import com.pulse.rule_engine.service.RuleGroupBOService;
import com.pulse.rule_engine.service.RuleGroupDetailBaseDtoService;
import com.pulse.rule_engine.service.bto.DeleteRuleGroupBto;
import com.pulse.rule_engine.service.bto.MergeRuleGroupBto;
import com.pulse.rule_engine.service.query.RuleGroupBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "f8702cc2-aa69-3df4-a4cc-158899ebd83c")
public class RuleGroupController {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBOService ruleGroupBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseDtoQueryService ruleGroupBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseVoConverter ruleGroupBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailBaseDtoService ruleGroupDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailVoConverter ruleGroupDetailVoConverter;

    /** 根据分组ID获取规则组详情列表 */
    @PublicInterface(id = "4be18e4d-762b-49be-ab24-a9d723f06095", version = "1749018671923")
    @AutoGenerated(locked = false, uuid = "4be18e4d-762b-49be-ab24-a9d723f06095")
    @RequestMapping(
            value = {"/api/rule-engine/list-rule-group-detail-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<RuleGroupDetailVo> listRuleGroupDetailById(@NotNull String groupId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleGroupDetailBaseDto> rpcResult =
                ruleGroupDetailBaseDtoService.getByGroupId(groupId);
        List<RuleGroupDetailVo> result =
                ruleGroupDetailVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除规则组 */
    @PublicInterface(id = "70694774-7eb2-46e8-8f29-03d2552e8ce2", version = "1749018191017")
    @AutoGenerated(locked = false, uuid = "70694774-7eb2-46e8-8f29-03d2552e8ce2")
    @RequestMapping(
            value = {"/api/rule-engine/delete-rule-group"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteRuleGroup(@Valid @NotNull DeleteRuleGroupBto deleteRuleGroupBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = ruleGroupBOService.deleteRuleGroup(deleteRuleGroupBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询规则组列表（分页） */
    @PublicInterface(id = "8fb259ef-0385-4659-9b20-afa91b7c8b4b", version = "1749018408984")
    @AutoGenerated(locked = false, uuid = "8fb259ef-0385-4659-9b20-afa91b7c8b4b")
    @RequestMapping(
            value = {"/api/rule-engine/list-rule-group-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RuleGroupBaseVo> listRuleGroupPaged(@Valid @NotNull ListRuleGroupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RuleGroupBaseDto> dtoResult =
                ruleGroupBaseDtoQueryService.listRuleGroupPaged(qto);
        VSQueryResult<RuleGroupBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                ruleGroupBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存规则组 保存规则组（新增、更新） */
    @PublicInterface(id = "b16e3bef-5e36-4ff2-911b-1b2c73a30712", version = "1749018166271")
    @AutoGenerated(locked = false, uuid = "b16e3bef-5e36-4ff2-911b-1b2c73a30712")
    @RequestMapping(
            value = {"/api/rule-engine/merge-rule-group"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeRuleGroup(@Valid @NotNull MergeRuleGroupBto mergeRuleGroupBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = ruleGroupBOService.mergeRuleGroup(mergeRuleGroupBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
