package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleGroupBaseVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleGroupBaseVo;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleGroupBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6efaabfb-3642-4af9-800a-d99fadbf52e1|VO|CONVERTER")
public class RuleGroupBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseVoDataAssembler ruleGroupBaseVoDataAssembler;

    /** 把RuleGroupBaseDto转换成RuleGroupBaseVo */
    @AutoGenerated(locked = true, uuid = "47de8f1f-35ee-37fe-ae50-b60f232f2a47")
    public RuleGroupBaseVo convertToRuleGroupBaseVo(RuleGroupBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleGroupBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把RuleGroupBaseDto转换成RuleGroupBaseVo */
    @AutoGenerated(locked = false, uuid = "6efaabfb-3642-4af9-800a-d99fadbf52e1-converter-Map")
    public Map<RuleGroupBaseDto, RuleGroupBaseVo> convertToRuleGroupBaseVoMap(
            List<RuleGroupBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleGroupBaseDto, RuleGroupBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleGroupBaseVo vo = new RuleGroupBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setDescription(dto.getDescription());
                                            vo.setLogicOperator(dto.getLogicOperator());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setStatus(dto.getStatus());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCode(dto.getCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleGroupBaseDto转换成RuleGroupBaseVo */
    @AutoGenerated(locked = true, uuid = "6efaabfb-3642-4af9-800a-d99fadbf52e1-converter-list")
    public List<RuleGroupBaseVo> convertToRuleGroupBaseVoList(List<RuleGroupBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleGroupBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装RuleGroupBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "797ac94b-4a86-3ba7-987a-d0e97e38cb6b")
    public RuleGroupBaseVo convertAndAssembleData(RuleGroupBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleGroupBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d7170f49-8d3b-354b-9dc1-1f7da435b9d3")
    public List<RuleGroupBaseVo> convertAndAssembleDataList(List<RuleGroupBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleGroupBaseVo> voMap =
                convertToRuleGroupBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleGroupBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
