package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleGroupDetailVoDataAssembler;
import com.pulse.rule_engine.entrance.web.query.assembler.RuleGroupDetailVoDataAssembler.RuleGroupDetailVoDataHolder;
import com.pulse.rule_engine.entrance.web.query.collector.RuleGroupDetailVoDataCollector;
import com.pulse.rule_engine.entrance.web.vo.RuleGroupDetailVo;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleGroupDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "2ed6b69d-b034-4167-aa53-1e348742dcda|VO|CONVERTER")
public class RuleGroupDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailVoDataAssembler ruleGroupDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailVoDataCollector ruleGroupDetailVoDataCollector;

    /** 把RuleGroupDetailBaseDto转换成RuleGroupDetailVo */
    @AutoGenerated(locked = false, uuid = "2ed6b69d-b034-4167-aa53-1e348742dcda-converter-Map")
    public Map<RuleGroupDetailBaseDto, RuleGroupDetailVo> convertToRuleGroupDetailVoMap(
            List<RuleGroupDetailBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleGroupDetailBaseDto, RuleGroupDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleGroupDetailVo vo = new RuleGroupDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setGroupId(dto.getGroupId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleGroupDetailBaseDto转换成RuleGroupDetailVo */
    @AutoGenerated(locked = true, uuid = "2ed6b69d-b034-4167-aa53-1e348742dcda-converter-list")
    public List<RuleGroupDetailVo> convertToRuleGroupDetailVoList(
            List<RuleGroupDetailBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleGroupDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装RuleGroupDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "97cfca44-fd99-3e12-8b34-3bd3de42da77")
    public List<RuleGroupDetailVo> convertAndAssembleDataList(
            List<RuleGroupDetailBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        RuleGroupDetailVoDataHolder dataHolder = new RuleGroupDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, RuleGroupDetailVo> voMap =
                convertToRuleGroupDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleGroupDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        ruleGroupDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把RuleGroupDetailBaseDto转换成RuleGroupDetailVo */
    @AutoGenerated(locked = true, uuid = "b590f85b-aed8-3a30-bc8d-d0a8688ccc2f")
    public RuleGroupDetailVo convertToRuleGroupDetailVo(RuleGroupDetailBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleGroupDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleGroupDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "e93e25b8-1782-31b1-815b-41e6d52286ce")
    public RuleGroupDetailVo convertAndAssembleData(RuleGroupDetailBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
