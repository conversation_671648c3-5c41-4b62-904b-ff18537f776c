package com.pulse.rule_engine.entrance.web.query.assembler;

import com.pulse.rule_engine.entrance.web.vo.RuleGroupBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** RuleGroupBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "af6ea1ff-1e98-340d-9686-9942056b12eb")
public class RuleGroupBaseVoDataAssembler {

    /** 批量自定义组装RuleGroupBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "9693c570-dc0f-3543-9b63-d1e9b6edf88c")
    public void assembleDataCustomized(List<RuleGroupBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装RuleGroupBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "d6ce5fc3-5cf4-30fb-8114-5f3b05028e6f")
    public void assembleData(Map<String, RuleGroupBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
