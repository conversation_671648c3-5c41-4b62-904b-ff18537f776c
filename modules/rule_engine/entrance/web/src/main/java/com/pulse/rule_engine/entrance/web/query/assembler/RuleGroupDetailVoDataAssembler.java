package com.pulse.rule_engine.entrance.web.query.assembler;

import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.rule_engine.entrance.web.vo.RuleForDetailVo;
import com.pulse.rule_engine.entrance.web.vo.RuleGroupDetailVo;
import com.pulse.rule_engine.entrance.web.vo.RuleOrganizationBaseVo;
import com.pulse.rule_engine.entrance.web.vo.RuleRefCategorySimpleVo;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.service.RuleGroupDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** RuleGroupDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "179ccc58-0317-3f78-a403-efeae0d0dea5")
public class RuleGroupDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailBaseDtoService ruleGroupDetailBaseDtoService;

    /** 批量自定义组装RuleGroupDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "68048e92-ab17-38a2-b078-c31752e83c99")
    public void assembleDataCustomized(List<RuleGroupDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 组装rule数据 */
    @AutoGenerated(locked = true, uuid = "9c93996f-48f6-3b95-bffb-e48cf7e716fc")
    private void assembleRuleData(
            RuleGroupDetailVoDataAssembler.RuleGroupDetailVoDataHolder dataHolder) {
        Map<String, Pair<CategoryBaseDto, RuleRefCategorySimpleVo>> rule2RuleCategory =
                dataHolder.rule2RuleCategory.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.rule2RuleCategory.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, List<Pair<RuleOrganizationBaseDto, RuleOrganizationBaseVo>>>
                rule2RuleOrganizationList =
                        dataHolder.rule2RuleOrganizationList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getRuleId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .rule2RuleOrganizationList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        for (Map.Entry<RuleBaseDto, RuleForDetailVo> rule : dataHolder.rule.entrySet()) {
            RuleBaseDto baseDto = rule.getKey();
            RuleForDetailVo vo = rule.getValue();
            vo.setRuleCategory(
                    Optional.ofNullable(rule2RuleCategory.get(baseDto.getRuleCategoryId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setRuleOrganizationList(
                    Optional.ofNullable(rule2RuleOrganizationList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }
    }

    /** 组装RuleGroupDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "c9e5db9b-7433-3bfe-8c70-f2488fae174a")
    public void assembleData(
            Map<String, RuleGroupDetailVo> voMap,
            RuleGroupDetailVoDataAssembler.RuleGroupDetailVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<RuleGroupDetailBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<RuleBaseDto, RuleForDetailVo>> rule =
                dataHolder.rule.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.rule.get(dto)),
                                        (o1, o2) -> o1));

        for (RuleGroupDetailBaseDto baseDto : baseDtoList) {
            RuleGroupDetailVo vo = voMap.get(baseDto.getId());
            vo.setRule(
                    Optional.ofNullable(rule.get(baseDto.getRuleId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleRuleData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class RuleGroupDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<RuleGroupDetailBaseDto> rootBaseDtoList;

        /** 持有字段rule的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<RuleBaseDto, RuleForDetailVo> rule;

        /** 持有字段rule.ruleCategory的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<CategoryBaseDto, RuleRefCategorySimpleVo> rule2RuleCategory;

        /** 持有字段rule.ruleOrganizationList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<RuleOrganizationBaseDto, RuleOrganizationBaseVo> rule2RuleOrganizationList;
    }
}
