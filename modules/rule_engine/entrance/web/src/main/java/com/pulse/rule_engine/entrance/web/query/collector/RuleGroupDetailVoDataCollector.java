package com.pulse.rule_engine.entrance.web.query.collector;

import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.rule_engine.entrance.web.converter.RuleForDetailVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleGroupDetailVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleOrganizationBaseVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleRefCategorySimpleVoConverter;
import com.pulse.rule_engine.entrance.web.query.assembler.RuleGroupDetailVoDataAssembler.RuleGroupDetailVoDataHolder;
import com.pulse.rule_engine.entrance.web.vo.RuleForDetailVo;
import com.pulse.rule_engine.entrance.web.vo.RuleOrganizationBaseVo;
import com.pulse.rule_engine.entrance.web.vo.RuleRefCategorySimpleVo;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.manager.facade.dictionary_basic.CategoryBaseDtoServiceInRuleEngineRpcAdapter;
import com.pulse.rule_engine.service.RuleBaseDtoService;
import com.pulse.rule_engine.service.RuleGroupDetailBaseDtoService;
import com.pulse.rule_engine.service.RuleOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装RuleGroupDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "31325efe-6841-3a4a-9804-308195e16c31")
public class RuleGroupDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private CategoryBaseDtoServiceInRuleEngineRpcAdapter
            categoryBaseDtoServiceInRuleEngineRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoService ruleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleForDetailVoConverter ruleForDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailBaseDtoService ruleGroupDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailVoConverter ruleGroupDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupDetailVoDataCollector ruleGroupDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private RuleOrganizationBaseDtoService ruleOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleOrganizationBaseVoConverter ruleOrganizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleRefCategorySimpleVoConverter ruleRefCategorySimpleVoConverter;

    /** 获取RuleGroupDetailBaseDto数据填充RuleGroupDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "5370283d-1902-329a-be3c-a6bfabad2daf")
    public void collectDataWithDtoData(
            List<RuleGroupDetailBaseDto> dtoList, RuleGroupDetailVoDataHolder dataHolder) {

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "666fa02a-5193-3e12-93cf-710b65925bee")
    private void fillDataWhenNecessary(RuleGroupDetailVoDataHolder dataHolder) {
        List<RuleGroupDetailBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.rule == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(RuleGroupDetailBaseDto::getRuleId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<RuleBaseDto> baseDtoList =
                    ruleBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(RuleBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, RuleBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(RuleBaseDto::getId, Function.identity()));
            Map<RuleBaseDto, RuleForDetailVo> dtoVoMap =
                    ruleForDetailVoConverter.convertToRuleForDetailVoMap(baseDtoList);
            Map<RuleBaseDto, RuleForDetailVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.rule =
                    rootDtoList.stream()
                            .map(RuleGroupDetailBaseDto::getRuleId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.rule2RuleCategory == null) {
            Set<String> ids =
                    dataHolder.rule.keySet().stream()
                            .map(RuleBaseDto::getRuleCategoryId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<CategoryBaseDto> baseDtoList =
                    categoryBaseDtoServiceInRuleEngineRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(CategoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, CategoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(CategoryBaseDto::getId, Function.identity()));
            Map<CategoryBaseDto, RuleRefCategorySimpleVo> dtoVoMap =
                    ruleRefCategorySimpleVoConverter.convertToRuleRefCategorySimpleVoMap(
                            baseDtoList);
            Map<CategoryBaseDto, RuleRefCategorySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.rule2RuleCategory =
                    dataHolder.rule.keySet().stream()
                            .map(RuleBaseDto::getRuleCategoryId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.rule2RuleOrganizationList == null) {
            Set<String> ids =
                    dataHolder.rule.keySet().stream()
                            .map(RuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<RuleOrganizationBaseDto> baseDtoList =
                    ruleOrganizationBaseDtoService.getByRuleIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(RuleOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<RuleOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(RuleOrganizationBaseDto::getRuleId));
            Map<RuleOrganizationBaseDto, RuleOrganizationBaseVo> dtoVoMap =
                    ruleOrganizationBaseVoConverter.convertToRuleOrganizationBaseVoMap(baseDtoList);
            Map<RuleOrganizationBaseDto, RuleOrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.rule2RuleOrganizationList =
                    dataHolder.rule.keySet().stream()
                            .map(RuleBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "feacb16b-bae5-3114-ad5c-ff52b39dd5c8")
    public void collectDataDefault(RuleGroupDetailVoDataHolder dataHolder) {
        ruleGroupDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
