package com.pulse.rule_engine.entrance.web.vo;

import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "6efaabfb-3642-4af9-800a-d99fadbf52e1|VO|DEFINITION")
public class RuleGroupBaseVo {
    /** 编码 */
    @AutoGenerated(locked = true, uuid = "11b810d3-ad42-4437-ba78-5390d6b8073d")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "26cf2258-8332-4a2c-a658-aade1860e7a8")
    private Date createdAt;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "e26ce4e7-aa09-4ff8-a370-486c282d512f")
    private String createdBy;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "a1795ab9-40cb-48d6-81f1-960881ff3c31")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "36df0f56-17c4-4028-9e25-af8845d5c37b")
    private String id;

    /** 逻辑运算 逻辑运算符（AND、OR等） */
    @AutoGenerated(locked = true, uuid = "1cc3a8d5-d0fc-46bf-a5ac-3b47d6be61f4")
    private RuleLogicOperatorEnum logicOperator;

    /** 名字 */
    @AutoGenerated(locked = true, uuid = "bbf7c6b7-5c75-48b2-8347-d573cac0cbab")
    private String name;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "91a4b714-aa9b-4e70-9e34-b4f596c29173")
    private RuleStatusEnum status;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "c1bb07d9-647e-4397-a4d6-acc7150f2f10")
    private Date updatedAt;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "7875b626-6685-4dac-8a6f-5dac7fa65bb2")
    private String updatedBy;
}
