package com.pulse.rule_engine.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "2ed6b69d-b034-4167-aa53-1e348742dcda|VO|DEFINITION")
public class RuleGroupDetailVo {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9c096972-f209-4641-93f8-3f998a507f1e")
    private Date createdAt;

    /** 分组ID */
    @AutoGenerated(locked = true, uuid = "65d0d803-a328-4f1f-92c7-fe48d3e67195")
    private String groupId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "936d417f-bad9-4dae-b4c2-25b89472291b")
    private String id;

    /** 规则 */
    @Valid
    @AutoGenerated(locked = true, uuid = "18d1a79b-7ad3-463e-a1d5-cf509322ec96")
    private RuleForDetailVo rule;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ddf8d09b-a71b-43c1-a7d3-a8487eaf99d6")
    private Date updatedAt;
}
