package com.pulse.rule_engine.manager.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.bo.base.BaseRuleBO;
import com.pulse.rule_engine.manager.facade.dictionary_basic.CategoryBaseDtoServiceInRuleEngineRpcAdapter;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import javax.persistence.Entity;
import javax.persistence.Table;

@Slf4j
@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE rule  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ? and lock_version = ?")
@Table(name = "rule")
@Entity
@AutoGenerated(locked = false, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|DEFINITION")
public class RuleBO extends BaseRuleBO {

    /** 分类基础数据服务适配器 */
    @Autowired private CategoryBaseDtoServiceInRuleEngineRpcAdapter categoryBaseDtoServiceAdapter;

    /** 缓存是否为新增规则的判断结果，避免重复数据库查询 */
    private Boolean isNewRuleCache;

    /** 缓存原始规则对象，避免重复数据库查询 */
    private RuleBO originalRuleCache;

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {
        log.debug("开始执行规则聚合校验，规则ID: {}, 规则编码: {}", getId(), getCode());

        // 校验规则编码唯一性（仅在规则编码不为空时校验）
        validateRuleCodeUniqueness();

        // 校验规则状态业务逻辑（仅在状态不为空时校验）
        validateRuleStatus();

        // 校验当前版本号逻辑（仅在当前版本号不为空时校验）
        validateCurrentVersion();

        // 校验规则分类ID有效性（仅在规则分类ID不为空时校验）
        validateRuleCategoryId();

        // 校验规则版本聚合（仅在规则版本集合不为空时校验）
        validateRuleVersionAggregate();

        // 校验规则组织聚合（仅在规则组织集合不为空时校验）
        validateRuleOrganizationAggregate();

        log.debug("规则聚合校验完成");
    }

    /**
     * 校验规则编码唯一性
     *
     * <p>仅在规则编码不为空时进行校验，确保规则编码在系统中的唯一性 校验逻辑：排除当前对象本身，检查是否存在相同编码的其他规则
     */
    private void validateRuleCodeUniqueness() {
        if (StrUtil.isBlank(getCode())) {
            log.debug("规则编码为空，跳过编码唯一性校验");
            return;
        }

        executeWithExceptionHandling(
                "规则编码唯一性校验",
                () -> {
                    // 根据编码查询规则，排除当前对象本身
                    RuleBO existingRule = RuleBO.getByCode(getCode());
                    if (existingRule != null && !existingRule.getId().equals(getId())) {
                        String errorMessage = String.format("规则编码【%s】已存在，请使用其他编码", getCode());
                        log.warn("规则编码唯一性校验失败: 编码={}, 已存在规则ID={}", getCode(), existingRule.getId());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }
                    log.debug("规则编码唯一性校验通过: {}", getCode());
                });
    }

    /**
     * 校验规则状态业务逻辑
     *
     * <p>仅在状态不为空时进行校验，确保规则状态变更符合业务规则 校验逻辑：
     *
     * <ul>
     *   <li>草稿状态：可以变更为测试、生效状态
     *   <li>测试状态：可以变更为生效、已下线状态
     *   <li>生效状态：只能变更为已下线状态
     *   <li>已下线状态：不能变更为其他状态
     * </ul>
     */
    private void validateRuleStatus() {
        if (getStatus() == null) {
            log.debug("规则状态为空，跳过状态业务逻辑校验");
            return;
        }

        executeWithExceptionHandling(
                "规则状态业务逻辑校验",
                () -> {
                    // 判断是否为新增规则
                    boolean isNewRule = isNewRule();

                    if (isNewRule) {
                        // 如果是新增规则，状态只能是草稿
                        if (getStatus() != RuleStatusEnum.DRAFT) {
                            String errorMessage = "新增规则的状态只能是草稿状态";
                            log.warn("新增规则状态校验失败: 当前状态={}", getStatus());
                            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                        }
                        log.debug("新增规则状态校验通过: {}", getStatus());
                        return;
                    }

                    // 对于更新规则，需要校验状态变更的合理性
                    validateRuleStatusTransition();
                    log.debug("规则状态业务逻辑校验通过: {}", getStatus());
                });
    }

    /**
     * 校验规则状态变更的合理性
     *
     * <p>根据当前状态和目标状态，校验状态变更是否符合业务规则
     */
    private void validateRuleStatusTransition() {
        // 获取原始规则对象（使用缓存优化）
        RuleBO originalRule = getOriginalRule();
        if (originalRule == null) {
            log.debug("未找到原始规则记录，跳过状态变更校验");
            return;
        }

        RuleStatusEnum originalStatus = originalRule.getStatus();
        RuleStatusEnum currentStatus = getStatus();

        // 如果状态没有变更，跳过校验
        if (originalStatus == currentStatus) {
            log.debug("规则状态未变更，跳过状态变更校验");
            return;
        }

        // 校验状态变更的合理性
        boolean isValidTransition = isValidStatusTransition(originalStatus, currentStatus);
        if (!isValidTransition) {
            String errorMessage =
                    String.format("规则状态不能从【%s】变更为【%s】", originalStatus, currentStatus);
            log.warn("规则状态变更校验失败: 原状态={}, 目标状态={}", originalStatus, currentStatus);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        log.debug("规则状态变更校验通过: {} -> {}", originalStatus, currentStatus);
    }

    /**
     * 判断状态变更是否有效
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return true表示变更有效，false表示变更无效
     */
    private boolean isValidStatusTransition(RuleStatusEnum fromStatus, RuleStatusEnum toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        switch (fromStatus) {
            case DRAFT:
                // 草稿状态可以变更为测试、生效状态
                return toStatus == RuleStatusEnum.TEST || toStatus == RuleStatusEnum.EFFECTIVE;
            case TEST:
                // 测试状态可以变更为生效、已下线状态
                return toStatus == RuleStatusEnum.EFFECTIVE || toStatus == RuleStatusEnum.OFFLINE;
            case EFFECTIVE:
                // 生效状态只能变更为已下线状态
                return toStatus == RuleStatusEnum.OFFLINE;
            case OFFLINE:
                // 已下线状态不能变更为其他状态
                return false;
            default:
                return false;
        }
    }

    /**
     * 校验当前版本号逻辑
     *
     * <p>仅在当前版本号不为空时进行校验，确保当前版本号的合理性
     *
     * <p>基于RuleBOService业务逻辑的校验规则：
     *
     * <ul>
     *   <li>草稿状态：当前版本号可以为空（新增规则默认为草稿状态）
     *   <li>非草稿状态：当前版本号必须不为空且必须是有效的语义化版本号
     *   <li>当前版本号必须在规则版本列表中存在（如果规则版本列表不为空）
     *   <li>当前版本号对应的规则版本不能是已过期的版本
     *   <li>对于新增规则，如果没有规则版本但设置了当前版本号，则跳过存在性校验
     * </ul>
     */
    private void validateCurrentVersion() {
        if (StrUtil.isBlank(getCurrentVersion())) {
            // 草稿状态允许当前版本号为空
            if (getStatus() == RuleStatusEnum.DRAFT) {
                log.debug("草稿状态规则的当前版本号为空，跳过当前版本号校验");
                return;
            } else {
                String errorMessage = "非草稿状态的规则必须设置当前版本号";
                log.warn("当前版本号校验失败: 状态={}, 当前版本号为空", getStatus());
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
            }
        }

        executeWithExceptionHandling(
                "当前版本号校验",
                () -> {
                    // 校验版本号格式
                    validateCurrentVersionFormat();

                    // 判断是否为新增规则
                    boolean isNewRule = isNewRule();
                    Set<RuleVersionBO> ruleVersionBOSet = getRuleVersionBOSet();

                    // 对于新增规则，如果没有规则版本但设置了当前版本号，则跳过存在性校验
                    // 这种情况下，当前版本号将在Service层创建规则版本时自动设置
                    if (isNewRule && CollectionUtil.isEmpty(ruleVersionBOSet)) {
                        log.debug("新增规则且规则版本列表为空，跳过当前版本号存在性校验，版本号: {}", getCurrentVersion());
                    } else {
                        // 校验版本号是否在规则版本列表中存在
                        validateCurrentVersionExists();

                        // 校验当前版本号对应的规则版本是否有效
                        validateCurrentVersionValidity();
                    }

                    log.debug("当前版本号校验通过: {}", getCurrentVersion());
                });
    }

    /**
     * 校验规则分类ID有效性
     *
     * <p>仅在规则分类ID不为空时进行校验，确保规则分类ID的有效性
     *
     * <p>校验逻辑：
     *
     * <ul>
     *   <li>检查规则分类ID是否存在
     *   <li>检查分类是否启用（enableFlag为true）
     * </ul>
     */
    private void validateRuleCategoryId() {
        if (StrUtil.isBlank(getRuleCategoryId())) {
            log.debug("规则分类ID为空，跳过规则分类ID有效性校验");
            return;
        }

        executeWithExceptionHandling(
                "规则分类ID有效性校验",
                () -> {
                    // 调用分类基础数据服务查询分类信息
                    List<CategoryBaseDto> categoryList =
                            categoryBaseDtoServiceAdapter.getByIds(
                                    Collections.singletonList(getRuleCategoryId()));

                    // 检查分类是否存在
                    if (CollectionUtil.isEmpty(categoryList)) {
                        String errorMessage = String.format("规则分类ID【%s】不存在", getRuleCategoryId());
                        log.warn("规则分类ID有效性校验失败: {}", errorMessage);
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    CategoryBaseDto category = categoryList.get(0);

                    // 检查分类是否启用
                    if (category.getEnableFlag() == null || !category.getEnableFlag()) {
                        String errorMessage =
                                String.format(
                                        "规则分类【%s】已禁用，不能使用",
                                        StrUtil.isNotBlank(category.getName())
                                                ? category.getName()
                                                : getRuleCategoryId());
                        log.warn(
                                "规则分类ID有效性校验失败: 分类ID={}, 启用状态={}",
                                getRuleCategoryId(),
                                category.getEnableFlag());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    log.debug(
                            "规则分类ID有效性校验通过: 分类ID={}, 分类名称={}",
                            getRuleCategoryId(),
                            category.getName());
                });
    }

    /**
     * 校验规则版本聚合
     *
     * <p>仅在规则版本集合不为空时进行校验，确保规则版本聚合的合理性
     *
     * <p>基于RuleBOService业务逻辑的校验规则：
     *
     * <ul>
     *   <li>规则版本只能新增，不能更新删除
     *   <li>限制只能新增一个规则版本（与Service层业务逻辑保持一致）
     *   <li>校验版本号唯一性，避免重复版本号
     *   <li>校验规则版本数量限制，防止版本过多
     *   <li>避免重复校验，规则版本的详细校验已在RuleVersionBO.validate()中实现
     * </ul>
     */
    private void validateRuleVersionAggregate() {
        Set<RuleVersionBO> ruleVersionBOSet = getRuleVersionBOSet();
        if (CollectionUtil.isEmpty(ruleVersionBOSet)) {
            log.debug("规则版本集合为空，跳过规则版本聚合校验");
            return;
        }

        executeWithExceptionHandling(
                "规则版本聚合校验",
                () -> {
                    // 判断是否为新增规则
                    boolean isNewRule = isNewRule();

                    // 基于Service层业务逻辑：限制只能新增一个规则版本
                    // 对于新增规则，允许一次性创建多个版本（初始化场景）
                    // 对于更新规则，严格限制只能新增一个版本
                    if (!isNewRule && ruleVersionBOSet.size() > 1) {
                        String errorMessage =
                                "更新规则时只能新增一个规则版本，当前传入了" + ruleVersionBOSet.size() + "个版本";
                        log.warn(
                                "规则版本数量校验失败: 当前数量={}, 是否新增规则={}",
                                ruleVersionBOSet.size(),
                                isNewRule);
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    // 校验规则版本总数量限制
                    if (ruleVersionBOSet.size() > 100) {
                        String errorMessage = "规则版本数量不能超过100个";
                        log.warn("规则版本数量校验失败: 当前数量={}", ruleVersionBOSet.size());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    // 校验版本号唯一性
                    validateVersionNumberUniqueness(ruleVersionBOSet);

                    // 校验新增规则版本的业务逻辑
                    validateNewRuleVersions(ruleVersionBOSet, isNewRule);

                    log.debug(
                            "规则版本聚合校验通过，版本数量: {}, 是否新增规则: {}", ruleVersionBOSet.size(), isNewRule);
                });
    }

    /**
     * 校验规则组织聚合
     *
     * <p>仅在规则组织集合不为空时进行校验，确保规则组织聚合的合理性 校验逻辑：避免重复校验，因为规则组织的详细校验已在RuleOrganizationBO.validate()中实现
     */
    private void validateRuleOrganizationAggregate() {
        Set<RuleOrganizationBO> ruleOrganizationBOSet = getRuleOrganizationBOSet();
        if (CollectionUtil.isEmpty(ruleOrganizationBOSet)) {
            log.debug("规则组织集合为空，跳过规则组织聚合校验");
            return;
        }

        executeWithExceptionHandling(
                "规则组织聚合校验",
                () -> {
                    // 校验规则组织数量限制
                    if (ruleOrganizationBOSet.size() > 50) {
                        String errorMessage = "规则组织关联数量不能超过50个";
                        log.warn("规则组织数量校验失败: 当前数量={}", ruleOrganizationBOSet.size());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    // 校验组织ID唯一性
                    validateOrganizationIdUniqueness(ruleOrganizationBOSet);

                    log.debug("规则组织聚合校验通过，组织数量: {}", ruleOrganizationBOSet.size());
                });
    }

    /**
     * 校验当前版本号格式
     *
     * <p>使用语义化版本工具类验证版本号格式
     */
    private void validateCurrentVersionFormat() {
        // 使用语义化版本工具类验证版本号格式
        boolean isValid =
                com.pulse.pulse.common.utils.SemanticVersionUtils.isValidVersion(
                        getCurrentVersion());
        if (!isValid) {
            String errorMessage = "当前版本号格式不正确，应符合语义化版本规范（如：1.0.0）: " + getCurrentVersion();
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
        log.debug("当前版本号格式校验通过: {}", getCurrentVersion());
    }

    /** 校验当前版本号是否在规则版本列表中存在 */
    private void validateCurrentVersionExists() {
        Set<RuleVersionBO> ruleVersionBOSet = getRuleVersionBOSet();
        if (CollectionUtil.isEmpty(ruleVersionBOSet)) {
            String errorMessage = "规则版本列表为空，但设置了当前版本号: " + getCurrentVersion();
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        boolean versionExists =
                ruleVersionBOSet.stream()
                        .anyMatch(
                                version -> getCurrentVersion().equals(version.getVersionNumber()));

        if (!versionExists) {
            String errorMessage = "当前版本号在规则版本列表中不存在: " + getCurrentVersion();
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /** 校验当前版本号对应的规则版本是否有效 */
    private void validateCurrentVersionValidity() {
        Set<RuleVersionBO> ruleVersionBOSet = getRuleVersionBOSet();
        RuleVersionBO currentVersionBO =
                ruleVersionBOSet.stream()
                        .filter(version -> getCurrentVersion().equals(version.getVersionNumber()))
                        .findFirst()
                        .orElse(null);

        if (currentVersionBO == null) {
            return; // 在validateCurrentVersionExists中已经校验过存在性
        }

        // 校验当前版本是否已过期
        if (currentVersionBO.getEffectiveEndTime() != null
                && currentVersionBO.getEffectiveEndTime().before(new java.util.Date())) {
            String errorMessage = "当前版本号对应的规则版本已过期，不能设置为当前版本: " + getCurrentVersion();
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /**
     * 校验版本号唯一性
     *
     * @param ruleVersionBOSet 规则版本集合
     */
    private void validateVersionNumberUniqueness(Set<RuleVersionBO> ruleVersionBOSet) {
        if (CollectionUtil.isEmpty(ruleVersionBOSet)) {
            return;
        }

        Set<String> versionNumbers = new HashSet<>();
        for (RuleVersionBO ruleVersionBO : ruleVersionBOSet) {
            String versionNumber = ruleVersionBO.getVersionNumber();
            if (StrUtil.isNotBlank(versionNumber)) {
                if (!versionNumbers.add(versionNumber)) {
                    String errorMessage = "规则版本号重复: " + versionNumber;
                    log.warn("版本号唯一性校验失败: {}", errorMessage);
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                }
            }
        }
        log.debug("版本号唯一性校验通过，共校验{}个版本", versionNumbers.size());
    }

    /**
     * 校验组织ID唯一性
     *
     * @param ruleOrganizationBOSet 规则组织集合
     */
    private void validateOrganizationIdUniqueness(Set<RuleOrganizationBO> ruleOrganizationBOSet) {
        if (CollectionUtil.isEmpty(ruleOrganizationBOSet)) {
            return;
        }

        Set<String> organizationIds = new HashSet<>();
        for (RuleOrganizationBO ruleOrganizationBO : ruleOrganizationBOSet) {
            String organizationId = ruleOrganizationBO.getOrganizationId();
            if (StrUtil.isNotBlank(organizationId)) {
                if (!organizationIds.add(organizationId)) {
                    String errorMessage = "规则组织ID重复: " + organizationId;
                    log.warn("组织ID唯一性校验失败: {}", errorMessage);
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                }
            }
        }
        log.debug("组织ID唯一性校验通过，共校验{}个组织", organizationIds.size());
    }

    /**
     * 校验新增规则版本的业务逻辑
     *
     * <p>基于RuleBOService业务逻辑，校验新增规则版本的合理性
     *
     * @param ruleVersionBOSet 规则版本集合
     * @param isNewRule 是否为新增规则
     */
    private void validateNewRuleVersions(Set<RuleVersionBO> ruleVersionBOSet, boolean isNewRule) {
        if (CollectionUtil.isEmpty(ruleVersionBOSet)) {
            return;
        }

        // 对于新增规则版本，校验版本号是否由系统自动生成
        // 在Service层，版本号会根据最新版本和变更类型自动生成
        for (RuleVersionBO ruleVersionBO : ruleVersionBOSet) {
            // 校验规则版本的基本属性
            validateRuleVersionBasicProperties(ruleVersionBO);

            // 校验规则版本的时间逻辑
            validateRuleVersionTimeLogic(ruleVersionBO);
        }

        log.debug("新增规则版本业务逻辑校验通过，版本数量: {}", ruleVersionBOSet.size());
    }

    /**
     * 校验规则版本的基本属性
     *
     * @param ruleVersionBO 规则版本BO
     */
    private void validateRuleVersionBasicProperties(RuleVersionBO ruleVersionBO) {
        if (ruleVersionBO == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则版本不能为空");
        }

        // 校验DRL内容不能为空（业务必需）
        if (StrUtil.isBlank(ruleVersionBO.getDrlContent())) {
            String errorMessage = "规则版本的DRL内容不能为空";
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        // 校验生效开始时间不能为空（业务必需）
        if (ruleVersionBO.getEffectiveStartTime() == null) {
            String errorMessage = "规则版本的生效开始时间不能为空";
            log.warn(errorMessage);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /**
     * 校验规则版本的时间逻辑
     *
     * @param ruleVersionBO 规则版本BO
     */
    private void validateRuleVersionTimeLogic(RuleVersionBO ruleVersionBO) {
        Date effectiveStartTime = ruleVersionBO.getEffectiveStartTime();
        Date effectiveEndTime = ruleVersionBO.getEffectiveEndTime();

        // 如果设置了结束时间，校验时间范围的合理性
        if (effectiveEndTime != null) {
            // 结束时间不能早于开始时间
            if (effectiveEndTime.before(effectiveStartTime)) {
                String errorMessage = "规则版本的生效结束时间不能早于开始时间";
                log.warn("时间范围校验失败: 开始时间={}, 结束时间={}", effectiveStartTime, effectiveEndTime);
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
            }

            // 结束时间不能早于当前时间（避免创建已过期的版本）
            Date currentTime = new Date();
            if (effectiveEndTime.before(currentTime)) {
                String errorMessage = "规则版本的生效结束时间不能早于当前时间";
                log.warn("结束时间校验失败: 结束时间={}, 当前时间={}", effectiveEndTime, currentTime);
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
            }
        }
    }

    /**
     * 通用异常处理方法
     *
     * @param operation 操作名称
     * @param action 执行的操作
     */
    private void executeWithExceptionHandling(String operation, Runnable action) {
        try {
            action.run();
        } catch (Exception e) {
            if (e instanceof IgnoredException) {
                throw e;
            }
            String errorMessage = operation + "失败: " + e.getMessage();
            log.error(errorMessage, e);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }
    }

    /**
     * 判断是否为新增规则（优化版本，使用缓存避免重复查询）
     *
     * @return true表示新增规则，false表示更新规则
     */
    private boolean isNewRule() {
        // 使用缓存避免重复计算
        if (isNewRuleCache != null) {
            return isNewRuleCache;
        }

        if (StrUtil.isBlank(getId())) {
            log.debug("规则ID为空，判定为新增规则");
            isNewRuleCache = true;
            return true;
        }

        try {
            // 通过查询数据库来判断该ID的记录是否已存在
            RuleBO existingRule = RuleBO.getById(getId());
            boolean isNew = (existingRule == null);
            log.debug(
                    "规则ID: {}, 数据库中是否存在: {}, 判定为: {}",
                    getId(),
                    (existingRule != null),
                    isNew ? "新增规则" : "更新规则");

            // 缓存结果和原始规则对象
            isNewRuleCache = isNew;
            if (!isNew) {
                originalRuleCache = existingRule;
            }
            return isNew;
        } catch (Exception e) {
            // 查询异常时，为了安全起见，假设是更新操作
            log.warn("查询规则是否存在时发生异常，ID: {}, 异常信息: {}, 默认判定为更新规则", getId(), e.getMessage());
            isNewRuleCache = false;
            return false;
        }
    }

    /**
     * 获取原始规则对象（优化版本，使用缓存避免重复查询）
     *
     * @return 原始规则对象，如果是新增规则则返回null
     */
    private RuleBO getOriginalRule() {
        // 如果已经缓存了原始规则对象，直接返回
        if (originalRuleCache != null) {
            return originalRuleCache;
        }

        // 如果是新增规则，返回null
        if (isNewRule()) {
            return null;
        }

        // 查询并缓存原始规则对象
        try {
            originalRuleCache = RuleBO.getById(getId());
            return originalRuleCache;
        } catch (Exception e) {
            log.warn("查询原始规则对象时发生异常，ID: {}, 异常信息: {}", getId(), e.getMessage());
            return null;
        }
    }
}
