package com.pulse.rule_engine.manager.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.bo.base.BaseRuleGroupBO;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.hibernate.annotations.DynamicInsert;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "rule_group")
@Entity
@AutoGenerated(locked = false, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|DEFINITION")
public class RuleGroupBO extends BaseRuleGroupBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
