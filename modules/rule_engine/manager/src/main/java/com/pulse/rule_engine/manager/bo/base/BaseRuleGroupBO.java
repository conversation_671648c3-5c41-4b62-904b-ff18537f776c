package com.pulse.rule_engine.manager.bo.base;

import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.bo.RuleGroupBO;
import com.pulse.rule_engine.manager.bo.RuleGroupDetailBO;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "rule_group")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "6a9c3349-d4ad-3d24-9fd2-716e9f956828")
public abstract class BaseRuleGroupBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 编码 */
    @Column(name = "code")
    @AutoGenerated(locked = true, uuid = "2227c025-342d-47e5-ae3c-6c17ff07c6d6")
    private String code;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "ddd6debe-06a7-5206-b1d3-22f4da17593a")
    private Date createdAt;

    /** 创建人 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "676bcd1e-8b44-4953-9e81-2e862a73e4f6")
    private String createdBy;

    /** 描述 */
    @Column(name = "description")
    @AutoGenerated(locked = true, uuid = "8c098c91-d5e8-4ea6-b854-bd09e4fe44ae")
    private String description;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "64e9781c-3c03-481a-a4c3-b3e975dd81fc")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "11517a1d-a350-4afe-a24c-0dee4be4306b")
    @Version
    private Long lockVersion;

    /** 逻辑运算 逻辑运算符（AND、OR等） */
    @Column(name = "logic_operator")
    @AutoGenerated(locked = true, uuid = "b8853cd1-0141-458d-84a3-cd743a3699ea")
    @Enumerated(EnumType.STRING)
    private RuleLogicOperatorEnum logicOperator;

    /** 名字 */
    @Column(name = "name")
    @AutoGenerated(locked = true, uuid = "22ef17e6-c389-460e-b6b4-68cbaa857d56")
    private String name;

    @JoinColumn(name = "group_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<RuleGroupDetailBO> ruleGroupDetailBOSet = new HashSet<>();

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @Column(name = "status")
    @AutoGenerated(locked = true, uuid = "43b1de93-3377-43b6-a2c7-87fbfc8b972f")
    @Enumerated(EnumType.STRING)
    private RuleStatusEnum status;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "f9abd6de-0d1b-5e7a-9d20-679fa721b1f8")
    private Date updatedAt;

    /** 更新人 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "3d79254f-35d9-4d01-b45f-8ca82d954262")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public RuleGroup convertToRuleGroup() {
        RuleGroup entity = new RuleGroup();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "name",
                "code",
                "description",
                "logicOperator",
                "status",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static RuleGroupBO getByCode(String code) {
        Session session = TransactionalSessionFactory.getSession();
        RuleGroupBO ruleGroup =
                (RuleGroupBO)
                        session.createQuery("from RuleGroupBO where " + "code =: code ")
                                .setParameter("code", code)
                                .uniqueResult();
        return ruleGroup;
    }

    @AutoGenerated(locked = true)
    public static RuleGroupBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        RuleGroupBO ruleGroup =
                (RuleGroupBO)
                        session.createQuery("from RuleGroupBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return ruleGroup;
    }

    @AutoGenerated(locked = true)
    public String getCode() {
        return this.code;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public String getDescription() {
        return this.description;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public RuleLogicOperatorEnum getLogicOperator() {
        return this.logicOperator;
    }

    @AutoGenerated(locked = true)
    public String getName() {
        return this.name;
    }

    @AutoGenerated(locked = true)
    public Set<RuleGroupDetailBO> getRuleGroupDetailBOSet() {
        return this.ruleGroupDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public RuleStatusEnum getStatus() {
        return this.status;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setCode(String code) {
        this.code = code;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setDescription(String description) {
        this.description = description;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setId(String id) {
        this.id = id;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setLogicOperator(RuleLogicOperatorEnum logicOperator) {
        this.logicOperator = logicOperator;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setName(String name) {
        this.name = name;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    private void setRuleGroupDetailBOSet(Set<RuleGroupDetailBO> ruleGroupDetailBOSet) {
        this.ruleGroupDetailBOSet = ruleGroupDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setStatus(RuleStatusEnum status) {
        this.status = status;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleGroupBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleGroupBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (RuleGroupBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
