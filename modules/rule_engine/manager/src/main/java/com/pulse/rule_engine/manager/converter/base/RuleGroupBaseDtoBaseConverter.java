package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "aa51881e-7090-432a-b4a4-846278819a34|DTO|BASE_CONVERTER")
public class RuleGroupBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleGroupBaseDto convertFromRuleGroupToRuleGroupBaseDto(RuleGroup ruleGroup) {
        return convertFromRuleGroupToRuleGroupBaseDto(List.of(ruleGroup)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleGroupBaseDto> convertFromRuleGroupToRuleGroupBaseDto(
            List<RuleGroup> ruleGroupList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleGroupList)) {
            return new ArrayList<>();
        }
        List<RuleGroupBaseDto> ruleGroupBaseDtoList = new ArrayList<>();
        for (RuleGroup ruleGroup : ruleGroupList) {
            if (ruleGroup == null) {
                continue;
            }
            RuleGroupBaseDto ruleGroupBaseDto = new RuleGroupBaseDto();
            ruleGroupBaseDto.setId(ruleGroup.getId());
            ruleGroupBaseDto.setName(ruleGroup.getName());
            ruleGroupBaseDto.setCode(ruleGroup.getCode());
            ruleGroupBaseDto.setDescription(ruleGroup.getDescription());
            ruleGroupBaseDto.setLogicOperator(ruleGroup.getLogicOperator());
            ruleGroupBaseDto.setStatus(ruleGroup.getStatus());
            ruleGroupBaseDto.setCreatedBy(ruleGroup.getCreatedBy());
            ruleGroupBaseDto.setUpdatedBy(ruleGroup.getUpdatedBy());
            ruleGroupBaseDto.setLockVersion(ruleGroup.getLockVersion());
            ruleGroupBaseDto.setCreatedAt(ruleGroup.getCreatedAt());
            ruleGroupBaseDto.setUpdatedAt(ruleGroup.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleGroupBaseDtoList.add(ruleGroupBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleGroupBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
