package com.pulse.rule_engine.manager.dto;

import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "aa51881e-7090-432a-b4a4-846278819a34|DTO|DEFINITION")
public class RuleGroupBaseDto {
    /** 编码 */
    @AutoGenerated(locked = true, uuid = "a3d1b10a-c5b1-442e-b935-4c87ac35c988")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "42f1f66f-31b6-4a7c-853c-25898b7bd789")
    private Date createdAt;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "e12e02b1-3194-4265-8a39-99692bf7356d")
    private String createdBy;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "10818846-0ab8-4146-8869-bb2fcc6b2bff")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "d7ce0b93-f512-41fa-bad0-720be847196f")
    private String id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "ad6a0b70-5825-43bb-85c0-dd82c0c731a2")
    private Long lockVersion;

    /** 逻辑运算 逻辑运算符（AND、OR等） */
    @AutoGenerated(locked = true, uuid = "f026180b-fb38-401b-a88c-3f78e3f34545")
    private RuleLogicOperatorEnum logicOperator;

    /** 名字 */
    @AutoGenerated(locked = true, uuid = "2c290efd-663b-446c-ab6a-09f46ed9cdb3")
    private String name;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "a0b0d2bc-65d4-4434-8351-c0536b7d0743")
    private RuleStatusEnum status;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "9804b51d-556a-404b-a39a-5f413fa4bcb4")
    private Date updatedAt;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "29423f9d-681d-4fde-8cbc-7a462c15c95e")
    private String updatedBy;
}
