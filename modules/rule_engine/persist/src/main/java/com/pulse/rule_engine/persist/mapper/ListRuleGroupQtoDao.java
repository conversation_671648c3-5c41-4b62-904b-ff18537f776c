package com.pulse.rule_engine.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.persist.qto.ListRuleGroupQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a|QTO|DAO")
public class ListRuleGroupQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询规则组列表 */
    @AutoGenerated(locked = false, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a-count")
    public Integer count(ListRuleGroupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(rule_group.id) FROM rule_group WHERE rule_group.code like #codeLike"
                    + " AND rule_group.name like #nameLike AND rule_group.status in #statusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getCodeLike() == null) {
            conditionToRemove.add("#codeLike");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#codeLike", "?")
                        .replace("#nameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#codeLike")) {
                sqlParams.add("%" + qto.getCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则组列表 */
    @AutoGenerated(locked = false, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a-query-all")
    public List<String> query(ListRuleGroupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_group.id FROM rule_group WHERE rule_group.code like #codeLike AND"
                    + " rule_group.name like #nameLike AND rule_group.status in #statusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getCodeLike() == null) {
            conditionToRemove.add("#codeLike");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#codeLike", "?")
                        .replace("#nameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#codeLike")) {
                sqlParams.add("%" + qto.getCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_group.created_at desc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则组列表 */
    @AutoGenerated(locked = false, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a-query-paginate")
    public List<String> queryPaged(ListRuleGroupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_group.id FROM rule_group WHERE rule_group.code like #codeLike AND"
                    + " rule_group.name like #nameLike AND rule_group.status in #statusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getCodeLike() == null) {
            conditionToRemove.add("#codeLike");
        }
        if (qto.getNameLike() == null) {
            conditionToRemove.add("#nameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#codeLike", "?")
                        .replace("#nameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#codeLike")) {
                sqlParams.add("%" + qto.getCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#nameLike")) {
                sqlParams.add("%" + qto.getNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_group.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
