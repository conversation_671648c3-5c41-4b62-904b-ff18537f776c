package com.pulse.rule_engine.persist.qto;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a|QTO|DEFINITION")
public class ListRuleGroupQto {
    /** 编码 rule_group.code */
    @AutoGenerated(locked = true, uuid = "099de6e1-a092-4bcc-885b-731e964797ab")
    private String codeLike;

    @AutoGenerated(locked = true, uuid = "73485e2e-9ca3-46e6-a0c3-e4a0ebc3b66e")
    private Integer from;

    /** 名字 rule_group.name */
    @AutoGenerated(locked = true, uuid = "9f0537d4-ba08-497d-903c-49c5f5780ff6")
    private String nameLike;

    @AutoGenerated(locked = true, uuid = "a9bfe921-606d-4dd1-8670-2f3b1b57a92b")
    private Integer size;

    /** 状态 rule_group.status */
    @Valid
    @AutoGenerated(locked = true, uuid = "598f439f-1e6d-4cf0-bce6-9578928eeec0")
    private List<RuleStatusEnum> statusIn;
}
