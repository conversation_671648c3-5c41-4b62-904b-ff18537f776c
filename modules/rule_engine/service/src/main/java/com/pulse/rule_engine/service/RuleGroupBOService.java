package com.pulse.rule_engine.service;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.pulse.rule_engine.service.base.BaseRuleGroupBOService;
import com.pulse.rule_engine.service.bto.DeleteRuleGroupBto;
import com.pulse.rule_engine.service.bto.MergeRuleGroupBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|SERVICE")
public class RuleGroupBOService extends BaseRuleGroupBOService {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseDtoService ruleGroupBaseDtoService;

    /** 保存规则组 */
    @PublicInterface(id = "e428fd20-7520-4430-b6a0-9ab2008b958d", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "77e04992-4fd3-46d6-b486-67a3566a5b36")
    public String mergeRuleGroup(@Valid @NotNull MergeRuleGroupBto mergeRuleGroupBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleGroupBaseDto ruleGroupBaseDto = null;
        if (mergeRuleGroupBto.getId() != null) {
            ruleGroupBaseDto = ruleGroupBaseDtoService.getById(mergeRuleGroupBto.getId());
        }
        MergeRuleGroupBoResult boResult = super.mergeRuleGroupBase(mergeRuleGroupBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeRuleGroupBto.RuleGroupDetailBto */
        {
            for (MergeRuleGroupBto.RuleGroupDetailBto bto :
                    boResult.<MergeRuleGroupBto.RuleGroupDetailBto>getBtoOfType(
                            MergeRuleGroupBto.RuleGroupDetailBto.class)) {
                UpdatedBto<MergeRuleGroupBto.RuleGroupDetailBto, RuleGroupDetail, RuleGroupDetailBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeRuleGroupBto.RuleGroupDetailBto, RuleGroupDetailBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    RuleGroupDetailBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    RuleGroupDetail entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    RuleGroupDetailBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<RuleGroupDetail> deletedEntityList =
                    boResult.getDeletedEntityList(RuleGroupDetail.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 MergeRuleGroupBto */
        {
            MergeRuleGroupBto bto =
                    boResult.<MergeRuleGroupBto>getBtoOfType(MergeRuleGroupBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeRuleGroupBto, RuleGroup, RuleGroupBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeRuleGroupBto, RuleGroupBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                RuleGroupBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                RuleGroup entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                RuleGroupBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 删除规则组 */
    @PublicInterface(id = "68b3b248-bcbc-4e28-a579-b5c3b6caa6c3", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b2969f08-974f-47df-87a3-94b5a87eaeac")
    public String deleteRuleGroup(@Valid @NotNull DeleteRuleGroupBto deleteRuleGroupBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleGroupBaseDto ruleGroupBaseDto =
                ruleGroupBaseDtoService.getById(deleteRuleGroupBto.getId());
        DeleteRuleGroupBoResult boResult = super.deleteRuleGroupBase(deleteRuleGroupBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteRuleGroupBto */
        {
            DeleteRuleGroupBto bto =
                    boResult.<DeleteRuleGroupBto>getBtoOfType(DeleteRuleGroupBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteRuleGroupBto, RuleGroup> deletedBto = boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
