package com.pulse.rule_engine.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.bo.RuleGroupBO;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.pulse.rule_engine.persist.dos.RuleGroupDetail;
import com.pulse.rule_engine.service.base.BaseRuleGroupBOService.DeleteRuleGroupBoResult;
import com.pulse.rule_engine.service.base.BaseRuleGroupBOService.MergeRuleGroupBoResult;
import com.pulse.rule_engine.service.bto.DeleteRuleGroupBto;
import com.pulse.rule_engine.service.bto.MergeRuleGroupBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "21d67aa3-cdd0-34e0-8d31-07c34f0ebe2e")
public class BaseRuleGroupBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private RuleGroupBO createMergeRuleGroupOnDuplicateUpdate(
            MergeRuleGroupBoResult boResult, MergeRuleGroupBto mergeRuleGroupBto) {
        RuleGroupBO ruleGroupBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeRuleGroupBto.getId() == null);
        if (!allNull && !found) {
            ruleGroupBO = RuleGroupBO.getById(mergeRuleGroupBto.getId());
            if (ruleGroupBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (ruleGroupBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleGroupBO.convertToRuleGroup());
                updatedBto.setBto(mergeRuleGroupBto);
                updatedBto.setBo(ruleGroupBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "name")) {
                    ruleGroupBO.setName(mergeRuleGroupBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "description")) {
                    ruleGroupBO.setDescription(mergeRuleGroupBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "logicOperator")) {
                    ruleGroupBO.setLogicOperator(mergeRuleGroupBto.getLogicOperator());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "createdBy")) {
                    ruleGroupBO.setCreatedBy(mergeRuleGroupBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "updatedBy")) {
                    ruleGroupBO.setUpdatedBy(mergeRuleGroupBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "status")) {
                    ruleGroupBO.setStatus(mergeRuleGroupBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "code")) {
                    ruleGroupBO.setCode(mergeRuleGroupBto.getCode());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleGroupBO.convertToRuleGroup());
                updatedBto.setBto(mergeRuleGroupBto);
                updatedBto.setBo(ruleGroupBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "name")) {
                    ruleGroupBO.setName(mergeRuleGroupBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "description")) {
                    ruleGroupBO.setDescription(mergeRuleGroupBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "logicOperator")) {
                    ruleGroupBO.setLogicOperator(mergeRuleGroupBto.getLogicOperator());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "createdBy")) {
                    ruleGroupBO.setCreatedBy(mergeRuleGroupBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "updatedBy")) {
                    ruleGroupBO.setUpdatedBy(mergeRuleGroupBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "status")) {
                    ruleGroupBO.setStatus(mergeRuleGroupBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                        "code")) {
                    ruleGroupBO.setCode(mergeRuleGroupBto.getCode());
                }
            }
        } else {
            ruleGroupBO = new RuleGroupBO();
            if (pkExist) {
                ruleGroupBO.setId(mergeRuleGroupBto.getId());
            } else {
                ruleGroupBO.setId(String.valueOf(this.idGenerator.allocateId("rule_group")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "name")) {
                ruleGroupBO.setName(mergeRuleGroupBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "description")) {
                ruleGroupBO.setDescription(mergeRuleGroupBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "logicOperator")) {
                ruleGroupBO.setLogicOperator(mergeRuleGroupBto.getLogicOperator());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "createdBy")) {
                ruleGroupBO.setCreatedBy(mergeRuleGroupBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "updatedBy")) {
                ruleGroupBO.setUpdatedBy(mergeRuleGroupBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "status")) {
                ruleGroupBO.setStatus(mergeRuleGroupBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "code")) {
                ruleGroupBO.setCode(mergeRuleGroupBto.getCode());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeRuleGroupBto);
            addedBto.setBo(ruleGroupBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return ruleGroupBO;
    }

    /** 创建对象:RuleGroupDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createRuleGroupDetailBtoOnDuplicateUpdate(
            BaseRuleGroupBOService.MergeRuleGroupBoResult boResult,
            MergeRuleGroupBto mergeRuleGroupBto,
            RuleGroupBO ruleGroupBO) {
        if (CollectionUtil.isEmpty(mergeRuleGroupBto.getRuleGroupDetailBtoList())) {
            mergeRuleGroupBto.setRuleGroupDetailBtoList(List.of());
        }
        ruleGroupBO
                .getRuleGroupDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeRuleGroupBto.getRuleGroupDetailBtoList().stream()
                                            .filter(
                                                    ruleGroupDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (ruleGroupDetailBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            ruleGroupDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToRuleGroupDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeRuleGroupBto.getRuleGroupDetailBtoList())) {
            for (MergeRuleGroupBto.RuleGroupDetailBto item :
                    mergeRuleGroupBto.getRuleGroupDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<RuleGroupDetailBO> any =
                        ruleGroupBO.getRuleGroupDetailBOSet().stream()
                                .filter(
                                        ruleGroupDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                ruleGroupDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        RuleGroupDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleGroupDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "ruleId")) {
                            bo.setRuleId(item.getRuleId());
                        }
                    } else {
                        RuleGroupDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleGroupDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "ruleId")) {
                            bo.setRuleId(item.getRuleId());
                        }
                    }
                } else {
                    RuleGroupDetailBO subBo = new RuleGroupDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "ruleId")) {
                        subBo.setRuleId(item.getRuleId());
                    }
                    subBo.setRuleGroupBO(ruleGroupBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("rule_group_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    ruleGroupBO.getRuleGroupDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 删除对象:deleteRuleGroup,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RuleGroupBO deleteDeleteRuleGroupOnMissThrowEx(
            BaseRuleGroupBOService.DeleteRuleGroupBoResult boResult,
            DeleteRuleGroupBto deleteRuleGroupBto) {
        RuleGroupBO ruleGroupBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteRuleGroupBto.getId() == null);
        if (!allNull && !found) {
            ruleGroupBO = RuleGroupBO.getById(deleteRuleGroupBto.getId());
            found = true;
        }
        if (ruleGroupBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(ruleGroupBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteRuleGroupBto);
            deletedBto.setEntity(ruleGroupBO.convertToRuleGroup());
            boResult.getDeletedBtoList().add(deletedBto);
            return ruleGroupBO;
        }
    }

    /** 删除规则组 */
    @AutoGenerated(locked = true)
    protected DeleteRuleGroupBoResult deleteRuleGroupBase(DeleteRuleGroupBto deleteRuleGroupBto) {
        if (deleteRuleGroupBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteRuleGroupBoResult boResult = new DeleteRuleGroupBoResult();
        RuleGroupBO ruleGroupBO = deleteDeleteRuleGroupOnMissThrowEx(boResult, deleteRuleGroupBto);
        boResult.setRootBo(ruleGroupBO);
        return boResult;
    }

    /** 保存规则组 */
    @AutoGenerated(locked = true)
    protected MergeRuleGroupBoResult mergeRuleGroupBase(MergeRuleGroupBto mergeRuleGroupBto) {
        if (mergeRuleGroupBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRuleGroupBoResult boResult = new MergeRuleGroupBoResult();
        RuleGroupBO ruleGroupBO =
                createMergeRuleGroupOnDuplicateUpdate(boResult, mergeRuleGroupBto);
        if (ruleGroupBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleGroupBto, "__$validPropertySet"),
                    "ruleGroupDetailBtoList")) {
                createRuleGroupDetailBtoOnDuplicateUpdate(boResult, mergeRuleGroupBto, ruleGroupBO);
            }
        }
        boResult.setRootBo(ruleGroupBO);
        return boResult;
    }

    public static class MergeRuleGroupBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleGroupBO getRootBo() {
            return (RuleGroupBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleGroupBto.RuleGroupDetailBto, RuleGroupDetailBO> getCreatedBto(
                MergeRuleGroupBto.RuleGroupDetailBto ruleGroupDetailBto) {
            return this.getAddedResult(ruleGroupDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleGroupBto, RuleGroupBO> getCreatedBto(
                MergeRuleGroupBto mergeRuleGroupBto) {
            return this.getAddedResult(mergeRuleGroupBto);
        }

        @AutoGenerated(locked = true)
        public RuleGroupDetail getDeleted_RuleGroupDetail() {
            return (RuleGroupDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleGroupDetail.class));
        }

        @AutoGenerated(locked = true)
        public RuleGroup getDeleted_RuleGroup() {
            return (RuleGroup) CollectionUtil.getFirst(this.getDeletedEntityList(RuleGroup.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleGroupBto.RuleGroupDetailBto, RuleGroupDetail, RuleGroupDetailBO>
                getUpdatedBto(MergeRuleGroupBto.RuleGroupDetailBto ruleGroupDetailBto) {
            return super.getUpdatedResult(ruleGroupDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleGroupBto, RuleGroup, RuleGroupBO> getUpdatedBto(
                MergeRuleGroupBto mergeRuleGroupBto) {
            return super.getUpdatedResult(mergeRuleGroupBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleGroupBto.RuleGroupDetailBto, RuleGroupDetailBO>
                getUnmodifiedBto(MergeRuleGroupBto.RuleGroupDetailBto ruleGroupDetailBto) {
            return super.getUnmodifiedResult(ruleGroupDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleGroupBto, RuleGroupBO> getUnmodifiedBto(
                MergeRuleGroupBto mergeRuleGroupBto) {
            return super.getUnmodifiedResult(mergeRuleGroupBto);
        }
    }

    public static class DeleteRuleGroupBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleGroupBO getRootBo() {
            return (RuleGroupBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteRuleGroupBto, RuleGroupBO> getCreatedBto(
                DeleteRuleGroupBto deleteRuleGroupBto) {
            return this.getAddedResult(deleteRuleGroupBto);
        }

        @AutoGenerated(locked = true)
        public RuleGroup getDeleted_RuleGroup() {
            return (RuleGroup) CollectionUtil.getFirst(this.getDeletedEntityList(RuleGroup.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteRuleGroupBto, RuleGroup, RuleGroupBO> getUpdatedBto(
                DeleteRuleGroupBto deleteRuleGroupBto) {
            return super.getUpdatedResult(deleteRuleGroupBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteRuleGroupBto, RuleGroupBO> getUnmodifiedBto(
                DeleteRuleGroupBto deleteRuleGroupBto) {
            return super.getUnmodifiedResult(deleteRuleGroupBto);
        }
    }
}
