package com.pulse.rule_engine.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> RuleGroup
 *
 * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "b2969f08-974f-47df-87a3-94b5a87eaeac|BTO|DEFINITION")
public class DeleteRuleGroupBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9f700c20-03db-48b9-8e8e-5c48d71b3dca")
    private String id;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
