package com.pulse.rule_engine.service.bto;

import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> RuleGroup
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "77e04992-4fd3-46d6-b486-67a3566a5b36|BTO|DEFINITION")
public class MergeRuleGroupBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 编码 */
    @AutoGenerated(locked = true, uuid = "1a8e2eaa-61b6-4954-b970-2ad469d00547")
    private String code;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "601ecf7c-d470-49ca-9a0e-8594e6133f5a")
    private String createdBy;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "45e94251-9c0f-4c29-9069-272d69701fca")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "84951690-d320-4700-8f45-1992ce5068a1")
    private String id;

    /** 逻辑运算 逻辑运算符（AND、OR等） */
    @AutoGenerated(locked = true, uuid = "4af4d805-048c-4934-9c6c-f5616393fa4d")
    private RuleLogicOperatorEnum logicOperator;

    /** 名字 */
    @AutoGenerated(locked = true, uuid = "73d36387-992b-49c2-9cbf-64472bd43a62")
    private String name;

    @Valid
    @AutoGenerated(locked = true, uuid = "930059fa-058f-4d04-9618-5cacdc0fcd8f")
    private List<MergeRuleGroupBto.RuleGroupDetailBto> ruleGroupDetailBtoList;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线"，默认为“草稿” */
    @AutoGenerated(locked = true, uuid = "b73352b3-97db-4a2c-857e-6b6879be217f")
    private RuleStatusEnum status;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "88cfaa75-8d04-4965-b51a-73f0151ebc59")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setLogicOperator(RuleLogicOperatorEnum logicOperator) {
        this.__$validPropertySet.add("logicOperator");
        this.logicOperator = logicOperator;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setRuleGroupDetailBtoList(
            List<MergeRuleGroupBto.RuleGroupDetailBto> ruleGroupDetailBtoList) {
        this.__$validPropertySet.add("ruleGroupDetailBtoList");
        this.ruleGroupDetailBtoList = ruleGroupDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setStatus(RuleStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    /**
     * <b>[源自]</b> RuleGroupDetail
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class RuleGroupDetailBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "3f20a383-58e9-466d-b00b-f70f60f2aef7")
        private String id;

        /** 规则ID */
        @AutoGenerated(locked = true, uuid = "2bd85a67-f240-4e5f-b894-07ebee6df10c")
        private String ruleId;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setRuleId(String ruleId) {
            this.__$validPropertySet.add("ruleId");
            this.ruleId = ruleId;
        }
    }
}
