package com.pulse.rule_engine.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.rule_engine.persist.mapper.ListRuleGroupQtoDao;
import com.pulse.rule_engine.persist.qto.ListRuleGroupQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a|QTO|SERVICE")
public class ListRuleGroupQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleGroupQtoDao listRuleGroupMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "6505f1e0-7d81-4c07-9bb7-d45c4fd1b53a-query-paged")
    public List<String> queryPaged(ListRuleGroupQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listRuleGroupMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListRuleGroupQto qto) {
        return listRuleGroupMapper.count(qto);
    }
}
