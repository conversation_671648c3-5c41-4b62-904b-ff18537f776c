package com.pulse.rule_engine.service.query;

import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.persist.qto.ListRuleGroupQto;
import com.pulse.rule_engine.service.RuleGroupBaseDtoService;
import com.pulse.rule_engine.service.index.entity.ListRuleGroupQtoService;
import com.pulse.rule_engine.service.query.assembler.RuleGroupBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** RuleGroupBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "f525b411-c2ee-37de-9fdd-17dfde364884")
public class RuleGroupBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleGroupQtoService listRuleGroupQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseDtoDataAssembler ruleGroupBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupBaseDtoService ruleGroupBaseDtoService;

    /** 根据ListRuleGroupQto查询RuleGroupBaseDto列表,分页 */
    @PublicInterface(id = "89c80ea1-8952-4dd0-b838-53b4d0945350", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "0a253eb0-7585-3e91-b5b8-fc2e150cd270")
    public VSQueryResult<RuleGroupBaseDto> listRuleGroupPaged(
            @Valid @NotNull ListRuleGroupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listRuleGroupQtoService.queryPaged(qto);
        List<RuleGroupBaseDto> dtoList = toDtoList(ids);
        ruleGroupBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listRuleGroupQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "79570af3-5bea-3212-a28a-133a10c139ea")
    private List<RuleGroupBaseDto> toDtoList(List<String> ids) {
        List<RuleGroupBaseDto> baseDtoList = ruleGroupBaseDtoService.getByIds(ids);
        Map<String, RuleGroupBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(Collectors.toMap(RuleGroupBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
