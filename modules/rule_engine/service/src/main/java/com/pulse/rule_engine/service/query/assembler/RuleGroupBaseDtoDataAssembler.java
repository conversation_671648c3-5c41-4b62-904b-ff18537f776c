package com.pulse.rule_engine.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** RuleGroupBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "fc4bacab-a20b-3118-bd87-ab366ab96a33")
public class RuleGroupBaseDtoDataAssembler {

    /** 批量自定义组装RuleGroupBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "9609e034-457f-3141-8e06-e80f87003e2d")
    public void assembleDataCustomized(List<RuleGroupBaseDto> dataList) {
        // 自定义数据组装

    }

    /** 组装RuleGroupBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "cf3f024b-ee90-37ab-9007-c19e24646cd7")
    public void assembleData(List<RuleGroupBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }
}
